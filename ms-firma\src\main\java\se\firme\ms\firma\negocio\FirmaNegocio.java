/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.firma.negocio;

import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;

import co.venko.ms.models.entity.AdmUsuario;

import java.io.File;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import org.springframework.core.env.Environment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import se.firme.commons.exception.FirmaException;
import se.firme.commons.firmese.dto.ArchivoFirmaResponseDTO;
import se.firme.commons.firmese.dto.DetalleSolicitudDTO;
import se.firme.commons.firmese.dto.DocumentoDTO;
import se.firme.commons.firmese.dto.FirmaRequestDTO;
import se.firme.commons.firmese.dto.FirmanteDTO;
import se.firme.commons.firmese.dto.MultipleFirmaDTO;
import se.firme.commons.firmese.dto.PersonaScanDTO;
import se.firme.commons.firmese.dto.PropietarioDTO;
import se.firme.commons.firmese.dto.SendFileEmailRequestDTO;
import se.firme.commons.firmese.dto.TokenFirmaDTO;
import se.firme.commons.firmese.dto.UserLoginDTO;
import se.firme.commons.firmese.enumerados.EEstadoArchivoFirma;
import se.firme.commons.firmese.service.EmailService;
import se.firme.commons.firmese.service.EmailTemplateService;
import se.firme.commons.firmese.util.Parameters;
import se.firme.commons.firmese.util.Utilities;
import se.firme.commons.models.projection.IArchivoFirma;
import se.firme.commons.models.projection.IDocumentosEstado;
import se.firme.ms.datos.models.dto.FirmaOrdenRequestDTO;
import se.firme.ms.datos.models.dto.SolicitudFirmaUnificadaDTO;
import se.firme.ms.datos.models.entity.*;
import se.firme.ms.firma.rest.client.UsuarioVMSClient;
import se.firme.ms.firma.servicio.MensajeroService;
import se.firme.ms.firma.servicio.SenderEmailService;
import se.firme.ms.models.service.*;
import se.firme.ms.models.service.helper.ArchivoFirmaHelper;
import se.firme.ms.models.service.helper.UsuarioHelper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import javax.servlet.http.HttpServletRequest;

import se.firme.commons.firmese.dto.EndpointDTO;

/**
 * <AUTHOR>
 */
@Service
public class FirmaNegocio {

    @Autowired
    private ArchivoFirmaServiceImpl archivoFirmaService;

    @Autowired
    private UsuarioServiceImpl usuarioService;

    @Autowired
    private TokenServiceImpl tokenServiceImpl;
    @Autowired
    private Environment env;
    @Autowired
    private ProcesoFirmaServiceImpl procesoFirmaService;
    @Autowired
    private FirmaArchivoUsuarioServiceImpl firmaArchivoService;
    @Autowired
    private MensajeroService mensajeroClient;
    @Autowired
    private UsuarioVMSClient usuarioVMSClient;
    @Autowired
    private ValidacionFuenteServiceImpl validaiconFuenteService;
    @Autowired
    private SolicitudFirmaService solicitudFirmaService;

    @Autowired
    private UsuarioValidationService usuarioValidationService;

    @Autowired
    private FirmaOrdenService firmaOrdenService;

    private static Logger logger = LoggerFactory.getLogger(FirmaNegocio.class);

    public List<IDocumentosEstado> firmasPendientes(long idDusuario) throws FirmaException {
        try {
            return archivoFirmaService.findByEstado(idDusuario, 1);
        } catch (Exception e) {
            throw new FirmaException(e.getMessage());
        }
    }

    /**
     * @param idUsuario
     * @param pageNo
     * @param pageSize
     * @param sortBy
     * @return
     * @throws se.firme.commons.exception.FirmaException
     */
    public Page<IDocumentosEstado> getDocumentsPending(int idUsuario, Integer pageNo, Integer pageSize, String sortBy, String sortDirection,
                                                       int estado) throws FirmaException {
        Sort.Direction direction = Sort.Direction.ASC;

        if (sortDirection.equalsIgnoreCase("DESC")) {
            direction = Sort.Direction.DESC;
        }

        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(direction, sortBy));
        Page<IDocumentosEstado> pagedResult = archivoFirmaService.findByEstadoPag(idUsuario, estado, paging);

        return pagedResult;
    }

    public Page<IDocumentosEstado> getDocumentosFirmadosUsuario(int idUsuario, Integer pageNo, Integer pageSize,
                                                                String sortBy, String sortDirection, int estado) throws FirmaException {
        Sort.Direction direction = Sort.Direction.ASC;

        if (sortDirection.equalsIgnoreCase("DESC")) {
            direction = Sort.Direction.DESC;
        }

        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(direction, sortBy));
        Page<IDocumentosEstado> pagedResult = archivoFirmaService.findByFirmadosUsuarioPag(idUsuario, estado, paging);

        return pagedResult;
    }

    public Page<IDocumentosEstado> getDocumentsSigns(int idUsuario, Integer pageNo, Integer pageSize, String sortBy)
            throws FirmaException {
        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy));
        Page<IDocumentosEstado> pagedResult = archivoFirmaService.findByEstadoPag(idUsuario, 2, paging);
        return pagedResult;
    }

    public Page<ArchivoFirmaResponseDTO> documentosFirmadosPag(long idDusuario, Integer pageNo, Integer pageSize,
                                                               String sortBy) throws FirmaException {
        try {
            Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy));
            List<ArchivoFirmaResponseDTO> lista = new ArrayList<>();
            Page<ArchivoFirma> pages = archivoFirmaService.findByIdUsuarioArchivoFirmadoPag(((int) idDusuario), paging);
            List<ArchivoFirma> archivoFirmadoList = pages.getContent();
            if (archivoFirmadoList != null && !archivoFirmadoList.isEmpty()) {
                for (ArchivoFirma it : archivoFirmadoList) {
                    lista.add(ArchivoFirmaHelper.getArchivoFirmaHandle(it));
                }

            }
            Page<ArchivoFirmaResponseDTO> page = new PageImpl<>(lista, pages.getPageable(), pages.getTotalElements());
            return page;
        } catch (Exception e) {
            throw new FirmaException(e.getMessage());
        }
    }
    
    public List<IDocumentosEstado> findByIdUsuarioCodigoTransaccion(long idDusuario, String codigoTransaccion)
            throws FirmaException {
        try {
            return archivoFirmaService.findByIdUsuarioCodigoTransaccion(idDusuario, codigoTransaccion);
        } catch (Exception e) {
            throw new FirmaException(e.getMessage());
        }
    }

    public List<ArchivoFirmaResponseDTO> documentosFirmados(long idDusuario) throws FirmaException {

        List<ArchivoFirmaResponseDTO> lista = new ArrayList<>();
        try {
            List<ArchivoFirma> archivoFirmadoList = archivoFirmaService.findByIdUsuarioArchivoFirmado(idDusuario);
            if (archivoFirmadoList.isEmpty()) {
                throw new Exception("No tiene documentos firmados");
            } else {
                for (ArchivoFirma it : archivoFirmadoList) {
                    lista.add(ArchivoFirmaHelper.getArchivoFirmaHandle(it));
                }

                return lista;
            }
        } catch (Exception e) {
            throw new FirmaException(e.getMessage());
        }
    }

    public Token subirArchivo(FirmaRequestDTO datosFirma, byte[] bytes, boolean temporal) throws Exception {
        Usuario usuario = getUsuario(datosFirma.getIdUsuario());

        String rutaArchivo = Utilities.procesarArchivo(datosFirma, bytes, getRutaArchivos(temporal));

        // obtener el hash
        String hashArchivo = Utilities.generarHash(rutaArchivo);

        // validar si el hash ya existe y si la cantidad de firmas aun esta pendiente
        // por diligenciar
        ArchivoFirma archivoValidado = archivoFirmaService.validarSePuedeFirmar(hashArchivo);
        // List<ArchivoFirma> firmaList =
        // archivoFirmaService.getArchivoFirmadoByHash(hashArchivo);
        if (archivoValidado == null || archivoValidado.getHashArchivo() == null
                || "".equals(archivoValidado.getHashArchivo())) {
            throw new Exception("El archivo ya se encuentra firmado y cuenta con la cantidad de firmas requeridas");
        }

        // cargar el regitro del archivo a la bd
        if (archivoValidado.getIdArchivoFirma() == 0) { // el archivo aun no existe en la bd
            String path = null;

            if (temporal) {
                path = Utilities.getPathArchivo(usuario.getIdUsuario());
            }
            archivoValidado = archivoFirmaService.guardarArchivoFirma(datosFirma.getNombreArchivo(), hashArchivo,
                    datosFirma.getCantidadFirmas(), EEstadoArchivoFirma.PENDIENTE_FIRMA.getId(), datosFirma.getIp(),
                    datosFirma.getIdUsuario(), path);
        }

        Token tkn = tokenServiceImpl.findByIdArchivoFirma(archivoValidado.getIdArchivoFirma(), usuario.getIdUsuario());

        if (tkn == null || (tkn != null && Utilities.isVacio(tkn.getIdToken()))) {
            tkn = tokenServiceImpl.getNuevoToken(usuario, Parameters.string.TOKEN_TIPO_FIRMA_DOCUMENTO, 30,
                    archivoValidado.getIdArchivoFirma(), null, null);
        }

        if (tokenServiceImpl.isTokenActivo(tkn)) {
            // devolvemos token de firma
            return tkn;
        } else {
            // devolvemos error
            throw new Exception("Código de transaccion inactivo, por favor genere un nuevo código de transacción.");
        }
    }

    public ProcesoFirma getProcesoFirma(List<FirmaRequestDTO> datosFirma, int idUsuario) throws FirmaException {
        if (datosFirma == null || datosFirma.isEmpty()) {
            throw new FirmaException("No se especificó ningún archivo");
        } else {
            Usuario usuario = getUsuario(idUsuario);
            ProcesoFirma proceso = procesoFirmaService.getNuevoProcesoFirma(usuario);
            List<String> idsArchivos = new ArrayList<>();
            boolean firmanOtros = false;

            for (FirmaRequestDTO it : datosFirma) {
                // datosFirma.forEach(it -> {
                ArchivoFirma archivoFirma = archivoFirmaService.findById(it.getIdArchivo());
                if (archivoFirma != null) {
                    try {
                        logger.info("Tipo de firma: {0} " + archivoFirma.getTipoFirma());
                        if (archivoFirma.getTipoFirma() == null || "".equals(archivoFirma.getTipoFirma())) {
                            archivoFirma.setTipoFirma(Parameters.string.TIPO_FIRMA_SINGLE);
                            archivoFirmaService.update(archivoFirma);
                        } else {
                            SolicitudFirma solicitudFirma = solicitudFirmaService.findByIdArchivoFirmaAndEmailFirmanteAndFirmadoIsFalse(archivoFirma.getIdArchivoFirma(), usuario.getCorreoElectronico());
                            if (Parameters.string.TIPO_FIRMA_OTHERS.equals(archivoFirma.getTipoFirma()) && solicitudFirma == null) {
                                firmanOtros = true;
                            }
                        }
                    } catch (FirmaException e) {
                        logger.error("Error while updating signature type for file with id: " + archivoFirma.getIdArchivoFirma() + " :: " + e.getMessage());
                    }
                }
                idsArchivos.add(String.valueOf(it.getIdArchivo()));
            } // );
            if (firmanOtros) {
                throw new FirmaException("Uno o más archivos están en proceso de firma de otras personas");
            }
            procesoFirmaService.actualizarIdProcesoArchivoFirma(proceso.getIdProcesoFirma(), idsArchivos);
            logger.info("Firme.se Tu codigo es: {0}" + proceso.getCodigoSms());
            StringBuilder builder = new StringBuilder();
//                builder.append("<#> ");
            builder.append("Firme.se\n\n");
            builder.append("Tu codigo para completar el proceso de firma es: ");
            builder.append(proceso.getCodigoSms());
//                builder.append("\nvgmCCOrf5mD");/*VtXeXco0bNT*/

            mensajeroClient.notificar(builder.toString(), usuario.getNumeroCelular());
            return proceso;
        }
    }

    public void iniciarProcesoFirma(MultipleFirmaDTO multipleFirmaDTO, String tipoFirma, int idUsuario)
            throws FirmaException {
        try {
            Date fechaVigencia = Utilities.getFechaTextoADate(multipleFirmaDTO.getFechaVigencia());
            if (fechaVigencia != null) {
                if (fechaVigencia.before(new Date())) {
                    //throw new FirmaException("La fecha de vigencia debe ser mayor a la fecha actual.");
                }
            }
            List<FirmanteDTO> firmantes = multipleFirmaDTO.getFirmantes();
            logger.info("Firmantes de documento: " + firmantes);
            if (firmantes != null && !firmantes.isEmpty()) {

                int totalFirmantes = firmantes.size();
                if (Parameters.string.TIPO_FIRMA_MULTIPLE.equals(tipoFirma)) {
                    totalFirmantes++;
                }
                String maxFirmantes = env.getProperty(Parameters.MAXIMO_FIRMANTES_DOCUMENTO);
                Utilities.validarMaximoFirmantes(totalFirmantes, maxFirmantes);
                List<FirmaRequestDTO> documentos = multipleFirmaDTO.getDocumentos();
                if (documentos != null && !documentos.isEmpty()) {
                    archivoFirmaService.verificarTipoFirma(documentos);

                    // CORREGIDO: Usar lista y join para evitar dobles guiones en el token
                    List<String> idsList = new ArrayList<>();
                    List<String[]> adjuntos = new ArrayList<String[]>();
                    for (FirmaRequestDTO dto : documentos) {
                        logger.info("Id de archivo: " + dto.getIdArchivo());
                        idsList.add(String.valueOf(dto.getIdArchivo()));
                        ArchivoFirma archivoFirma = archivoFirmaService.findById(dto.getIdArchivo());
                        if (archivoFirma != null) {

                            archivoFirma.setCantidadFirmas(totalFirmantes);
                            archivoFirma.setEmailFirmantes(getFirmantes(firmantes));
                            archivoFirma.setTipoFirma(tipoFirma);
                            archivoFirmaService.update(archivoFirma);
                            logger.info("Ruta relativa: " + archivoFirma.getPath());
                            dto.setNombreArchivo(archivoFirma.getPath());

                            String[] infoArc = new String[2];
                            infoArc[0] = getRutaArchivos(true) + archivoFirma.getRutaRelativaArchivo();
                            infoArc[1] = archivoFirma.getNombreArchivo();
                            adjuntos.add(infoArc);
                        }
                    }
                    // Construir el string de IDs correctamente
                    String ids = String.join("-", idsList);

                    for (FirmanteDTO email : firmantes) {
                        String idsToken = "0-" + ids + "-0";
                        String token = tokenServiceImpl.crearTokenFirmante(idUsuario, idsToken, email.getEmail(),
                                fechaVigencia);

                        solicitudFirmaService.crearSolicitudesFirmaDeArchivos((long) idUsuario, idsToken, email.getEmail(), fechaVigencia);

                        EmailService
                                .enviarCorreo(email.getEmail(), "Notificación Firma Documentos Firme.se",
                                        EmailTemplateService.getTemplateFirmaMultiple(token,
                                                env.getProperty(Parameters.string.FIRMESE_BASE_URI_FRONTEND)),
                                        adjuntos);
                    }
                    return;
                }
                throw new FirmaException("Lista de documentos está vacía");
            }
            throw new FirmaException("Lista de firmantes está vacía");
        } catch (FirmaException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error while starting signature process with signers: " + multipleFirmaDTO.getFirmantes() + " and documents: " + multipleFirmaDTO.getDocumentos() + " :: " + e.getMessage());
            throw new FirmaException(e.getMessage());
        }
    }

    private String getFirmantes(List<FirmanteDTO> firmantes) {
        String emails = "";
        for (FirmanteDTO email : firmantes) {
            emails += email.getEmail() + " ";
        }
        emails = emails.trim();
        emails = emails.replaceAll(" ", ",");
        return emails;
    }

    public void validarCodigoSMSFirma(String ctoken, long usms) {

    }

    private Usuario getUsuario(long idUsuario) throws FirmaException {
        Usuario usuario = usuarioService.findById(idUsuario);

        if (idUsuario == 0) {
            throw new FirmaException("Usuario no válido");
        }

        if (usuario == null) {
            throw new FirmaException("Usuario no encontrado");
        }

        return usuario;
    }

    private String getRutaArchivos(boolean temporal) {
        return env.getProperty("routes.custom.file") + File.separator + (temporal ? ("tmp" + File.separator) : "");
    }

    public boolean completarFirma(DocumentoDTO documento) throws FirmaException {
        try {
            logger.info("Entrando a método completar firma");
            Token token = tokenServiceImpl.consultarCodTransaccionFirma(documento.getCodTransaccion());
            logger.info("Sale de consulta de token");
            if (token != null) {
                logger.info("Si hay token");
                Usuario usuario = token.getIdUsuario();
                if (usuario != null) {
                    logger.info("Fecha expedición registro: {0}" + usuario.getFechaExpedicionDocumento());
                    String json = Utilities.converB64ToString(documento.getDocument());
                    logger.info(json);
                    PersonaScanDTO personaDTO = new Gson().fromJson(json, PersonaScanDTO.class);
                    if (personaDTO != null) {
                        logger.info("Valida número de cédula: {0}" + personaDTO.getDocumentID());
                        logger.info("RawData: {0}" + Utilities.converB64ToString(documento.getRowData()));
                        String documentoRegistro = "";
                        try {
                            documentoRegistro = String.valueOf(Integer.parseInt(personaDTO.getDocumentID()));
                        } catch (Exception e) {
                            documentoRegistro = personaDTO.getDocumentID();
                        }
                        if (documentoRegistro.equals(usuario.getNumeroDocumento())) {
                            tokenServiceImpl.desactivarTokenByIdToken(token.getIdToken());
                            ArchivoFirma archivoFirma = archivoFirmaService.findById(token.getIdArchivoFirma());
                            firmaArchivoService.guardarFirmaArchivoUsuario(archivoFirma, token.getIdUsuario(), true);
                            archivoFirmaService.actualizarCantidadFirmas(archivoFirma.getCantidadFirmado() + 1,
                                    archivoFirma.getIdArchivoFirma());
                            archivoFirmaService.actualizarEstadoArchivoFirma(archivoFirma.getIdArchivoFirma(), 2);
                            return true;
                        }
                        throw new FirmaException("No coincide el número de documento de la persona");
                    }
                    throw new FirmaException("No fue posible obtener el registro de personaDTO");
                }
                throw new FirmaException("No fue posible obtener el registro de usuario");
            }
            throw new FirmaException("No fue posible obtener el registro de token");
        } catch (FirmaException | NullPointerException | IOException | JsonSyntaxException e) {
            throw new FirmaException(
                    "ER: " + (e.getMessage() == null ? "No se pudo completar el proceso" : e.getMessage()));
        }
    }

    public List<ArchivoFirmaResponseDTO> subirArchivoMultiple(List<FirmaRequestDTO> datosFirmas, boolean temporal,
                                                              String ip, int idUsuario) throws Exception {
        List<ArchivoFirmaResponseDTO> archivosSubidos = new ArrayList<>();

        for (FirmaRequestDTO datosFirma : datosFirmas) {
            Usuario usuario = getUsuario(idUsuario);
            String rutaArchivo = Utilities.procesarArchivo(datosFirma, null, getRutaArchivos(temporal));

            // obtener el hash
            String hashArchivo = Utilities.generarHash(rutaArchivo);

            ArchivoFirmaResponseDTO arc = new ArchivoFirmaResponseDTO();
            arc.setNombreArchivo(datosFirma.getNombreArchivo());

            if (Utilities.isEncryptedPDF(rutaArchivo)) {
                arc.setObservacion("El archivo " + datosFirma.getNombreArchivo()
                        + " se encuentra protegido con contraseña, por favor retire la protección y vuelva a intentarlo");
                archivosSubidos.add(arc);
                continue;
            }

            // validar si el hash ya existe y si la cantidad de firmas aun esta pendiente
            // por diligenciar
            ArchivoFirma archivoValidado = archivoFirmaService.validarSePuedeFirmar(hashArchivo);
            if (archivoValidado == null) {
                arc.setObservacion("El archivo " + datosFirma.getNombreArchivo()
                        + " ya se encuentra firmado y cuenta con la cantidad de firmas requeridas");
            } else {
                if (archivoValidado.getIdArchivoFirma() == 0) { // el archivo aun no existe en la bd
                    String path = Utilities.getPathArchivo(usuario.getIdUsuario());
                    String fileName = datosFirma.getNombreArchivo();
                    archivoValidado = archivoFirmaService.guardarArchivoFirma(fileName, hashArchivo,
                            datosFirma.getCantidadFirmas(), EEstadoArchivoFirma.PENDIENTE_FIRMA.getId(), ip,
                            usuario.getIdUsuario(), path, datosFirma.getDescripcion());
                    datosFirma.setIdArchivo(archivoValidado.getIdArchivoFirma());
                    arc.setIdArchivo(archivoValidado.getIdArchivoFirma());
                    arc.setObservacion("OK");
                    arc.setFechaRegistro(new Date());
                    arc.setFechaRegistroStr(Utilities.getFechaDateAFechaTexto(new Date(), "yyyy-MM-dd"));
                } else {
                    arc.setIdArchivo(archivoValidado.getIdArchivoFirma());
                    arc.setFechaRegistro(archivoValidado.getFechaRegistro());
                    arc.setObservacion("El archivo " + datosFirma.getNombreArchivo()
                            + " ya se encuentra cargado en la plataforma de firme.se");
                }
            }
            archivosSubidos.add(arc);
        }

        return archivosSubidos;
    }

    public List<ArchivoFirmaResponseDTO> firmarDocumentos(String csms, long usms, HttpServletRequest request)
            throws FirmaException, IOException, Exception {
        String ipAddress = getRemoteAddres(request);
        String userAgent = request.getHeader("User-Agent");
        return procesoFirmaService.firmarDocumentos(csms, usms, null, ipAddress, userAgent);
    }

    public void enviarNotificacionMultiple(SendFileEmailRequestDTO datosFirma) throws FirmaException, Exception {
        List<String> ids = new ArrayList<>();

        for (FirmaRequestDTO it : datosFirma.getArchivos()) {
            ids.add(String.valueOf(it.getIdArchivo()));
        }

        List<ArchivoFirma> archivos = archivoFirmaService.findByIds(ids);
        if (archivos != null && archivos.size() > 0) {
            List<String[]> adjuntos = new ArrayList<>();
            for (ArchivoFirma it : archivos) {
                String ruta;
                String archivo;
                if (it.getFirmaArchivoUsuarioList() != null && it.getFirmaArchivoUsuarioList().size() > 0) {
                    int i = (it.getFirmaArchivoUsuarioList().size() - 1);
                    ruta = getRutaArchivos(false) + it.getFirmaArchivoUsuarioList().get(i).getRutaRelativaArchivo();
                    archivo = it.getFirmaArchivoUsuarioList().get(i).getNombreArchivoFirma();
                    String[] infoArc = new String[2];
                    infoArc[0] = ruta;
                    infoArc[1] = archivo;
                    adjuntos.add(infoArc);
                }
            }

            EmailService.enviarCorreo(datosFirma.getCorreos(), "Notificación Firma Documentos Firme.se",
                    EmailTemplateService.notificacionFirmasEmailTemplate(), adjuntos);
        } else {
            throw new FirmaException("No se encontraron registros con los id especificados ");
        }
    }

    public List<ArchivoFirmaResponseDTO> getArchivoFirmaByHash(String hashArchivo)
            throws FirmaException, ParseException, Exception {
        List<ArchivoFirma> archivos = archivoFirmaService.findByHash(hashArchivo);

        List<ArchivoFirmaResponseDTO> lista = new ArrayList<>();
        if (archivos != null && archivos.size() > 0) {
            for (ArchivoFirma it : archivos) {
                String ruta;
                String archivo;
                if (it.getFirmaArchivoUsuarioList() != null && it.getFirmaArchivoUsuarioList().size() > 0) {
                    int i = (it.getFirmaArchivoUsuarioList().size() - 1);
                    ruta = getRutaArchivos(false) + it.getFirmaArchivoUsuarioList().get(i).getRutaRelativaArchivo();
                    archivo = it.getFirmaArchivoUsuarioList().get(i).getNombreArchivoFirma();
                    ArchivoFirmaResponseDTO obj = ArchivoFirmaHelper.getArchivoFirmaHandle(it);
//                    obj.setB64(Utilities.convertirRutaArchivoStringAABse64(ruta + archivo));

                    PropietarioDTO dto = new PropietarioDTO();
                    Usuario usuario = usuarioService.findById(it.getIdUsuario());
                    dto.setNombreCompleto(usuario.getNombreCompleto());
                    dto.setNumeroDocumento(usuario.getNumeroDocumento());
                    dto.setTipoDocumento(usuario.getIdTipoDocumento().getNombreTipoDocumento() + "");
                    obj.setPropietario(dto);

                    lista.add(obj);
                    break;
                }
            }
            return lista;
        } else {
            throw new FirmaException("No se encontraron registros con el hash especificado ");
        }
    }

    public void eliminarDocumentoPendiente(long idDocument, long idUsuario) throws FirmaException {
        archivoFirmaService.eliminarArchivo(idDocument, idUsuario);
    }

    private String getRemoteAddres(HttpServletRequest request) {
        String ipAddress = request.getHeader("X-FORWARDED-FOR");
        return ipAddress == null ? request.getRemoteAddr() : ipAddress;
    }

    public DetalleSolicitudDTO verFirmantes(int idUsuario, int idArchivo) throws FirmaException {
        try {
            logger.info("ver detalle de documento con id " + idArchivo + " de propietario con id " + idUsuario);
            return archivoFirmaService.buscarFirmantesSolicitados(idUsuario, idArchivo);
        } catch (Exception e) {
            throw new FirmaException("No se pudo retornar los firmantes :: " + e.getMessage());
        }
    }

    public List<ArchivoFirmaResponseDTO> subirArchivoMultiplePreregistro(MultipleFirmaDTO datosFirma,
                                                                         UserLoginDTO infoRerquest, int idUsuario) throws FirmaException {
        verificarFirmante(datosFirma.getFirmantes(), idUsuario);
        Date fechaVigencia = null;
        try {
            fechaVigencia = Utilities.getFechaTextoADate(datosFirma.getFechaVigencia());
        } catch (Exception e) {
            logger.info("No se pudo calcular la fecha de vigencia: " + e.getMessage());
        }
        if (fechaVigencia != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(fechaVigencia);
            calendar.add(Calendar.HOUR_OF_DAY, 24);
            fechaVigencia = calendar.getTime();

            if (fechaVigencia.before(new Date())) {
                logger.info("ERROR fechaVigencia está antes: " + fechaVigencia + " de fechaActual:   " + new Date());
                throw new FirmaException("La fecha de vigencia debe ser mayor a la fecha actual.");
            }
        }
        return subirArchivoMultiple(datosFirma, infoRerquest, idUsuario);
    }

    public List<ArchivoFirmaResponseDTO> subirArchivoMultiple(MultipleFirmaDTO datosFirma, UserLoginDTO infoRerquest,
                                                              int idUsuario) throws FirmaException {
        logger.info("" + infoRerquest.toString());
        try {

            List<ArchivoFirmaResponseDTO> archivosSubidos = subirArchivoMultiple(datosFirma.getDocumentos(), true,
                    infoRerquest.getDireccionIP(), idUsuario);
            logger.info("info requerida: " + datosFirma.getTipoFirma());
            if (datosFirma.getTipoFirma() == null || "".equals(datosFirma.getTipoFirma())) {
                datosFirma.setTipoFirma(Parameters.string.TIPO_FIRMA_OTHERS);
            }

            logger.info("info mapeada: " + datosFirma.getTipoFirma());
            iniciarProcesoFirma(datosFirma, datosFirma.getTipoFirma(), idUsuario);
            return archivosSubidos;
        } catch (Exception e) {
            throw new FirmaException(
                    "No se pudo subir documento a la plataforma, " + e.getMessage());
        }
    }

    private void verificarFirmante(List<FirmanteDTO> firmantes, int idUsuario) throws FirmaException {
        try {
            for (FirmanteDTO firmanteDTO : firmantes) {
                if (!firmanteDTO.isCompleto()) {
                    throw new FirmaException(
                            "No estan completos los datos del interesado con email " + firmanteDTO.getEmail());
                }
                logger.info("Verificando Usuario " + firmanteDTO.getNumeroDocumento() + " :: " + firmanteDTO.getEmail());
                Usuario usuario = usuarioService.findByNumeroDocumento(firmanteDTO.getNumeroDocumento());
                if (usuario != null) {
                    int firmas = procesoFirmaService.findAllByIdUsuario(usuario).size();
                    logger.info("Usuario encontrado " + usuario.toString());
                    if (usuario.getActivo() && !usuario.getProcesoRegistro()) {
                        logger.info("Usuario enrolado Hacer A (No hace nada)");
                        if (firmas == 0 && !firmanteDTO.getEmail().equals(usuario.getCorreoElectronico()) && usuario.getIdReferido() == (long) idUsuario) {
                            usuario.setCorreoElectronico(firmanteDTO.getEmail());
                            usuarioService.editarRegistro(usuario);
                        } else if (!firmanteDTO.getEmail().equals(usuario.getCorreoElectronico())) {
                            throw new FirmaException("El usuario ya está registrado con otros datos");
                        }

                    } else {
                        logger.info("Usuario existe y no enrolando hacer B (Completa el registro)");
                        usuario.setProcesoRegistro(false);
                        usuario.setActivo(true);
                        usuario.setEstado(true);
                        usuario.setNombreCompleto(firmanteDTO.getNombreCompleto());
                        usuario.setIdReferido(idUsuario);

                        if (firmas == 0 && !firmanteDTO.getEmail().equals(usuario.getCorreoElectronico()) && usuario.getIdReferido() == (long) idUsuario) {
                            usuario.setCorreoElectronico(firmanteDTO.getEmail());
                        }

                        usuarioService.editarRegistro(usuario);
                        AdmUsuario admUsuario = UsuarioHelper.convert(usuario);
                        usuarioVMSClient.guardarUsuarioAdm(admUsuario);
                        logger.info("User admin activo");
                        validarUsuarioRegistraduria(usuario);
                    }
                } else {
                    logger.info("No existe usuario y tiene Cédula escaneada hacer C (Inserta el usuario con todo OK)");

                    usuario = new Usuario();

                    usuario.setClave("noasignada");
                    usuario.setCorreoElectronico(firmanteDTO.getEmail().toLowerCase().trim());
                    usuario.setFechaExpedicionDocumento(
                            new SimpleDateFormat("yyyy-MM-dd").parse(firmanteDTO.getFechaExpedicion()));
                    usuario.setFechaRegistro(new Date());
                    usuario.setIdReferido(idUsuario);
                    usuario.setIdTipoDocumento(new TipoDocumento(firmanteDTO.getTipoDocumento()));
                    usuario.setNombreCompleto(firmanteDTO.getNombreCompleto());
                    usuario.setNumeroCelular(firmanteDTO.getNumeroCelular());
                    usuario.setNumeroDocumento(firmanteDTO.getNumeroDocumento());
                    usuario = usuarioService.crearRegistroUsuario(usuario);
                    usuario.setActivo(true);
                    usuario.setProcesoRegistro(false);
                    usuario.setEstado(true);
                    usuarioService.editarRegistro(usuario);
                    AdmUsuario admUsuario = UsuarioHelper.convert(usuario);
                    usuarioVMSClient.guardarUsuarioAdm(admUsuario);
                    logger.info("User admin activo");
                    validarUsuarioRegistraduria(usuario);
                }
            }

        } catch (Exception e) {
            logger.error("Verificar firmante: " + e.getMessage());
            throw new FirmaException("No se pudo hacer la verificación de usuario interviniente :: " + e.getMessage());
        }

    }

    private void validarUsuarioRegistraduria(Usuario usuario) {
        try {
            new Thread(() -> {
                try {
                    EndpointDTO endpointDTO = new EndpointDTO();
                    endpointDTO.setUrl(env.getProperty("routes.custom.konivin.ws"));
                    endpointDTO.setUser(env.getProperty("routes.custom.konivin.pass"));
                    endpointDTO.setPasswd(env.getProperty("routes.custom.konivin.user"));
                    validaiconFuenteService.validarUsuarioEnRegistraduria(usuario, endpointDTO);
                } catch (Exception ex) {
                    logger.error("ER: {0}" + ex.getMessage());
                }
            }).start();
        } catch (Exception ex) {
            logger.error("ER: {0}" + ex.getMessage());
        }
    }

    public Page<IArchivoFirma> findArchivosFirmados(int idUsuario, Integer pageNo, Integer pageSize, String sortBy)
            throws FirmaException {
        Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(sortBy));
        Page<IArchivoFirma> pagedResult = archivoFirmaService.findArchivosFirmados(idUsuario, paging);

        return pagedResult;
    }

    public Object reenviarSolicitudFirma(int idPropietario, String input) throws FirmaException {
        try {
            JsonElement element = new JsonParser().parse(input);
            if (element != null) {
                JsonObject jsonObject = element.getAsJsonObject();
                if (jsonObject != null) {
                    String ids = jsonObject.get("code").getAsString();
                    Token tkn = tokenServiceImpl.findByIdsFirmante(jsonObject.get("signatory").getAsString(),
                            "%-" + ids + "-%", idPropietario);
                    if (tkn != null) {
                        logger.info("Email firmante: " + tkn.getEmailFirmante());
                        if (tkn.getActivo()) {
                            if (tkn.getFechaVencimiento().after(new Date())) {
                                enviarNotificacionSolicitud(tkn.getIdToken(), tkn.getEmailFirmante(),
                                        jsonObject.get("mode").getAsString());
                                return "token vigente";
                            }
                            tkn.setFechaRegistro(new Date());
                            tkn.setFechaVencimiento(
                                    Utilities.getSumarMinutosHorasDiasAFecha(tkn.getFechaRegistro(), 0, 72, 0));
                            tokenServiceImpl.actualizarRegistro(tkn);
                            enviarNotificacionSolicitud(tkn.getIdToken(), tkn.getEmailFirmante(),
                                    jsonObject.get("mode").getAsString());
                            return "token vencido";
                        } else {
                            Optional<FirmaArchivoUsuario> optional = firmaArchivoService
                                    .consultarFirmaDocumento(tkn.getEmailFirmante(), Integer.parseInt(ids));
                            if (optional.isPresent()) {
                                return "token usado en firma";
                            }
                            tkn.setActivo(true);
                            tokenServiceImpl.actualizarRegistro(tkn);
                            enviarNotificacionSolicitud(tkn.getIdToken(), tkn.getEmailFirmante(),
                                    jsonObject.get("mode").getAsString());
                            return "token no activo";
                        }
                    }
                    throw new FirmaException("No se encontró registro de solicitud");
                }
            }

            throw new FirmaException("Datos de entrada no válidos");
        } catch (Exception e) {
            throw new FirmaException(e.getMessage());
        }
    }

    private boolean enviarNotificacionSolicitud(String token, String email, String medio) {
        try {
            switch (medio) {
                case "wta":
                    Usuario usuario = usuarioService.findByEmail(email);
                    if (usuario != null) {
                        StringBuilder builder = new StringBuilder();
                        builder.append("Cordial saludo");
                        builder.append(" *");
                        builder.append(usuario.getNombreCompleto());
                        builder.append("* \\n");
                        builder.append("\\n");
                        builder.append("Este mensaje de solicitud de firma es enviado por la plataforma firme.se");
                        builder.append("\\n");
                        builder.append("\\n");
                        builder.append("Para continuar con el proceso por favor haz clic en el enlace a continuación:");
                        builder.append("\\n");
                        builder.append("\\n");
                        builder.append(env.getProperty(Parameters.string.FIRMESE_BASE_URI_FRONTEND));
                        builder.append("/multiple-firma/");
                        builder.append(token);

                        mensajeroClient.notificar(builder.toString(), usuario.getNumeroCelular());
                        return true;
                    }
                    EmailService.enviarCorreo(email, "Notificación Firma Documentos Firme.se", EmailTemplateService
                                    .getTemplateFirmaMultiple(token, env.getProperty(Parameters.string.FIRMESE_BASE_URI_FRONTEND)),
                            null);
                    return true;
                case "eml":
                    EmailService.enviarCorreo(email, "Notificación Firma Documentos Firme.se", EmailTemplateService
                                    .getTemplateFirmaMultiple(token, env.getProperty(Parameters.string.FIRMESE_BASE_URI_FRONTEND)),
                            null);
                    break;

                default:
                    EmailService.enviarCorreo(email, "Notificación Firma Documentos Firme.se", EmailTemplateService
                                    .getTemplateFirmaMultiple(token, env.getProperty(Parameters.string.FIRMESE_BASE_URI_FRONTEND)),
                            null);
                    break;
            }
            return true;
        } catch (Exception e) {
            return false;
        }

    }

    public List<TokenFirmaDTO> getSolicitudesFirma(int parseInt) throws FirmaException {
        try {
            Usuario usuario = usuarioService.findById(parseInt);
            if (usuario != null) {
                List<TokenFirmaDTO> tokenList = new ArrayList<>();
                List<Token> tokens = tokenServiceImpl.findTokensByFirmante(usuario.getCorreoElectronico(),
                        Parameters.string.TOKEN_TIPO_FIRMA_DOCUMENTO);
                for (Token token : tokens) {
                    TokenFirmaDTO dto = new TokenFirmaDTO();
                    dto.setToken(token.getIdToken());
                    dto.setFechaSolicitud(token.getFechaRegistro().toString());
                    dto.setFechaVigencia(token.getFechaVencimiento().toString());
                    // usuario = usuarioService.findById(token.geti;
                    PropietarioDTO propietarioDTO = new PropietarioDTO();
                    propietarioDTO.setNombreCompleto(token.getIdUsuario().getNombreCompleto());
                    propietarioDTO.setNumeroDocumento(token.getIdUsuario().getNumeroDocumento());
                    propietarioDTO.setTipoDocumento(token.getIdUsuario().getIdTipoDocumento().getNombreTipoDocumento());
                    dto.setPropietario(propietarioDTO);
                    List<FirmaRequestDTO> firmas = new ArrayList<>();

                    try {
                        List<ArchivoFirma> archivos = archivoFirmaService.buscarArchivosFirma(token.getIds());

                        if (archivos.isEmpty()) {
                            continue;
                        }

                        for (ArchivoFirma archivo : archivos) {
                            FirmaRequestDTO dtoFirma = new FirmaRequestDTO();
                            dtoFirma.setIdArchivo(archivo.getIdArchivoFirma());
                            dtoFirma.setNombreArchivo(archivo.getNombreArchivo());
                            dtoFirma.setIp(archivo.getIp());
                            firmas.add(dtoFirma);
                        }
                    } catch (Exception e) {
                        logger.info(e.getMessage());
                        continue;
                    }
                    dto.setArchivos(firmas);
                    tokenList.add(dto);
                }
                return tokenList;
            }
            throw new FirmaException("No se encontró registro de usuario");
        } catch (Exception e) {
            throw new FirmaException(e.getMessage());
        }
    }

    public Map<String, Object> procesarSolicitudFirmaOrden(FirmaOrdenRequestDTO request, String ipAddress) throws FirmaException {
        try {
            Map<String, Object> resultado = firmaOrdenService.procesarSolicitudFirmaOrden(request, ipAddress);
            
            // Agregar información adicional sobre los tokens y correos enviados
            logger.info("Solicitud de firma en orden procesada exitosamente");
            if (resultado.containsKey("documentos")) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> documentos = (List<Map<String, Object>>) resultado.get("documentos");
                int totalTokens = documentos.stream()
                    .mapToInt(doc -> doc.containsKey("tokensGenerados") ? (Integer) doc.get("tokensGenerados") : 0)
                    .sum();
                int totalCorreos = documentos.stream()
                    .mapToInt(doc -> doc.containsKey("correosEnviados") ? (Integer) doc.get("correosEnviados") : 0)
                    .sum();
                    
                resultado.put("totalTokensGenerados", totalTokens);
                resultado.put("totalCorreosEnviados", totalCorreos);
                logger.info("Total de tokens generados: " + totalTokens);
                logger.info("Total de correos enviados: " + totalCorreos);
            }
            
            return resultado;
        } catch (Exception e) {
            throw new FirmaException("Error procesando solicitud de firma en orden: " + e.getMessage());
        }
    }

    public Map<String, Object> consultarEstadoOrdenFirma(Long idArchivo) throws FirmaException {
        try {
            List<SolicitudFirma> solicitudes = firmaOrdenService.consultarSolicitudesPorArchivo(idArchivo);
            
            Map<String, Object> response = new HashMap<>();
            response.put("idArchivo", idArchivo);
            response.put("solicitudes", solicitudes);
            response.put("totalSolicitudes", solicitudes.size());
            
            // Contar firmados vs pendientes
            long firmados = solicitudes.stream().filter(SolicitudFirma::isFirmado).count();
            long pendientes = solicitudes.size() - firmados;
            
            response.put("firmados", firmados);
            response.put("pendientes", pendientes);
            
            // Determinar si está completo
            boolean completo = pendientes == 0;
            response.put("completo", completo);
            
            return response;
        } catch (Exception e) {
            throw new FirmaException("Error consultando estado de orden: " + e.getMessage());
        }
    }

    public boolean validarOrdenFirma(Long idArchivo, String emailFirmante) throws FirmaException {
        try {
            return firmaOrdenService.validarOrdenFirma(idArchivo, emailFirmante);
        } catch (Exception e) {
            throw new FirmaException("Error validando orden de firma: " + e.getMessage());
        }
    }

    public List<SolicitudFirma> consultarSolicitudesPorArchivo(Long idArchivo) throws FirmaException {
        try {
            return firmaOrdenService.consultarSolicitudesPorArchivo(idArchivo);
        } catch (Exception e) {
            throw new FirmaException("Error consultando solicitudes por archivo: " + e.getMessage());
        }
    }
        public Map<String, Object> consultarEstadoSolicitudPorUsuarioOEmail(Long idArchivoFirma, Long idUsuario, String emailFirmante) throws FirmaException {
        ArchivoFirma archivo;
        try {
            archivo = archivoFirmaService.findById(idArchivoFirma);
        } catch (Exception e) {
            throw new FirmaException("Error consultando archivo: " + e.getMessage());
        }
        if (archivo == null) {
            throw new FirmaException("Archivo de firma no encontrado: " + idArchivoFirma);
        }

        boolean esPropietario = false;
        if (idUsuario != null) {
            esPropietario = archivo.getIdUsuario() == idUsuario.longValue();
        }

        boolean esFirmante = false;
        if (!esPropietario && emailFirmante != null && !emailFirmante.trim().isEmpty()) {
            try {
                SolicitudFirma solicitud = solicitudFirmaService.findByIdArchivoFirmaAndEmailFirmante(idArchivoFirma, emailFirmante.trim().toLowerCase());
                esFirmante = (solicitud != null);
            } catch (Exception e) {
                throw new FirmaException("Error consultando firmante en la base de datos: " + e.getMessage());
            }
        }

        if (!esPropietario && !esFirmante) {
            throw new FirmaException("No tiene permisos para consultar este archivo");
        }

        Map<String, Object> estado;
        try {
            estado = firmaOrdenService.consultarEstadoSolicitud(idArchivoFirma);
        } catch (Exception e) {
            throw new FirmaException("Error consultando estado de solicitud: " + e.getMessage());
        }
        estado.put("validadoParaUsuario", esPropietario ? idUsuario : null);
        estado.put("validadoParaEmail", esFirmante ? emailFirmante : null);
        estado.put("propietarioArchivo", archivo.getIdUsuario());
        estado.put("usuarioParticipa", esPropietario || esFirmante);

        return estado;
    }

    // logica con paginación para los archivos firmados con busqueda
    public Page<IDocumentosEstado> getDocumentosFirmadosConBusqueda(
    	    int idUsuario, String nombre, Integer pageNo, Integer pageSize, 
    	    String sortBy, String sortDirection, int estado) throws FirmaException {
    	    
    	    logger.info("Iniciando lógica de negocio para buscar archivos firmados del usuario: "+ idUsuario+ " el nombre es: "+ nombre);
    	    Sort.Direction direction = Sort.Direction.ASC;

        	if (sortDirection.equalsIgnoreCase("DESC")) {
        		direction = Sort.Direction.DESC;
        	}
    	    try{
                Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(direction, sortBy));
                Page<IDocumentosEstado> pagedResult = archivoFirmaService.findFirmadosConBusqueda(idUsuario, estado, nombre, paging);
    	        return pagedResult;
            } catch (Exception e){
                throw new FirmaException("Error al buscar documentos unificados");
            }
    	    
    	}
    
    // logica con paginación para los archivos unidos firmados y propietario
    public Page<IDocumentosEstado> getDocumentosUnificadosConBusqueda(
    	    int idUsuario, String nombre, Integer pageNo, Integer pageSize, 
    	    String sortBy, String sortDirection, int estado) throws FirmaException {
    		
    	
    		Sort.Direction direction = Sort.Direction.ASC;

    		if (sortDirection.equalsIgnoreCase("DESC")) {
    			direction = Sort.Direction.DESC;
    		}
    	    
    	    logger.info("Iniciando lógica de negocio para buscar documentos Unificados, el nombre del archivo es: "+ nombre);
    	   
            try{
                Pageable paging = PageRequest.of(pageNo, pageSize, Sort.by(direction, sortBy));
                Page<IDocumentosEstado> pagedResult = archivoFirmaService.findDocumentosUnificadosConBusqueda(idUsuario, estado, nombre, paging);
                return pagedResult;
            } catch(Exception e){
                throw new FirmaException("Error al buscar documentos unificados");
            }
    	}

}