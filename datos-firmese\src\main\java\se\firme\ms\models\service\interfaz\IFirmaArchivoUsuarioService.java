/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.models.service.interfaz;

import java.util.List;
import se.firme.commons.exception.FirmaException;
import se.firme.ms.datos.models.entity.ArchivoFirma;
import se.firme.ms.datos.models.entity.FirmaArchivoUsuario;
import se.firme.ms.datos.models.entity.Usuario;

/**
 *
 * <AUTHOR>
 */
public interface IFirmaArchivoUsuarioService {
    public void guardarFirmaArchivoUsuario(ArchivoFirma archivoFirma, Usuario usuario, boolean subioInicialmente);
    public List<FirmaArchivoUsuario> findByIdArchivoFirma(long idArchivoFirma);
    public List<FirmaArchivoUsuario> findByIdArchivoFirmaAndIdusuario(long idArchivoFirma, long idUsuario);
    public FirmaArchivoUsuario guardarFirmaArchivoUsuario(ArchivoFirma archivoFirma, Usuario usuario, boolean subioInicialmente, String hash, String rutaRelativa, String nombreArchivoFirma, String ipAddress,String agente) throws  FirmaException;
    public void actualizarFirma(String hashArchivo, String rutaRelativa, String nombreArchivo, long idFirmaArchivoUsuario);
}
