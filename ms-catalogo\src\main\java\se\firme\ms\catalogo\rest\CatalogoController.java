/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.catalogo.rest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import se.firme.commons.firmese.dto.ApiResponse;
import se.firme.ms.models.service.interfaz.ICatalogo;



/**
 * @document CatalogoController
 * <AUTHOR>
 * @fecha martes, agosto 19 de 2020, 07:30:00 PM
 */
@RestController
@RefreshScope 
@RequestMapping("/manager")
public class CatalogoController {

    @Autowired
  private  ICatalogo catalogoService;

    @GetMapping("/tipo-documento/listar")
    public ResponseEntity<ApiResponse> getTipoDocumentoActivo() {
        try {
            return new ResponseEntity<>(new ApiResponse.ok().data(catalogoService.getTipoDocumentoActivo()).build(), HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
        }
    }
}
