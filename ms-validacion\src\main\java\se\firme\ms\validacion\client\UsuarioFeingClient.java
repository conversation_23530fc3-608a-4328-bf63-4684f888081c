/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package se.firme.ms.validacion.client;


import co.venko.ms.models.entity.AdmUsuario;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 *
 * <AUTHOR>
 */
@FeignClient(name="vms-usuario") // nombre del servicio con el cual nos vamos a comunicar y que debe estar registrado en eureka
public interface UsuarioFeingClient {
 
    @PostMapping("/usuario")
    public AdmUsuario guardarUsuarioAdm(@RequestBody AdmUsuario usuario);
}
