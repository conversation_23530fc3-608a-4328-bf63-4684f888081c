package se.firme.commons.firmese.dto;

import java.util.List;

public class TokenFirmaDTO {
	private String token;
	private String fechaSolicitud;
	private String fechaVigencia;
	private PropietarioDTO propietario;
	private List<FirmaRequestDTO> archivos;

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public String getFechaSolicitud() {
		return fechaSolicitud;
	}

	public void setFechaSolicitud(String fechaSolicitud) {
		this.fechaSolicitud = fechaSolicitud;
	}

	public String getFechaVigencia() {
		return fechaVigencia;
	}

	public void setFechaVigencia(String fechaVigencia) {
		this.fechaVigencia = fechaVigencia;
	}

	public PropietarioDTO getPropietario() {
		return propietario;
	}

	public void setPropietario(PropietarioDTO propietario) {
		this.propietario = propietario;
	}

	public List<FirmaRequestDTO> getArchivos() {
		return archivos;
	}

	public void setArchivos(List<FirmaRequestDTO> archivos) {
		this.archivos = archivos;
	}

}
