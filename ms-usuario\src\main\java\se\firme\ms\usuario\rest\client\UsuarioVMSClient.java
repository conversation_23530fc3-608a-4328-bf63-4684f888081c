/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package se.firme.ms.usuario.rest.client;

import co.venko.ms.models.entity.AdmUsuario;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @document UsuarioVMSClient
 * <AUTHOR>
 * @fecha lunes, agosto 24 de 2020, 04:38:01 PM
 */
@FeignClient(name = "vms-usuario")
public interface UsuarioVMSClient {

    @PostMapping("/usuario")
    public AdmUsuario guardarUsuarioAdm(@RequestBody AdmUsuario usuario);

    @GetMapping("/usuario/search/eliminar-cuenta")
	public void eliminarUsuarioAdm(@RequestParam("codigo") String codigo);
    
    @GetMapping("/usuario/search/buscar-usuario-nat")
    public AdmUsuario findById(@RequestParam("idusuario") String idusuario);
    
    //@PutMapping("/usuario")
   // public AdmUsuario actualizarUsuarioAdm(@RequestBody AdmUsuario usuario);
}
