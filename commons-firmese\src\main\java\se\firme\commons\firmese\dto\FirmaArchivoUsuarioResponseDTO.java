/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.commons.firmese.dto;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class FirmaArchivoUsuarioResponseDTO implements Serializable{

    private static final long serialVersionUID = -6665416099926667622L;

    private Date fechaFirma;
    private boolean subioArchivo;
    private UsuarioResponseDTO firma;
    private String hashFirma;
    private String ipFirma;
    private String fechaFirmaStr;
    private String agenteNavegador;

    public FirmaArchivoUsuarioResponseDTO() {
    }

    public Date getFechaFirma() {
        return fechaFirma;
    }

    public void setFechaFirma(Date fechaFirma) {
        this.fechaFirma = fechaFirma;
    }

    public boolean isSubioArchivo() {
        return subioArchivo;
    }

    public void setSubioArchivo(boolean subioArchivo) {
        this.subioArchivo = subioArchivo;
    }

    public UsuarioResponseDTO getFirma() {
        return firma;
    }

    public void setFirma(UsuarioResponseDTO firma) {
        this.firma = firma;
    }

    public String getHashfirma() {
        return hashFirma;
    }

    public void setHashfirma(String hashfirma) {
        this.hashFirma = hashfirma;
    }

    public String getIpFirma() {
        return ipFirma;
    }

    public void setIpFirma(String ipFirma) {
        this.ipFirma = ipFirma;
    }

    public String getFechaFirmaStr() {
        return fechaFirmaStr;
    }

    public void setFechaFirmaStr(String fechaFirmaStr) {
        this.fechaFirmaStr = fechaFirmaStr;
    }

    public String getHashFirma() {
        return hashFirma;
    }

    public void setHashFirma(String hashFirma) {
        this.hashFirma = hashFirma;
    }

    public String getAgenteNavegador() {
        return agenteNavegador;
    }

    public void setAgenteNavegador(String agenteNavegador) {
        this.agenteNavegador = agenteNavegador;
    }

    
    
}
