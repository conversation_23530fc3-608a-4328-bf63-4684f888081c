/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.commons.firmese.enumerados;

/**
 *
 * <AUTHOR>
 */
public enum EEstadoArchivoFirma {
    PENDIENTE_FIRMA {
        @Override
        public int getId() {
            return 1;
        }

        @Override
        public String getName() {
            return "Pendiente Firmar";
        }
    },
    FIRMADO {
        @Override
        public int getId() {
            return 2;
        }

        @Override
        public String getName() {
            return "Firmado";
        }
    }
    ;

    public abstract int getId();
    public abstract String getName();


  
    
}
