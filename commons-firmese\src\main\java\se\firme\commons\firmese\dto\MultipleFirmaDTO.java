package se.firme.commons.firmese.dto;

import java.util.List;

public class MultipleFirmaDTO {
	private List<FirmanteDTO> firmantes;
	private List<FirmaRequestDTO> documentos;
	private String tipoFirma;
	private String fechaVigencia;

	public List<FirmanteDTO> getFirmantes() {
		return firmantes;
	}

	public void setFirmantes(List<FirmanteDTO> firmantes) {
		this.firmantes = firmantes;
	}

	public List<FirmaRequestDTO> getDocumentos() {
		return documentos;
	}

	public void setDocumentos(List<FirmaRequestDTO> documentos) {
		this.documentos = documentos;
	}

	public String getTipoFirma() {
		return tipoFirma;
	}

	public void setTipoFirma(String tipoFirma) {
		this.tipoFirma = tipoFirma;
	}

	public String getFechaVigencia() {
		return fechaVigencia;
	}

	public void setFechaVigencia(String fechaVigencia) {
		this.fechaVigencia = fechaVigencia;
	}

	@Override
	public String toString() {
		return "MultipleFirmaDTO [tipoFirma=" + tipoFirma + "]";
	}

	

}
