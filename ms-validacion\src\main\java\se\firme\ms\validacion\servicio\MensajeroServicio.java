package se.firme.ms.validacion.servicio;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import se.firme.commons.firmese.dto.MensajeDTO;
import se.firme.ms.validacion.client.MensajeroClient;



@Component
public class MensajeroServicio {
	@Autowired
	private MensajeroClient mensajeroClient;
	
	public boolean notificar(String string, String numeroCelular,String proveedor) {
		try {
			MensajeDTO dto = new MensajeDTO();
			dto.setCelular(numeroCelular);
			dto.setMensaje(string);
			dto.setPais("CO");
			dto.setTipoProveedor(proveedor);
			mensajeroClient.send(dto);
			return true;
		} catch (Exception e) {
			System.err.println(e.getMessage());
			return false;
		}
	}
}
