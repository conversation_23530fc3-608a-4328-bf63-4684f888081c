/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.commons.firmese.enumerados;

/**
 *
 * <AUTHOR>
 */
public enum EParametro {
    CORREO_SMTP {
        @Override
        public String getId() {
            return "CORREO_SMTP";
        }
    },
    CORREO_PUERTO {
        @Override
        public String getId() {
            return "CORREO_PUERTO";
        }
    },
    CORREO_REMITENTE {
        @Override
        public String getId() {
            return "CORREO_REMITENTE";
        }
    },
    CORREO_CLAVE {
        @Override
        public String getId() {
            return "CORREO_CLAVE";
        }
    },
    HOST_DE_DESPLIEGUE {
        @Override
        public String getId() {
            return "HOST_DE_DESPLIEGUE";
        }
    },
    CLIENTE_KONIVIN_CLAVE{
        @Override
        public String getId() {
            return "CLIENTE_KONIVIN_CLAVE";
        }
    },
    CLIENTE_KONIVIN_USUARIO{
        @Override
        public String getId() {
            return "CLIENTE_KONIVIN_USUARIO";
        }
    },
    FUENTE_ESTADO_CEDULA_CODIGO{
        @Override
        public String getId() {
            return "FUENTE_ESTADO_CEDULA_CODIGO";
        }
    },
    FUENTE_ESTADO_CEDULA_PATRON_FECHA{
        @Override
        public String getId() {
            return "FUENTE_ESTADO_CEDULA_PATRON_FECHA";
        }
    },
    KONIVIN_URL_SERVICIO_FUENTES{
        @Override
        public String getId() {
            return "KONIVIN_URL_SERVICIO_FUENTES";
        }
    },
    FIRMAS_RUTA_PRINCIPAL{
        @Override
        public String getId() {
            return "FIRMAS_RUTA_PRINCIPAL";
        }
    },
    HASH_ALGORITMO{
        @Override
        public String getId() {
            return "HASH_ALGORITMO";
        }
    },
    ;

    public abstract String getId();


    public static boolean isParametrazada(String id) {
        boolean rta = false;
        for (int i = 0; i < EParametro.values().length; i++) {
            EParametro get = EParametro.values()[i];

            if (get.getId().equals(id)) {
                rta = true;
                break;
            }
        }
        return rta;
    }
    
}
