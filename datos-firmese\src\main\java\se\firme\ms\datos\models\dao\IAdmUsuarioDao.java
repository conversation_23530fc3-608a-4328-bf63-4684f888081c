package se.firme.ms.datos.models.dao;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import se.firme.ms.datos.models.entity.AdmUsuarioDf;

@Repository
public interface IAdmUsuarioDao extends CrudRepository<AdmUsuarioDf, Long> {
    @Query(value = "SELECT * FROM gestion_adm.adm_usuario WHERE eMailUsuario = ?1 LIMIT 1", nativeQuery = true)
    AdmUsuarioDf findByeMailUsuario(String eMailUsuario);

    @Modifying
    @Transactional
    @Query(value = "INSERT INTO gestion_adm.adm_usuario " +
            "(idUsuario, nombreUsuario, telefonoUsuario, direccionUsuario, claveUsuario, activoUsuario, eMailUsuario, client_id, idUsuarioEnCliente) " +
            "VALUES (:idUsuario, :nombreUsuario, :telefonoUsuario, :direccionUsuario, :claveUsuario, :activoUsuario, :eMailUsuario, :client_id, :idUsuarioEnCliente)", nativeQuery = true)
    void insertarAdmUsuario(
        @org.springframework.data.repository.query.Param("idUsuario") String idUsuario,
        @org.springframework.data.repository.query.Param("nombreUsuario") String nombreUsuario,
        @org.springframework.data.repository.query.Param("telefonoUsuario") String telefonoUsuario,
        @org.springframework.data.repository.query.Param("direccionUsuario") String direccionUsuario,
        @org.springframework.data.repository.query.Param("claveUsuario") String claveUsuario,
        @org.springframework.data.repository.query.Param("activoUsuario") Integer activoUsuario,
        @org.springframework.data.repository.query.Param("eMailUsuario") String eMailUsuario,
        @org.springframework.data.repository.query.Param("client_id") String client_id,
        @org.springframework.data.repository.query.Param("idUsuarioEnCliente") Long idUsuarioEnCliente
    );
}