/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.models.service;

import se.firme.ms.models.service.interfaz.ICatalogo;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import se.firme.ms.datos.models.dao.ITipoDocumentoDao;
import se.firme.ms.datos.models.entity.TipoDocumento;

/**
 *
 * <AUTHOR>
 */
@Service
public class CatalogoServiceImpl implements ICatalogo{

    @Autowired
    ITipoDocumentoDao tipoDocumentoDao;
        
    @Override
    public List<TipoDocumento> getTipoDocumentoActivo() {
        return tipoDocumentoDao.findTipoDocumentoActivo();
    }
    
}
