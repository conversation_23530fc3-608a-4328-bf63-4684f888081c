/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.datos.models.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
@Entity
@Table(name = "proceso_firma")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "ProcesoFirma.findAll", query = "SELECT p FROM ProcesoFirma p")
    , @NamedQuery(name = "ProcesoFirma.findByIdProcesoFirma", query = "SELECT p FROM ProcesoFirma p WHERE p.idProcesoFirma = :idProcesoFirma")
    , @NamedQuery(name = "ProcesoFirma.findByFechaRegistro", query = "SELECT p FROM ProcesoFirma p WHERE p.fechaRegistro = :fechaRegistro")
    , @NamedQuery(name = "ProcesoFirma.findByFechaVencimiento", query = "SELECT p FROM ProcesoFirma p WHERE p.fechaVencimiento = :fechaVencimiento")
    , @NamedQuery(name = "ProcesoFirma.findByCodigoSms", query = "SELECT p FROM ProcesoFirma p WHERE p.codigoSms = :codigoSms")
    , @NamedQuery(name = "ProcesoFirma.findByActivo", query = "SELECT p FROM ProcesoFirma p WHERE p.activo = :activo")})
public class ProcesoFirma implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id_proceso_firma")
    private Long idProcesoFirma;
    @Basic(optional = false)
    @NotNull
    @Column(name = "fecha_registro")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaRegistro;
    @Basic(optional = false)
    @NotNull
    @Column(name = "fecha_vencimiento")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaVencimiento;
    @Basic(optional = false)
    @NotNull
    @Size(min = 1, max = 10)
    @Column(name = "otp")
    private String codigoSms;
    @Basic(optional = false)
    @NotNull
    @Column(name = "activo")
    private boolean activo;
    @JoinColumn(name = "id_usuario", referencedColumnName = "id_usuario")
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private Usuario idUsuario;
    @Column(name = "token")
    private String token;

    public ProcesoFirma() {
    }

    public ProcesoFirma(Long idProcesoFirma) {
        this.idProcesoFirma = idProcesoFirma;
    }

    public ProcesoFirma(Long idProcesoFirma, Date fechaRegistro, Date fechaVencimiento, String codigoSms, boolean activo) {
        this.idProcesoFirma = idProcesoFirma;
        this.fechaRegistro = fechaRegistro;
        this.fechaVencimiento = fechaVencimiento;
        this.codigoSms = codigoSms;
        this.activo = activo;
    }

    public Long getIdProcesoFirma() {
        return idProcesoFirma;
    }

    public void setIdProcesoFirma(Long idProcesoFirma) {
        this.idProcesoFirma = idProcesoFirma;
    }

    public Date getFechaRegistro() {
        return fechaRegistro;
    }

    public void setFechaRegistro(Date fechaRegistro) {
        this.fechaRegistro = fechaRegistro;
    }

    public Date getFechaVencimiento() {
        return fechaVencimiento;
    }

    public void setFechaVencimiento(Date fechaVencimiento) {
        this.fechaVencimiento = fechaVencimiento;
    }

    public String getCodigoSms() {
        return codigoSms;
    }

    public void setCodigoSms(String codigoSms) {
        this.codigoSms = codigoSms;
    }

   

    public Usuario getIdUsuario() {
        return idUsuario;
    }

    public void setIdUsuario(Usuario idUsuario) {
        this.idUsuario = idUsuario;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (idProcesoFirma != null ? idProcesoFirma.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof ProcesoFirma)) {
            return false;
        }
        ProcesoFirma other = (ProcesoFirma) object;
        if ((this.idProcesoFirma == null && other.idProcesoFirma != null) || (this.idProcesoFirma != null && !this.idProcesoFirma.equals(other.idProcesoFirma))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "se.firme.ms.datos.models.entity.ProcesoFirma[ idProcesoFirma=" + idProcesoFirma + " ]";
    }

    public boolean isActivo() {
        return activo;
    }

    public void setActivo(boolean activo) {
        this.activo = activo;
    }

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

    
}
