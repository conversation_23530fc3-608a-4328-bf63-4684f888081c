package se.firme.ms.usuario.rest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import se.firme.commons.firmese.dto.ApiResponse;
import se.firme.commons.firmese.dto.RegistroTemporalDTO;
import se.firme.ms.usuario.negocio.RegistroTemporalNegocio;

@RestController
@RefreshScope
@RequestMapping("/tmp")
public class RegistroTemporalController {
	@Autowired
	private RegistroTemporalNegocio registro;

	 @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	    public ResponseEntity<ApiResponse> registrar(@RequestBody RegistroTemporalDTO datosRegistro) {
	        try {
	            registro.agregarRegistro(datosRegistro);
	            return new ResponseEntity<>(new ApiResponse.ok().data("Registro agregado correctamente").build(), HttpStatus.OK);
	        } catch (Exception e) {
	        	System.out.println("ERR: "+e.getMessage());
	            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
	        }
	    }
}
