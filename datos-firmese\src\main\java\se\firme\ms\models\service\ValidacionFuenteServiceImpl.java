/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.models.service;

import se.firme.ms.models.service.interfaz.IValidacionFuenteService;
import se.firme.ms.models.service.interfaz.IParametroService;
import com.venko.clientekonivin.dto.SolicitudDTO;
import com.venko.clientekonivin.restClient.KonivinClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import se.firme.commons.firmese.dto.EndpointDTO;
import se.firme.commons.firmese.util.Parameters;
import se.firme.commons.firmese.util.Utilities;
import se.firme.ms.datos.models.dao.IUsuarioDao;
import se.firme.ms.datos.models.entity.Usuario;

/**
 *
 * <AUTHOR>
 */
@Service
public class ValidacionFuenteServiceImpl implements IValidacionFuenteService {

    @Autowired
    IParametroService parametroService;
    
    @Autowired
    IUsuarioDao usuarioDao;

    @Override
    @Transactional
    public void validarUsuarioEnRegistraduria(Usuario usuario,EndpointDTO endpointDTO) throws Exception {
        Usuario usuariodataVerificada = procesarDatosRespuestaFuente(usuario,endpointDTO);
        
        usuarioDao.actualizarVerificacionEnFuente(usuariodataVerificada.getVerificadoFuente(), usuariodataVerificada.getFechaVerificacion(), usuariodataVerificada.getObervacionVerificacion(), usuariodataVerificada.getNombreCompleto(), usuario.getIdUsuario());
    }
    
    private Usuario procesarDatosRespuestaFuente(Usuario usuario,EndpointDTO endpointDTO) throws Exception{
        Usuario usuariodataVerificada = new Usuario();
        String estadoDocumento = "";
        String json = consultarFuente(usuario,endpointDTO);
        String fuenteFallo = Utilities.getAtributoDeJson(json, "fuenteFallo");

        if (Utilities.isVacio(fuenteFallo)) {
            usuariodataVerificada.setObervacionVerificacion("No fué posible identificar el estado de la consulta de la fuente");
        } else {
            if (fuenteFallo.trim().toLowerCase().equals("si")) {
                usuariodataVerificada.setObervacionVerificacion("Falló la consulta de la fuente");
            } else {
                estadoDocumento = Utilities.getAtributoDeJson(json, "estado");
                if (!Utilities.isVacio(estadoDocumento) && estadoDocumento.trim().toLowerCase().equals("vigente")) {
                    String nombreCompleto = Utilities.getJsonString(Utilities.getJsonObject(Utilities.getJsonObject(Utilities.getJsonObject(json, "personaVO"), "nombres"), "ESTADO-CEDULA-COLOMBIA"), "primerNombre");
                    if (!Utilities.isVacio(nombreCompleto)) {
                        usuariodataVerificada.setVerificadoFuente(true);
                        usuariodataVerificada.setObervacionVerificacion("Documento verificado en fuente");
                    } else {
                        usuariodataVerificada.setObervacionVerificacion("No fué posible identificar el nombre completo de la persona");
                        nombreCompleto = "";
                    }
                    usuariodataVerificada.setNombreCompleto(nombreCompleto);
                } else {
                    usuariodataVerificada.setObervacionVerificacion("El estado del documento no es válido [" + estadoDocumento + "]");
                }

            }
        }        
        
        return usuariodataVerificada;
    }

    private String consultarFuente(Usuario usuario, EndpointDTO endpointDTO) throws Exception {

        KonivinClient cliente = new KonivinClient(endpointDTO.getUrl());
        SolicitudDTO datosSolicitud = new SolicitudDTO();
        datosSolicitud.setLcy(endpointDTO.getUser()); //Asignar aquí el nombre de usuario para autenticación
        datosSolicitud.setVpv(endpointDTO.getPasswd()); //Asignar aquí contraseña de usuario para autenticación
        if(usuario.getIdTipoDocumento().getIdTipoDocumento().trim().toLowerCase().equals("ce")){
            datosSolicitud.setJor(Parameters.getParameter(Parameters.FUENTE_ESTADO_CEDULA_EXTRANJERIA_CODIGO)); //asignar aquí código de la fuente, para este ejemplo se usará el código de fuente nombreCompleto
            datosSolicitud.setIcf("03");
        } else {
            datosSolicitud.setJor(Parameters.getParameter(Parameters.FUENTE_ESTADO_CEDULA_CODIGO)); //asignar aquí código de la fuente, para este ejemplo se usará el código de fuente nombreCompleto
            datosSolicitud.setIcf("01");
        }
        datosSolicitud.setThy("co");
        datosSolicitud.setKlm(usuario.getNumeroDocumento()); //Asigna aquí el número de documento de identidad de la persona que se va a consultar
        datosSolicitud.setHho("");
        datosSolicitud.setHgu(Utilities.getFechaDateAFechaTexto(usuario.getFechaExpedicionDocumento(), "yyyy-MM-dd"));

        return cliente.consultar(String.class, datosSolicitud);
    }
}
