package se.firme.ms.datos.models.entity;

import java.io.Serializable;
import javax.persistence.*;

@Entity
@Table(name = "usuario_firma_archivo_otp")
public class UsuarioFirmaArchivoOtp implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "idusuario_firma_archivo_otp")
    private Long idUsuarioOtpFirmaArchivo;
    
    @JoinColumn(name = "id_proceso_firma", referencedColumnName = "id_proceso_firma")
    @ManyToOne(optional = false)
    private ProcesoFirma idProcesoFirma;
    
    @JoinColumn(name = "id_archivo_firma", referencedColumnName = "id_archivo_firma")
    @ManyToOne(optional = false)
    private ArchivoFirma idArchivoFirma;
    
    @Column(name = "estado")
    private int estado = 1;
    
    // Constructores, getters y setters
    public UsuarioFirmaArchivoOtp() {
    }
    
    public UsuarioFirmaArchivoOtp(ProcesoFirma idProcesoFirma, ArchivoFirma idArchivoFirma) {
        this.idProcesoFirma = idProcesoFirma;
        this.idArchivoFirma = idArchivoFirma;
    }
    
    // Getters y setters
    public Long getIdUsuarioOtpFirmaArchivo() {
        return idUsuarioOtpFirmaArchivo;
    }
    
    public void setIdUsuarioOtpFirmaArchivo(Long idUsuarioOtpFirmaArchivo) {
        this.idUsuarioOtpFirmaArchivo = idUsuarioOtpFirmaArchivo;
    }
    
    public ProcesoFirma getIdProcesoFirma() {
        return idProcesoFirma;
    }
    
    public void setIdProcesoFirma(ProcesoFirma idProcesoFirma) {
        this.idProcesoFirma = idProcesoFirma;
    }
    
    public ArchivoFirma getIdArchivoFirma() {
        return idArchivoFirma;
    }
    
    public void setIdArchivoFirma(ArchivoFirma idArchivoFirma) {
        this.idArchivoFirma = idArchivoFirma;
    }
    
    public int getEstado() {
        return estado;
    }
    
    public void setEstado(int estado) {
        this.estado = estado;
    }
}