/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.models.service.interfaz;

import java.util.Map;
import se.firme.ms.datos.models.entity.Usuario;

/**
 *
 * <AUTHOR>
 */
public interface IEmailService {
    public void enviarTokenRegistroMail(Map<String, String> parametros, Usuario usuario);
}
