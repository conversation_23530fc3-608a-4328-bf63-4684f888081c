package se.firme.cli.utils.commands;

import org.springframework.stereotype.Component;
import se.firme.commons.firmese.util.Parameters;
import se.firme.ms.datos.models.dao.IArchivoFirmaDao;
import se.firme.ms.datos.models.dao.ISolicitudFirmaDao;
import se.firme.ms.datos.models.dao.ITokenDao;
import se.firme.ms.datos.models.entity.ArchivoFirma;
import se.firme.ms.datos.models.entity.SolicitudFirma;
import se.firme.ms.datos.models.entity.Token;

import java.util.Arrays;
import java.util.List;
import java.util.logging.Logger;

@Component
public class MigrateSignRequestsCommand {
    private static Logger LOG = Logger.getLogger(MigrateSignRequestsCommand.class.getName());


    private final ITokenDao tokenDao;

    private final ISolicitudFirmaDao solicitudFirmaDao;

    private final IArchivoFirmaDao archivoFirmaDao;

    public MigrateSignRequestsCommand(ITokenDao tokenDao, ISolicitudFirmaDao solicitudFirmaDao, IArchivoFirmaDao archivoFirmaDao) {
        this.tokenDao = tokenDao;
        this.solicitudFirmaDao = solicitudFirmaDao;
        this.archivoFirmaDao = archivoFirmaDao;
    }

    public void run() throws Exception {
        List<Token> activeRequests = tokenDao.findAllByActivoTrueAndTipo(Parameters.string.TOKEN_TIPO_FIRMA_DOCUMENTO);

        LOG.info("Found " + activeRequests.size() + " active sign requests");

        for (Token token : activeRequests) {
            LOG.info("Migrating token " + token.getIdToken());

            try {
                int[] fileIds = Arrays.stream(token.getIds()
                                .split("-"))
                        .filter(id -> !id.isEmpty())
                        .mapToInt(Integer::parseInt)
                        .filter(id -> id > 0)
                        .toArray();

                for (int fileId : fileIds) {
                    try {
                        ArchivoFirma archivoFirma = archivoFirmaDao.findById((long) fileId).orElse(null);

                        if (archivoFirma == null) {
                            LOG.warning("File with id " + fileId + " not found");
                            continue;
                        }

                        if (archivoFirma.getEstado() == Parameters.string.ESTADO_ARCHIVO_FIRMADO) {
                            LOG.warning("File with id" + fileId + " already signed");
                            continue;
                        }

                        boolean exists = solicitudFirmaDao.findByIdArchivoFirmaAndEmailFirmanteAndFirmadoIsFalse(fileId, token.getEmailFirmante()) != null;

                        if (exists) {
                            LOG.warning("File with id " + fileId + " already migrated");
                            continue;
                        }

                        SolicitudFirma solicitudFirma = new SolicitudFirma();
                        solicitudFirma.setEmailFirmante(token.getEmailFirmante());
                        solicitudFirma.setIdUsuario(token.getIdUsuario().getIdUsuario());
                        solicitudFirma.setIdArchivoFirma(fileId);
                        solicitudFirma.setFechaRegistro(token.getFechaRegistro());
                        solicitudFirma.setFechaVencimiento(token.getFechaVencimiento());
                        solicitudFirma = solicitudFirmaDao.save(solicitudFirma);

                        if (solicitudFirma.getFechaRegistro() != token.getFechaRegistro()) {
                            solicitudFirma.setFechaRegistro(token.getFechaRegistro());
                            solicitudFirmaDao.save(solicitudFirma);
                        }

                        LOG.info("File with id " + fileId + " migrated successfully");
                    } catch (Exception e) {
                        LOG.warning("Failed to migrate file with id " + fileId + ": " + e.getMessage());
                    }
                }

            } catch (Exception e) {
                LOG.warning("Failed to migrate token " + token.getIdToken() + ": " + e.getMessage());
            }
        }
    }

}
