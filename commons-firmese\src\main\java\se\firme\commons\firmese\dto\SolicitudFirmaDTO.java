package se.firme.commons.firmese.dto;

import java.util.List;

public class SolicitudFirmaDTO {
	private String solicitante;
	private String email;
	private List<ArchivoFirmaResponseDTO> archivos;
	private boolean nuevo;

	public SolicitudFirmaDTO() {
	}
	public SolicitudFirmaDTO(boolean nuevo) {
		this.nuevo=nuevo;
	}

	public String getSolicitante() {
		return solicitante;
	}

	public void setSolicitante(String solicitante) {
		this.solicitante = solicitante;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public List<ArchivoFirmaResponseDTO> getArchivos() {
		return archivos;
	}

	public void setArchivos(List<ArchivoFirmaResponseDTO> archivos) {
		this.archivos = archivos;
	}

	public SolicitudFirmaDTO archivos(List<ArchivoFirmaResponseDTO> lista) {
		this.archivos=lista;
		return this;
	}

	public SolicitudFirmaDTO solicitante(String nombreCompleto) {
		this.solicitante=nombreCompleto;
		return this;
	}

	public SolicitudFirmaDTO email(String correoElectronico) {
		this.email=correoElectronico;
		return this;
	}
	
	public SolicitudFirmaDTO build() {
		return this;
	}

	public boolean isNuevo() {
		return nuevo;
	}

	public void setNuevo(boolean nuevo) {
		this.nuevo = nuevo;
	}

}
