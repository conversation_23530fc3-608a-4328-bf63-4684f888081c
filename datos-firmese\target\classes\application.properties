# To change this license header, choose License Headers in Project Properties.
# To change this template file, choose Too<PERSON> | Templates
# and open the template in the editor.

# Datasource configuration

spring.datasource.url=**************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.ddl-auto=none

#logging.level.org.hibernate.SQL=debug