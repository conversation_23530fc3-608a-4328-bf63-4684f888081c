/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.usuario.negocio;

import se.firme.commons.exception.FirmaException;
import se.firme.commons.firmese.dto.DocumentoDTO;
import se.firme.commons.firmese.dto.RegistroDTO;
import se.firme.commons.firmese.dto.ServicioDTO;
import se.firme.commons.models.projection.IUsuario;

/**
 *
 * <AUTHOR>
 */
public interface IUsuarioNegosioService {

    public void procesoRegistro(RegistroDTO datosRegistro) throws Exception;

    public boolean completarRegistro(DocumentoDTO documento) throws FirmaException;

    public ServicioDTO getUsuarioServicio(long codigo) throws FirmaException;

    public Object agregarPaqueteServicio(String json) throws FirmaException;

	public boolean cambiarContrasena(String body) throws FirmaException;

	public IUsuario consultarUsuario(String id);

}
