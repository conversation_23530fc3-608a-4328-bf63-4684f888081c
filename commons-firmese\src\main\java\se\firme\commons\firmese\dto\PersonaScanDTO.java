/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.commons.firmese.dto;

import java.io.Serializable;

/**
 * @document PersonaScanDTO
 * <AUTHOR>
 * @fecha miércoles, septiembre 30 de 2020, 10:36:04 AM
 */
public class PersonaScanDTO implements Serializable {

    public PersonaScanDTO() {
    }

    private String documentID;
    private String surename;
    private String secondSurename;
    private String firstName;
    private String secondName;
    private String birthday;
    private String bloodType;
    private String cedulaType;
    private String codeAfis;
    private String decadentCard;
    private String department;
    private String placeBirth;
    private String gender;
    private String municipality;

    public String getDocumentID() {
        return documentID;
    }

    public void setDocumentID(String documentID) {
        this.documentID = documentID;
    }

    public String getSurename() {
        return surename;
    }

    public void setSurename(String surename) {
        this.surename = surename;
    }

    public String getSecondSurename() {
        return secondSurename;
    }

    public void setSecondSurename(String secondSurename) {
        this.secondSurename = secondSurename;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getSecondName() {
        return secondName;
    }

    public void setSecondName(String secondName) {
        this.secondName = secondName;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getBloodType() {
        return bloodType;
    }

    public void setBloodType(String bloodType) {
        this.bloodType = bloodType;
    }

    public String getCedulaType() {
        return cedulaType;
    }

    public void setCedulaType(String cedulaType) {
        this.cedulaType = cedulaType;
    }

    public String getCodeAfis() {
        return codeAfis;
    }

    public void setCodeAfis(String codeAfis) {
        this.codeAfis = codeAfis;
    }

    public String getDecadentCard() {
        return decadentCard;
    }

    public void setDecadentCard(String decadentCard) {
        this.decadentCard = decadentCard;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getPlaceBirth() {
        return placeBirth;
    }

    public void setPlaceBirth(String placeBirth) {
        this.placeBirth = placeBirth;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getMunicipality() {
        return municipality;
    }

    public void setMunicipality(String municipality) {
        this.municipality = municipality;
    }
    
    
}
