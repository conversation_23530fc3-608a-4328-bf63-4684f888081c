package se.firme.ms.models.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import se.firme.commons.exception.FirmaException;
import se.firme.ms.datos.models.dto.FirmanteOrdenDTO;
import se.firme.ms.datos.models.entity.ArchivoFirma;
import se.firme.ms.datos.models.entity.Token;
import se.firme.ms.datos.models.entity.Usuario;
import se.firme.ms.models.service.interfaz.IUsuarioService;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Service
public class TokenGenerationService {
    
    private static Logger logger = Logger.getLogger(TokenGenerationService.class.getName());
    
    @Autowired
    private TokenServiceImpl tokenService;
    
    @Autowired
    private EmailNotificationService emailNotificationService;
    
    @Autowired
    private UsuarioValidationService usuarioValidationService;

    @Autowired
    private ArchivoFirmaServiceImpl archivoFirmaService;

    @Autowired
    private IUsuarioService usuarioService;

    /**
     * Genera tokens agrupados para múltiples documentos/plantillas por firmante
     * CORREGIDO: Manejo correcto de firma secuencial
     */
    public Map<String, String> generarTokensAgrupadosParaFirmantes(List<Long> idsArchivos, Long idUsuario, 
        List<FirmanteOrdenDTO> firmantes, String fechaVigencia, List<String> nombresArchivos, 
        List<String> rutasCompletas) throws FirmaException {

        try {
            logger.info("Generando tokens únicos para " + (firmantes != null ? firmantes.size() : 0) + 
                       " firmante(s) y " + (idsArchivos != null ? idsArchivos.size() : 0) + " documento(s)");
            
            // Validaciones de entrada
            if (idsArchivos == null || idsArchivos.isEmpty()) {
                throw new FirmaException("Lista de IDs de archivos no puede estar vacía");
            }
            
            if (firmantes == null || firmantes.isEmpty()) {
                throw new FirmaException("Lista de firmantes no puede estar vacía");
            }
            
            if (idUsuario == null) {
                throw new FirmaException("ID de usuario no puede ser null");
            }
            
            Date fechaVigenciaDate = null;
            if (fechaVigencia != null && !fechaVigencia.trim().isEmpty()) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                fechaVigenciaDate = sdf.parse(fechaVigencia);
                logger.info("Fecha de vigencia configurada: " + fechaVigenciaDate);
            }
            
            // **🎯 DETECTAR TIPO DE ORDEN PARA MANEJO CORRECTO**
            String tipoOrden = detectarTipoOrden(firmantes);
            logger.info("🔍 Tipo de orden detectado: " + tipoOrden);
            
            if ("SECUENCIAL".equals(tipoOrden)) {
                logger.info("🔄 PROCESANDO FIRMA SECUENCIAL - Solo firmantes del primer orden recibirán tokens");
                return procesarFirmaSecuencial(idsArchivos, idUsuario, firmantes, fechaVigenciaDate, nombresArchivos, rutasCompletas);
            } else {
                logger.info("🔄 PROCESANDO FIRMA PARALELA - Todos los firmantes recibirán tokens");
                return procesarFirmaParalela(idsArchivos, idUsuario, firmantes, fechaVigenciaDate, nombresArchivos, rutasCompletas);
            }
            
        } catch (Exception e) {
            logger.severe("Error generando tokens únicos: " + e.getMessage());
            e.printStackTrace();
            throw new FirmaException("Error generando tokens únicos: " + e.getMessage());
        }
    }

    /**
     * Detecta el tipo de orden basado en los firmantes
     */
    private String detectarTipoOrden(List<FirmanteOrdenDTO> firmantes) {
        if (firmantes == null || firmantes.isEmpty()) {
            return "PARALELO"; // Default
        }
        
        // Verificar si todos tienen orden 1 (PARALELO) o hay órdenes diferentes (SECUENCIAL)
        Set<Integer> ordenes = new HashSet<>();
        for (FirmanteOrdenDTO firmante : firmantes) {
            ordenes.add(firmante.getOrden());
        }
        
        if (ordenes.size() == 1 && ordenes.contains(1)) {
            return "PARALELO";
        } else {
            return "SECUENCIAL";
        }
    }

    /**
     * Procesa firma secuencial - Solo firmantes del primer orden reciben tokens
     */
    private Map<String, String> procesarFirmaSecuencial(List<Long> idsArchivos, Long idUsuario, 
        List<FirmanteOrdenDTO> firmantes, Date fechaVigenciaDate, List<String> nombresArchivos, 
        List<String> rutasCompletas) throws FirmaException {

        Map<String, String> tokensPorFirmante = new HashMap<>();

        try {
            logger.info("=== PROCESANDO FIRMA SECUENCIAL ===");

            // Separar TyC y normales
            List<Long> idsPlantillasTyC = new ArrayList<>();
            List<Long> idsDocumentosNormales = new ArrayList<>();
            Map<Long, String> mapaNombresArchivos = new HashMap<>();
            Map<Long, String> mapaRutasCompletas = new HashMap<>();

            for (int i = 0; i < idsArchivos.size(); i++) {
                Long idArchivo = idsArchivos.get(i);
                String nombreArchivo = i < nombresArchivos.size() ? nombresArchivos.get(i) : "";
                String rutaCompleta = i < rutasCompletas.size() ? rutasCompletas.get(i) : "";

                mapaNombresArchivos.put(idArchivo, nombreArchivo);
                mapaRutasCompletas.put(idArchivo, rutaCompleta);

                if (esPlantillaTyC(idArchivo, nombreArchivo)) {
                    idsPlantillasTyC.add(idArchivo);
                } else {
                    idsDocumentosNormales.add(idArchivo);
                }
            }

            boolean hayPlantillasTyC = !idsPlantillasTyC.isEmpty();
            boolean hayDocumentosNormales = !idsDocumentosNormales.isEmpty();

            // Mapear TyC individuales por firmante
            Map<String, List<Long>> tycPorFirmante = new HashMap<>();
            if (hayPlantillasTyC) {
                tycPorFirmante = mapearTyCPorFirmante(idsPlantillasTyC, firmantes, mapaRutasCompletas);
            }

            // Encontrar el primer orden activo
            int primerOrden = firmantes.stream()
                .mapToInt(FirmanteOrdenDTO::getOrden)
                .min()
                .orElse(1);

            logger.info("🔍 Primer orden detectado: " + primerOrden);

            // Generar tokens para TODOS los firmantes (de todos los órdenes)
            for (FirmanteOrdenDTO firmante : firmantes) {
                boolean tieneTyC = false;
                try {
                    Usuario usuario = usuarioService.findByEmail(firmante.getEmail());
                    if (usuario != null && usuario.getFirmadoTyc()) {
                        tieneTyC = true;
                        logger.info("ℹ️ Usuario ya firmó TyC: " + firmante.getEmail());
                    }
                } catch (Exception e) {
                    logger.warning("Error verificando firmado_tyc para usuario: " + firmante.getEmail() + " - " + e.getMessage());
                }

                List<Long> documentosParaEsteFirmante = new ArrayList<>();
                List<String> nombresParaEsteFirmante = new ArrayList<>();
                List<String> rutasParaEsteFirmante = new ArrayList<>();

                // AGREGAR TyC individuales si el usuario NO tiene TyC firmado
                if (!tieneTyC && hayPlantillasTyC) {
                    List<Long> tycDelFirmante = tycPorFirmante.get(firmante.getEmail());
                    if (tycDelFirmante != null && !tycDelFirmante.isEmpty()) {
                        for (Long idTyC : tycDelFirmante) {
                            documentosParaEsteFirmante.add(idTyC);
                            nombresParaEsteFirmante.add(mapaNombresArchivos.get(idTyC));
                            rutasParaEsteFirmante.add(mapaRutasCompletas.get(idTyC));
                            logger.info("   ✅ TyC individual incluido: " + mapaNombresArchivos.get(idTyC) + " (ID: " + idTyC + ")");
                        }
                    } else {
                        logger.warning("⚠️ No se encontraron TyC individuales para: " + firmante.getEmail());
                    }
                }

                // AGREGAR documentos normales
                for (Long idDoc : idsDocumentosNormales) {
                    documentosParaEsteFirmante.add(idDoc);
                    nombresParaEsteFirmante.add(mapaNombresArchivos.get(idDoc));
                    rutasParaEsteFirmante.add(mapaRutasCompletas.get(idDoc));
                    logger.info("   ✅ Documento normal incluido: " + mapaNombresArchivos.get(idDoc) + " (ID: " + idDoc + ")");
                }

                if (!documentosParaEsteFirmante.isEmpty()) {
                    String idsParaToken = "0-" + documentosParaEsteFirmante.stream()
                        .filter(Objects::nonNull)
                        .map(String::valueOf)
                        .filter(s -> !s.trim().isEmpty())
                        .collect(Collectors.joining("-")) + "-0";
                    String tokenUnico = tokenService.crearTokenFirmante(
                        idUsuario,
                        idsParaToken,
                        firmante.getEmail(),
                        fechaVigenciaDate
                    );
                    tokensPorFirmante.put(firmante.getEmail(), tokenUnico);

                    // Solo enviar el correo si es del primer orden
                    if (firmante.getOrden() == primerOrden) {
                        emailNotificationService.enviarNotificacionAgrupada(
                            tokenUnico, firmante.getEmail(), nombresParaEsteFirmante, rutasParaEsteFirmante
                        );
                        logger.info("Correo enviado a: " + firmante.getEmail() + " con token: " + tokenUnico);
                    }
                } else {
                    logger.info("ℹ️ Firmante sin documentos a firmar: " + firmante.getEmail());
                }
            }

            logger.info("✅ Tokens secuenciales generados para todos los firmantes: " + tokensPorFirmante.size());
            logger.info("📧 Correos enviados solo a firmantes del orden activo");

            return tokensPorFirmante;

        } catch (Exception e) {
            logger.severe("Error procesando firma secuencial: " + e.getMessage());
            throw new FirmaException("Error procesando firma secuencial: " + e.getMessage());
        }
    }

    /**
     * Procesa firma paralela - Todos los firmantes reciben tokens
     */
    private Map<String, String> procesarFirmaParalela(List<Long> idsArchivos, Long idUsuario, 
        List<FirmanteOrdenDTO> firmantes, Date fechaVigenciaDate, List<String> nombresArchivos, 
        List<String> rutasCompletas) throws FirmaException {
        
        Map<String, String> tokensPorFirmante = new HashMap<>();
        
        try {
            logger.info("=== PROCESANDO FIRMA PARALELA ===");
            
            // Separar TyC y normales
            List<Long> idsPlantillasTyC = new ArrayList<>();
            List<Long> idsDocumentosNormales = new ArrayList<>();
            Map<Long, String> mapaNombresArchivos = new HashMap<>();
            Map<Long, String> mapaRutasCompletas = new HashMap<>();
            
            for (int i = 0; i < idsArchivos.size(); i++) {
                Long idArchivo = idsArchivos.get(i);
                String nombreArchivo = i < nombresArchivos.size() ? nombresArchivos.get(i) : "";
                String rutaCompleta = i < rutasCompletas.size() ? rutasCompletas.get(i) : "";
                
                mapaNombresArchivos.put(idArchivo, nombreArchivo);
                mapaRutasCompletas.put(idArchivo, rutaCompleta);
                
                if (esPlantillaTyC(idArchivo, nombreArchivo)) {
                    idsPlantillasTyC.add(idArchivo);
                } else {
                    idsDocumentosNormales.add(idArchivo);
                }
            }
            
            boolean hayPlantillasTyC = !idsPlantillasTyC.isEmpty();
            boolean hayDocumentosNormales = !idsDocumentosNormales.isEmpty();
            
            logger.info("📊 Análisis de documentos paralelos:");
            logger.info("   - Plantillas TyC: " + idsPlantillasTyC.size() + " " + idsPlantillasTyC);
            logger.info("   - Documentos normales: " + idsDocumentosNormales.size() + " " + idsDocumentosNormales);
            
            // Mapear TyC individuales por firmante
            Map<String, List<Long>> tycPorFirmante = new HashMap<>();
            if (hayPlantillasTyC) {
                tycPorFirmante = mapearTyCPorFirmante(idsPlantillasTyC, firmantes, mapaRutasCompletas);
            }
            
            // Generar tokens para TODOS los firmantes
            for (FirmanteOrdenDTO firmante : firmantes) {
                boolean tieneTyC = false;
                try {
                    Usuario usuario = usuarioService.findByEmail(firmante.getEmail());
                    if (usuario != null && usuario.getFirmadoTyc()) {
                        tieneTyC = true;
                    }
                } catch (Exception e) {
                    logger.warning("Error verificando firmado_tyc para usuario: " + firmante.getEmail() + " - " + e.getMessage());
                }

                List<Long> documentosParaEsteFirmante = new ArrayList<>();
                List<String> nombresParaEsteFirmante = new ArrayList<>();
                List<String> rutasParaEsteFirmante = new ArrayList<>();

                // AGREGAR TyC individuales si el usuario NO tiene TyC firmado
                if (!tieneTyC && hayPlantillasTyC) {
                    List<Long> tycDelFirmante = tycPorFirmante.get(firmante.getEmail());
                    if (tycDelFirmante != null && !tycDelFirmante.isEmpty()) {
                        for (Long idTyC : tycDelFirmante) {
                            documentosParaEsteFirmante.add(idTyC);
                            nombresParaEsteFirmante.add(mapaNombresArchivos.get(idTyC));
                            rutasParaEsteFirmante.add(mapaRutasCompletas.get(idTyC));
                            logger.info("   ✅ TyC individual incluido: " + mapaNombresArchivos.get(idTyC) + " (ID: " + idTyC + ")");
                        }
                    } else {
                        logger.warning("⚠️ No se encontraron TyC individuales para: " + firmante.getEmail());
                    }
                }

                // AGREGAR documentos normales
                for (Long idDoc : idsDocumentosNormales) {
                    documentosParaEsteFirmante.add(idDoc);
                    nombresParaEsteFirmante.add(mapaNombresArchivos.get(idDoc));
                    rutasParaEsteFirmante.add(mapaRutasCompletas.get(idDoc));
                    logger.info("   ✅ Documento normal incluido: " + mapaNombresArchivos.get(idDoc) + " (ID: " + idDoc + ")");
                }

                // GENERAR UN SOLO TOKEN CON TODOS LOS DOCUMENTOS PARA ESTE FIRMANTE
                if (!documentosParaEsteFirmante.isEmpty()) {
                    String idsParaToken = "0-" + documentosParaEsteFirmante.stream()
                        .filter(Objects::nonNull)
                        .map(String::valueOf)
                        .filter(s -> !s.trim().isEmpty())
                        .collect(Collectors.joining("-")) + "-0";
                    
                    String tokenUnico = tokenService.crearTokenFirmante(
                        idUsuario, 
                        idsParaToken,
                        firmante.getEmail(), 
                        fechaVigenciaDate
                    );
                    
                    if (tokenUnico != null && !tokenUnico.isEmpty()) {
                        tokensPorFirmante.put(firmante.getEmail(), tokenUnico);
                        
                        // ENVIAR UN SOLO CORREO CON TODOS LOS DOCUMENTOS
                        boolean enviado = emailNotificationService.enviarNotificacionAgrupada(
                            tokenUnico, firmante.getEmail(), nombresParaEsteFirmante, rutasParaEsteFirmante);

                        if (enviado) {
                            logger.info("✅ Token único enviado: " + firmante.getEmail() + 
                                    " (" + documentosParaEsteFirmante.size() + " documentos)");
                            logger.info("   📄 Documentos incluidos: " + nombresParaEsteFirmante);
                            logger.info("   🎯 IDs en token: " + idsParaToken);
                        } else {
                            logger.warning("⚠️ Error enviando token único a: " + firmante.getEmail());
                        }
                    }
                } else {
                    logger.info("ℹ️ Firmante sin documentos a firmar: " + firmante.getEmail());
                }
            }
            
            logger.info("✅ Tokens únicos generados exitosamente. Total firmantes notificados: " + tokensPorFirmante.size());
            logger.info("📧 Firmantes que recibieron UN SOLO correo cada uno: " + tokensPorFirmante.keySet());
            
            return tokensPorFirmante;
            
        } catch (Exception e) {
            logger.severe("Error procesando firma paralela: " + e.getMessage());
            throw new FirmaException("Error procesando firma paralela: " + e.getMessage());
        }
    }
    
    /**
     * Determina si un archivo es una plantilla TyC
     */
    private boolean esPlantillaTyC(Long idArchivo, String nombreArchivo) {
        try {
            // 1. Verificar por nombre de archivo
            if (nombreArchivo != null && !nombreArchivo.trim().isEmpty()) {
                String nombre = nombreArchivo.toLowerCase();
                if (nombre.contains("terminos") || 
                    nombre.contains("términos") ||
                    nombre.contains("tyc") ||
                    nombre.contains("condiciones") ||
                    nombre.contains("datos_personales") ||
                    nombre.contains("autorizacion") ||
                    nombre.contains("autorización")) {
                    return true;
                }
            }
            
            // 2. Verificar por ID de plantilla conocido (IDs 1 y 2 son TyC)
            if (idArchivo != null && (idArchivo.equals(1L) || idArchivo.equals(2L))) {
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            logger.warning("Error verificando si es plantilla TyC: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Normaliza email para comparación
     */
    private String normalizarEmail(String email) {
        if (email == null) return "";
        return email.toLowerCase().replaceAll("[^a-zA-Z0-9]", "_");
    }

    /**
     * Verifica si un TyC pertenece a un firmante específico usando mapeo exacto MEJORADO
     */
    private boolean perteneceEsteTyCAlFirmante(String rutaTyC, String emailOriginal, String emailNormalizado) {
        if (rutaTyC == null || rutaTyC.trim().isEmpty()) {
            return false;
        }
        
        String rutaNormalizada = rutaTyC.toLowerCase();
        
        logger.info("🔍 Verificando coincidencia:");
        logger.info("   - Ruta: " + rutaTyC);
        logger.info("   - Email original: " + emailOriginal);
        
        // **ESTRATEGIA 1**: Buscar email normalizado completo en la ruta (MÁS PRECISA)
        String emailParaDirectorio = emailOriginal.toLowerCase().replaceAll("[^a-zA-Z0-9]", "_");
        String patronDirectorio = "original_" + emailParaDirectorio + "/";
        
        if (rutaNormalizada.contains(patronDirectorio)) {
            logger.info("   ✅ Coincidencia EXACTA por patrón de directorio: " + patronDirectorio);
            return true;
        }
        
        // **ESTRATEGIA 2**: Buscar variaciones del email en la ruta (MEJORADA)
        String[] partesEmail = emailOriginal.split("@");
        if (partesEmail.length == 2) {
            String usuario = partesEmail[0].toLowerCase();
            String dominio = partesEmail[1].toLowerCase();
            
            // **PATRÓN 1**: usuario_dominio (ej: agfelix1629_gmail_com)
            String patronUsuarioDominio = usuario + "_" + dominio.replace(".", "_");
            if (rutaNormalizada.contains(patronUsuarioDominio)) {
                logger.info("   ✅ Coincidencia por patrón usuario_dominio: " + patronUsuarioDominio);
                return true;
            }
            
            // **PATRÓN 2**: Solo usuario si es específico y único (ej: agfelix1629)
            if (usuario.length() > 6) { // Solo usuarios específicos, no genéricos como "felix"
                if (rutaNormalizada.contains(usuario)) {
                    logger.info("   ✅ Coincidencia por usuario específico: " + usuario);
                    return true;
                }
            }
            
            // **ESTRATEGIA ESPECIAL PARA GMAIL**
            if (dominio.contains("gmail")) {
                // Patrón: usuario + gmail (ej: agfelix1629gmail)
                String patronGmail = usuario + "gmail";
                if (rutaNormalizada.contains(patronGmail)) {
                    logger.info("   ✅ Coincidencia por patrón Gmail: " + patronGmail);
                    return true;
                }
                
                // Patrón: usuario_gmail (ej: agfelix1629_gmail)
                String patronGmailGuion = usuario + "_gmail";
                if (rutaNormalizada.contains(patronGmailGuion)) {
                    logger.info("   ✅ Coincidencia por patrón Gmail con guión: " + patronGmailGuion);
                    return true;
                }
            }
        }
        
        logger.info("   ❌ No se encontró coincidencia para: " + emailOriginal);
        return false;
    }
    
    /**
     * Mapea TyC individuales por firmante with detailed logging
     */
    private Map<String, List<Long>> mapearTyCPorFirmante(List<Long> idsPlantillasTyC, 
                                                List<FirmanteOrdenDTO> firmantes, 
                                                Map<Long, String> mapaRutasCompletas) {
        Map<String, List<Long>> tycPorFirmante = new HashMap<>();

        logger.info("🔍 MAPEANDO TyC INDIVIDUALES POR FIRMANTE - SOLO VERIFICANDO firmadoTyc");

        for (FirmanteOrdenDTO firmante : firmantes) {
            boolean tieneTyC = false;
            try {
                Usuario usuario = usuarioService.findByEmail(firmante.getEmail());
                if (usuario != null && usuario.getFirmadoTyc()) {
                    tieneTyC = true;
                }
            } catch (Exception e) {
                logger.warning("Error verificando firmado_tyc para usuario: " + firmante.getEmail() + " - " + e.getMessage());
            }

            // SOLO agregar TyC si NO tiene TyC firmado
            if (!tieneTyC) {
                logger.info("🔄 Procesando firmante SIN TyC firmado: " + firmante.getEmail());

                List<Long> tycParaEsteFirmante = new ArrayList<>();
                String emailNormalizado = normalizarEmail(firmante.getEmail());

                for (Long idTyC : idsPlantillasTyC) {
                    String rutaTyC = mapaRutasCompletas.get(idTyC);
                    boolean pertenece = perteneceEsteTyCAlFirmante(rutaTyC, firmante.getEmail(), emailNormalizado);

                    // Verificar por campo email_firmantes en la BD si no hay match por ruta
                    if (!pertenece) {
                        try {
                            ArchivoFirma archivoTyC = archivoFirmaService.findById(idTyC);
                            if (archivoTyC != null) {
                                String emailFirmantes = archivoTyC.getEmailFirmantes();
                                if (emailFirmantes != null) {
                                    String[] emails = emailFirmantes.split(",");
                                    if (emails.length == 1 && emails[0].trim().equalsIgnoreCase(firmante.getEmail())) {
                                        pertenece = true;
                                    }
                                }
                            }
                        } catch (Exception e) {
                            logger.warning("Error verificando email_firmantes en archivo TyC ID " + idTyC + ": " + e.getMessage());
                        }
                    }

                    if (pertenece) {
                        tycParaEsteFirmante.add(idTyC);
                        logger.info("   ✅ MATCH ENCONTRADO: TyC ID " + idTyC + " → " + firmante.getEmail());
                    } else {
                        logger.info("   ❌ No match: TyC ID " + idTyC + " ≠ " + firmante.getEmail());
                    }
                }

                if (!tycParaEsteFirmante.isEmpty()) {
                    tycPorFirmante.put(firmante.getEmail(), tycParaEsteFirmante);
                    logger.info("✅ RESULTADO: " + firmante.getEmail() + " → " + tycParaEsteFirmante.size() + " TyC individuales (IDs: " + tycParaEsteFirmante + ")");
                } else {
                    logger.warning("⚠️ NO SE ENCONTRARON TyC para: " + firmante.getEmail());
                    logger.warning("   🔍 Posibles causas:");
                    logger.warning("   - El directorio no sigue el patrón esperado");
                    logger.warning("   - El email no coincide con la estructura de directorios");
                    logger.warning("   - Los archivos TyC no se crearon correctamente");
                }
            } else {
                logger.info("ℹ️ Firmante con TyC ya firmado: " + firmante.getEmail());
            }
        }

        logger.info("🎯 RESUMEN MAPEO TyC:");
        for (Map.Entry<String, List<Long>> entry : tycPorFirmante.entrySet()) {
            logger.info("   📧 " + entry.getKey() + " → TyC IDs: " + entry.getValue());
        }

        return tycPorFirmante;
    }

    public String obtenerTokenPorEmailEIdsArchivos(String email, String idsArchivos) {
        try {
            // El formato esperado de idsArchivos es "0-737-0" o similar
            // Busca el token en la tabla usando el email y los ids
            logger.info("🔍 Buscando token para email: " + email + " y ids_archivos: " + idsArchivos);

            // Usa el servicio TokenServiceImpl para consultar el token
            // El método consultarToken debe buscar por email y ids en la tabla token
            Long idArchivoLong = null;
            try {
                idArchivoLong = Long.parseLong(idsArchivos);
            } catch (NumberFormatException ex) {
                logger.warning("idsArchivos no es un número válido: " + idsArchivos);
                return null;
            }
            Token tokenEntity = tokenService.buscarTokenPorEmailYIdArchivo(email, idArchivoLong);
            if (tokenEntity != null && tokenEntity.getIdToken() != null && !tokenEntity.getIdToken().isEmpty()) {
                logger.info("✅ Token encontrado para " + email + ": " + tokenEntity.getIdToken());
                return tokenEntity.getIdToken();
            } else {
                logger.warning("⚠️ No se encontró token para firmante: " + email + " con ids: " + idsArchivos);
                return null;
            }
        } catch (Exception e) {
            logger.severe("Error buscando token por email e ids_archivos: " + e.getMessage());
            return null;
        }
    }
}