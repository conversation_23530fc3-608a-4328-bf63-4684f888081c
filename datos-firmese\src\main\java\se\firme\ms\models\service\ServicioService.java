/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.models.service;

import com.google.gson.Gson;

import java.util.Calendar;
import java.util.Date;
import java.util.Optional;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import se.firme.commons.exception.FirmaException;
import se.firme.commons.exception.ServicioFirmeseException;
import se.firme.commons.firmese.dto.PaqueteServicioDTO;
import se.firme.commons.firmese.dto.ServicioDTO;
import se.firme.commons.firmese.util.Parameters;
import se.firme.ms.datos.models.dao.IServicioDAO;
import se.firme.ms.datos.models.dao.SkuDao;
import se.firme.ms.datos.models.entity.Servicio;
import se.firme.ms.datos.models.entity.Sku;
import se.firme.ms.datos.models.entity.Usuario;
import se.firme.ms.models.service.interfaz.IServicioService;
import se.firme.ms.models.service.helper.ServicioHelper;
import se.firme.ms.models.service.interfaz.IPaqueteServicioService;

/**
 * @document ServicioService
 * <AUTHOR> Machado Jaimes
 * @fecha miércoles, enero 13 de 2021, 02:06:20 PM
 */
@Service
public class ServicioService implements IServicioService {

    @Autowired
    private IServicioDAO iServicioDAO;
    @Autowired
    private PaqueteServicioServiceImpl iPaqueteServicioService;
    @Autowired
    private SkuDao skudao;

    private static Logger logger = Logger.getLogger(ServicioService.class.getCanonicalName());

    @Override
    public void crearServicio(Usuario usuario) throws FirmaException {
        try {
            Servicio servicio = new Servicio();
            servicio.setCantidadFirmas(Parameters.string.SERVICIO_CORTESIA_FIRMAS);
            servicio.setFechaActualizacion(new Date());
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_WEEK, Parameters.string.SERVICIO_CORTESIA_DIAS);
            servicio.setFechaVencimiento(calendar.getTime());
            servicio.setIdServicio(usuario.getIdUsuario());
            servicio.setUsuario(usuario);
            servicio.setTipoServicio(Parameters.string.TIPO_SERVICIO_PREPAGO);
            servicio.setTipoValidacion(Parameters.string.TIPO_VALIDACION_FIRMAS_CANTIDAD);
            servicio.setCantOtros(Parameters.string.SERVICIO_CORTESIA_FIRMAS);
            iServicioDAO.save(servicio);
            PaqueteServicioDTO paqueteServicioDTO = new PaqueteServicioDTO();
            paqueteServicioDTO.setCantidadFirmas(servicio.getCantidadFirmas());
            paqueteServicioDTO.setIdServicio(usuario.getIdUsuario());
            paqueteServicioDTO.setJsonServicio("REGISRO DE CORTESIA");
            iPaqueteServicioService.registrarPaquete(paqueteServicioDTO);
            logger.log(Level.INFO, " {0}", servicio.toString());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "No se pudo crear el servicio: {0}",
                    e.getMessage() + " ID-USER : " + usuario.getNombreCompleto() + " - " + usuario.getNombreCompleto());
            throw new FirmaException("No se pudo crear el servicio");
        }
    }

    @Override
    @Transactional
    public boolean agregarPaqueteServicio(long idusuario, int cantidad, int tiempoValidez, int cantOtros)
            throws FirmaException {
        try {
            Servicio servicio = iServicioDAO.findServicioById(idusuario);
            String json = new Gson().toJson(ServicioHelper.convert(servicio));
            servicio.setCantidadFirmas(servicio.getCantidadFirmas() + cantidad);
            servicio.setCantOtros(servicio.getCantOtros() + cantOtros);
            iServicioDAO.recargarCantidadfirmas(servicio.getCantidadFirmas(), tiempoValidez, idusuario,
                    servicio.getCantOtros());
            PaqueteServicioDTO paqueteServicioDTO = new PaqueteServicioDTO();
            paqueteServicioDTO.setCantidadFirmas(servicio.getCantidadFirmas());
            paqueteServicioDTO.setIdServicio(idusuario);
            paqueteServicioDTO.setJsonServicio(json);
            iPaqueteServicioService.registrarPaquete(paqueteServicioDTO);
            logger.log(Level.INFO, "Usuario recargado con id: {0}", idusuario);
            return true;
        } catch (Exception e) {
            logger.log(Level.SEVERE, "No se pudo crear el servicio: {0}", e.getMessage() + " ID-USER : " + idusuario);
            throw new FirmaException("No se pudo crear el servicio");
        }
    }

    @Transactional
    public boolean agregarPaqueteServicio(long codigo, int idSku) throws FirmaException {
        try {
            Servicio servicio = iServicioDAO.findServicioById(codigo);
            String json = new Gson().toJson(ServicioHelper.convert(servicio));
            Optional<Sku> optional = skudao.findById(idSku);
            if (optional.isPresent()) {
                Sku sku = optional.get();
                if (servicio.getTipoServicio().equals(sku.getTipoServicio())
                        && servicio.getTipoValidacion().equals(sku.getTipoValidacion())) {
                    switch (servicio.getTipoValidacion()) {
                        case Parameters.string.TIPO_VALIDACION_FIRMAS_TIEMPO:
                            servicio.setCantidadFirmas(sku.getCantidadFirmas());
                            servicio.setCantOtros(sku.getCantidadOtrosFirmantes());
                            break;
                        case Parameters.string.TIPO_VALIDACION_FIRMAS_CANTIDAD:
                            servicio.setCantidadFirmas(servicio.getCantidadFirmas() + sku.getCantidadFirmas());
                            servicio.setCantOtros(sku.getCantidadOtrosFirmantes());
                            break;

                        default:
                            break;
                    }

                    Date fechaActual = new Date();
                    Calendar calendar = Calendar.getInstance();
                    if (fechaActual.before(servicio.getFechaVencimiento())) {
                        calendar.setTime(servicio.getFechaVencimiento());
                    } else {
                        servicio.setFechaActualizacion(fechaActual);
                    }

                    calendar.add(Calendar.MONTH, sku.getVigenciaMeses());
                    servicio.setFechaVencimiento(calendar.getTime());
                    iServicioDAO.save(servicio);
                    PaqueteServicioDTO paqueteServicioDTO = new PaqueteServicioDTO();
                    paqueteServicioDTO.setCantidadFirmas(servicio.getCantidadFirmas());
                    paqueteServicioDTO.setIdServicio(servicio.getIdServicio());
                    paqueteServicioDTO.setJsonServicio(json);
                    paqueteServicioDTO.setIdSku(sku.getIdSku());
                    ;
                    iPaqueteServicioService.registrarPaquete(paqueteServicioDTO);
//					iServicioDAO.recargarCantidadfirmas(servicio.getCantidadFirmas(), tiempoValidez, servicio.getIdServicio(),
//							servicio.getCantOtros());
                    return true;
                }
                throw new FirmaException("No se asignar sku con validaciones diferentes");
            }
            throw new FirmaException("No existe sku con id " + idSku);
        } catch (Exception e) {
            throw new FirmaException("No se pudo crear el servicio: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public synchronized boolean validarServicio(Long idUsuario, boolean propietario) throws ServicioFirmeseException {
        try {
            Servicio servicio = iServicioDAO.findServicioById(idUsuario);
            if (servicio != null) {
                if ("PRE".equals(servicio.getTipoServicio())) {
                    switch (servicio.getTipoValidacion()) {
                        case "VTF":
                            Date fechaActual = new Date();
                            if (fechaActual.before(servicio.getFechaVencimiento())) {
                                logger.log(Level.INFO, "Servicio activo con vencimiento: {0}",
                                        servicio.getFechaVencimiento());
                                return true;
                            } else {
                                throw new ServicioFirmeseException("Servicio vencido por tiempo, fecha de vencimiento "
                                        + servicio.getFechaVencimiento());
                            }
                        case "VCF":
                            int totalSencillas = servicio.getCantidadFirmas();
                            int totalOtros = servicio.getCantOtros();
                            int cantidad = 0;
                            if (propietario) {
                                cantidad = totalSencillas;
                                totalSencillas = totalSencillas - 1;
                            } else {
                                cantidad = totalOtros;
                                if (cantidad <= 0) {
                                    throw new ServicioFirmeseException("El propietario del documento no tiene saldo para permitir firmas de intervinientes");
                                }
                                totalOtros = totalOtros - 1;
                            }

                            logger.log(Level.INFO, "Propietario del documento: {0}", propietario);
                            if (cantidad > 0) {
                                iServicioDAO.actualizarCantidadfirmas(totalSencillas, totalOtros, idUsuario);
                                return true;
                            }
                            throw new ServicioFirmeseException("No tiene saldo para firmar documentos");
                        default:
                            throw new ServicioFirmeseException("No tiene un tipo de validación de servicio asignado");
                    }
                } else {
                    return true;
                }

            }
            throw new ServicioFirmeseException("No tiene registro de servicio disponible");
        } catch (NullPointerException e) {
            throw new ServicioFirmeseException("No tiene servicio disponible para firmar este documento");
        }
    }

    @Override
    @Transactional
    public boolean retornarBolsa(Long idUsuario, boolean propietario) {
        try {
            Servicio servicio = iServicioDAO.findServicioById(idUsuario);
            if (servicio != null) {
                int cantidad = servicio.getCantidadFirmas();
                int otros = servicio.getCantOtros();
                if (propietario) {
                    cantidad = cantidad + 1;
                } else {
                    otros = otros + 1;
                }
                iServicioDAO.actualizarCantidadfirmas(cantidad, otros, idUsuario);
                logger.log(Level.INFO, "Retorna a la bolsa, Cantidad de firmas en la bolsa: {0}", cantidad);
                return true;
            }
            logger.log(Level.SEVERE, "No se encontro servicio con id: {0}", idUsuario);
        } catch (Exception e) {
            logger.log(Level.SEVERE, "No se pudo retornar a la bolsa: {0}", e.getMessage() + " ID-USER : " + idUsuario);
        }
        return false;
    }

    @Override
    public ServicioDTO findById(long idUsuario) throws FirmaException {
        try {
            Servicio servicio = iServicioDAO.findServicioById(idUsuario);
            if (servicio != null) {
                return ServicioHelper.convert(servicio);
            }
            throw new FirmaException("No se encontró el registro de servicio");
        } catch (FirmaException | NullPointerException e) {
            throw new FirmaException(e.getMessage());
        }
    }

    @Transactional
    public void actualizarServicio(long idServicio, String endpoint, boolean endpointCBackHabilitado) throws FirmaException {
        try {
            iServicioDAO.actualizarServicio(idServicio, endpoint, endpointCBackHabilitado);
        } catch (Exception e) {
            throw new FirmaException(e.getMessage());
        }
    }

    @Transactional
    public void actualizarNotificarFirma(long idServicio, boolean notificarFirma) throws FirmaException {
        try {
            iServicioDAO.actualizarNotificarFirma(idServicio, notificarFirma);
        } catch (Exception e) {
            throw new FirmaException(e.getMessage());
        }
    }
}
