package se.firme.ms.firma.servicio;

import org.springframework.stereotype.Component;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import sendinblue.ApiClient;
import sendinblue.ApiException;
import se.firme.commons.firmese.service.EmailService;
import sendinblue.Configuration;
import sendinblue.auth.ApiKeyAuth;
import sibApi.SmtpApi;
import sibModel.SendSmtpEmail;
import sibModel.SendSmtpEmailSender;
import sibModel.SendSmtpEmailTo;
@Component
public class SenderEmailService {

	public boolean sendEmail(String email, String subject, String content) throws Exception {
		if(true) {
			return sendEmail(email, subject, content);
		}
		return EmailService.send(email, subject, content);
	}
	private  boolean sendEmailSendinBlue(String email, String subject, String content) {
		try {
			   ApiClient defaultClient = Configuration.getDefaultApiClient();
	            ApiKeyAuth apiKey = (ApiKeyAuth) defaultClient.getAuthentication("api-key");
	            apiKey.setApiKey("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");

	            SmtpApi sendinblueSmtpApiInstance = new SmtpApi();
	            SendSmtpEmail sendinblueSendSmtpEmail = new SendSmtpEmail();

	            sendinblueSendSmtpEmail.setSubject(subject);
	            SendSmtpEmailSender emailSender = new SendSmtpEmailSender();
	            emailSender.setEmail("<EMAIL>");
	            emailSender.setName("Firme.se");
	            sendinblueSendSmtpEmail.setSender(emailSender);

	            sendinblueSendSmtpEmail.setHtmlContent(content);
	            List<SendSmtpEmailTo> recipients=new ArrayList<>();
	            recipients.add(new SendSmtpEmailTo().email(email));
	            sendinblueSendSmtpEmail.setTo(recipients);
	            sendinblueSmtpApiInstance.sendTransacEmail(sendinblueSendSmtpEmail);
			return true;
		} catch (Exception e) {
			return false;
		}
	}
	

}
