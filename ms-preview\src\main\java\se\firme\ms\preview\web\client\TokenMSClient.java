package se.firme.ms.preview.web.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import se.firme.commons.firmese.dto.ApiResponse;

@FeignClient(name = "ms-token")
public interface TokenMSClient {

    @PostMapping(value = "/registro/verifica-token-firmante", consumes = "application/json")
    public ApiResponse verificarTokenFirmante(@RequestBody String token);
}
