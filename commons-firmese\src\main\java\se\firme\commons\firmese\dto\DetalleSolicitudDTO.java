package se.firme.commons.firmese.dto;

import java.util.List;

public class DetalleSolicitudDTO {
    private String nombreArchivo;
    private String tipoFirma;
    private String b64;
    private String estado;
    private String detalleEstado;
    private List<FirmanteSolicitudDTO> firmantes;
    private FirmanteSolicitudDTO propietario;

    public String getNombreArchivo() {
        return nombreArchivo;
    }

    public void setNombreArchivo(String nombreArchivo) {
        this.nombreArchivo = nombreArchivo;
    }

    public String getTipoFirma() {
        return tipoFirma;
    }

    public void setTipoFirma(String tipoFirma) {
        this.tipoFirma = tipoFirma;
    }

    public String getB64() {
        return b64;
    }

    public void setB64(String b64) {
        this.b64 = b64;
    }

    public String getEstado() {
        return estado;
    }

    public void setEstado(String estado) {
        this.estado = estado;
    }

    public String getDetalleEstado() {
        return detalleEstado;
    }

    public void setDetalleEstado(String detalleEstado) {
        this.detalleEstado = detalleEstado;
    }

    public List<FirmanteSolicitudDTO> getFirmantes() {
        return firmantes;
    }

    public void setFirmantes(List<FirmanteSolicitudDTO> firmantes) {
        this.firmantes = firmantes;
    }

    public FirmanteSolicitudDTO getPropietario() {
        return propietario;
    }

    public void setPropietario(FirmanteSolicitudDTO propietario) {
        this.propietario = propietario;
    }
}
