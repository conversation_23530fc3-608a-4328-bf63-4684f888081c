<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="SignatureSheet" pageWidth="612" pageHeight="792" whenNoDataType="AllSectionsNoDetail" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="0f7bdfaa-cfbb-4801-bcf2-67b62d05f114">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="idArchivoFirma" class="java.lang.Long"/>
	<parameter name="url" class="java.lang.String"/>
	<queryString>
		<![CDATA[select 	 fas.*
	,u.nombre_completo
	,u.correo_electronico
	,u.numero_documento
from firma_archivo_usuario  fas
	inner join usuario u on fas.id_usuario = u.id_usuario
where id_archivo_firma = $P{idArchivoFirma}]]>
	</queryString>
	<field name="id_firma_archivo_usuario" class="java.lang.Long">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="id_usuario" class="java.lang.Long">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="id_archivo_firma" class="java.lang.Long">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="subio" class="java.lang.Boolean">
		<fieldDescription><![CDATA[Indica si el usuario actual apra el archivo fue quien subio el documento]]></fieldDescription>
	</field>
	<field name="hash_archivo" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="fecha_registro" class="java.sql.Timestamp">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ruta_relativa_archivo" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="nombre_archivo_firma" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="ip" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="nombre_completo" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="correo_electronico" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<field name="numero_documento" class="java.lang.String">
		<fieldDescription><![CDATA[]]></fieldDescription>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="75">
			<staticText>
				<reportElement uuid="3edc19f9-d6b7-430d-b638-cacfa20bebbc" x="0" y="18" width="572" height="39"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="24" isBold="true" isUnderline="false"/>
				</textElement>
				<text><![CDATA[FIRMANTES]]></text>
			</staticText>
		</band>
	</pageHeader>
	<detail>
		<band height="131" splitType="Stretch">
			<textField>
				<reportElement uuid="c1ee0479-5964-4a58-837c-94f7755783c0" x="147" y="44" width="391" height="20"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{nombre_completo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="87b75515-e982-4ca0-88ec-0bf0b995a2e1" x="35" y="44" width="100" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Nombre:]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="9529de60-2605-4e1b-a4cf-32ee1ff08677" x="10" y="15" width="100" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Firma:]]></text>
			</staticText>
			<staticText>
				<reportElement uuid="deefeb48-5cd3-4497-8351-927af9f4d0db" x="35" y="84" width="100" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[EMail:]]></text>
			</staticText>
			<textField>
				<reportElement uuid="26ac69b2-956c-41db-9125-13d861b0c462" x="147" y="84" width="391" height="20"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{correo_electronico}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="474f4905-8c31-4d49-99ea-ad442cfcd797" x="35" y="104" width="100" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Fecha:]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy h.mm a">
				<reportElement uuid="aabee502-59ec-4d09-b90b-9c788af1e7af" x="147" y="104" width="171" height="20"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{fecha_registro}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement uuid="551261a2-139f-4287-b574-ee32dc0ad52d" x="8" y="8" width="544" height="1"/>
			</line>
			<textField>
				<reportElement uuid="c48b1218-5f02-4663-8f36-bff2202b7cb0" x="147" y="64" width="391" height="20"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{numero_documento}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="d79191e3-7f23-43fb-89d9-174dda6e3f18" x="35" y="64" width="100" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[Número Documento:]]></text>
			</staticText>
			<textField pattern="dd/MM/yyyy h.mm a">
				<reportElement uuid="e556373d-d4e4-4f19-bab7-78f3f91256fb" x="401" y="104" width="137" height="20"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{ip}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement uuid="ebc0b387-6c20-403b-94e0-9c8282536fc8" x="315" y="104" width="68" height="20"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font isBold="true"/>
				</textElement>
				<text><![CDATA[IP:]]></text>
			</staticText>
		</band>
	</detail>
	<columnFooter>
		<band height="45" splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="191" splitType="Stretch">
			<image>
				<reportElement uuid="fed2f6e9-ff51-4f99-bdf4-1c8a50515f44" x="147" y="11" width="236" height="162"/>
				<imageExpression><![CDATA[com.google.zxing.client.j2se.MatrixToImageWriter.toBufferedImage(
    new com.google.zxing.qrcode.QRCodeWriter().encode(
            $P{url}, com.google.zxing.BarcodeFormat.QR_CODE, 200, 200));]]></imageExpression>
			</image>
			<textField>
				<reportElement uuid="1ddeced7-6737-4a8e-ae6d-3303ed19b965" x="438" y="22" width="100" height="20"/>
				<textElement/>
				<textFieldExpression><![CDATA[$P{idArchivoFirma}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="42" splitType="Stretch"/>
	</summary>
</jasperReport>
