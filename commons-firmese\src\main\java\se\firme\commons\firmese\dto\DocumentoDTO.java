/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.commons.firmese.dto;

import java.io.Serializable;

/**
 * @document DocumentoDTO
 * <AUTHOR>
 * @fecha lunes, agosto 24 de 2020, 03:15:12 PM
 */
public class DocumentoDTO implements Serializable{

    public DocumentoDTO() {
    }

    private String codTransaccion;
    private String rowData;
    private String document;
    private String image;

    public String getCodTransaccion() {
        return codTransaccion;
    }

    public void setCodTransaccion(String codTransaccion) {
        this.codTransaccion = codTransaccion;
    }

    public String getRowData() {
        return rowData;
    }

    public void setRowData(String rowData) {
        this.rowData = rowData;
    }

    public String getDocument() {
        return document;
    }

    public void setDocument(String document) {
        this.document = document;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }
    
    
}
