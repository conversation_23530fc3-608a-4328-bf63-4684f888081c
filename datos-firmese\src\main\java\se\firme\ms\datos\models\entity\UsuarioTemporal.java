package se.firme.ms.datos.models.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.CreationTimestamp;

@Entity
@Table(name = "usuario_temporal")
public class UsuarioTemporal implements Serializable {
	private static final long serialVersionUID = 1L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "id_usuario")
	private Long idUsuario;
	@Basic(optional = false)
	@Column(name = "numero_documento")
	private String numeroDocumento;
	@Column(name = "row_data")
	private String rowData;
	@Basic(optional = false)
	@Column(name = "nombre_completo")
	private String nombreCompleto;
	@Basic(optional = false)
	@Column(name = "fecha_registro")
	@Temporal(TemporalType.TIMESTAMP)
	@CreationTimestamp
	private Date fechaRegistro;
	@Column(name = "id_tipo_documento")
	private String idTipoDocumento;
	@Column(name = "documento_persona")
	private String documentoPersona;
	@Column(name = "id_referido")
	private Long idReferido;

	public Long getIdUsuario() {
		return idUsuario;
	}

	public void setIdUsuario(Long idUsuario) {
		this.idUsuario = idUsuario;
	}

	public String getNumeroDocumento() {
		return numeroDocumento;
	}

	public void setNumeroDocumento(String numeroDocumento) {
		this.numeroDocumento = numeroDocumento;
	}

	public String getRowData() {
		return rowData;
	}

	public void setRowData(String rowData) {
		this.rowData = rowData;
	}

	public String getNombreCompleto() {
		return nombreCompleto;
	}

	public void setNombreCompleto(String nombreCompleto) {
		this.nombreCompleto = nombreCompleto;
	}

	public Date getFechaRegistro() {
		return fechaRegistro;
	}

	public void setFechaRegistro(Date fechaRegistro) {
		this.fechaRegistro = fechaRegistro;
	}

	public String getIdTipoDocumento() {
		return idTipoDocumento;
	}

	public void setIdTipoDocumento(String idTipoDocumento) {
		this.idTipoDocumento = idTipoDocumento;
	}

	public String getDocumentoPersona() {
		return documentoPersona;
	}

	public void setDocumentoPersona(String documentoPersona) {
		this.documentoPersona = documentoPersona;
	}

	public Long getIdReferido() {
		return idReferido;
	}

	public void setIdReferido(Long idReferido) {
		this.idReferido = idReferido;
	}

}
