/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.models.service.interfaz;

import java.util.List;
import se.firme.commons.exception.FirmaException;
import se.firme.ms.datos.models.entity.Token;
import se.firme.ms.datos.models.entity.Usuario;

/**
 *
 * <AUTHOR>
 */
public interface ITokenService {
	
    public Token getNuevoToken(Usuario usuario, int tipoToken, int longitud, Long idArhivoFirma, String emailFirmante, String ids);
    public Token findByIdArchivoFirma(long idArchivoFirma, long idUsuario);
    public boolean isTokenActivo(Token token) throws FirmaException;
    public void desactivarTokenByIdToken(String idToken);
    public List<Token> findByCodTransaccionUnico(String codTransaccion);
    public Token getTokenActivoUsuario(Usuario usuario);
}
