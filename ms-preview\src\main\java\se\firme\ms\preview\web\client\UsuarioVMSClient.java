/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.preview.web.client;


import co.venko.ms.models.entity.AdmUsuario;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 *
 * <AUTHOR>
 */
@FeignClient(name="vms-usuario") // nombre del servicio con el cual nos vamos a comunicar y que debe estar registrado en eureka
public interface UsuarioVMSClient {

    @GetMapping("/usuario/search/buscar-usuario-nat")
    public AdmUsuario findById(@RequestParam("idusuario") String idusuario);
}
