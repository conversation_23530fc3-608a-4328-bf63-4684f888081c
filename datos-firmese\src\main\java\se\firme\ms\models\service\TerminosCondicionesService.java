package se.firme.ms.models.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import se.firme.commons.exception.FirmaException;
import se.firme.ms.datos.models.dao.IArchivoFirmaDao;
import se.firme.ms.datos.models.dao.IPlantillaDocumentoRepository;
import se.firme.ms.datos.models.dao.IUsuarioDao;
import se.firme.ms.datos.models.entity.Usuario;
import se.firme.ms.datos.models.entity.PlantillaDocumento;
import se.firme.commons.models.projection.IDatosArchivo;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.logging.Logger;

@Service
public class TerminosCondicionesService {
    
    private static final Logger logger = Logger.getLogger(TerminosCondicionesService.class.getName());
    
    @Autowired
    private IUsuarioDao usuarioDao;
    
    @Autowired
    private IPlantillaDocumentoRepository plantillaRepository;
    
    @Autowired
    private IArchivoFirmaDao archivoFirmaDao;
    
    private static final List<String> NOMBRES_PLANTILLAS_TYC = Arrays.asList(
        "2025 - TÉRMINOS Y CONDICIONES V2",
        "2025. Autorización Datos Personales V3"
    );
    
    /**
     * Obtiene los IDs de plantillas TyC dinámicamente por nombre EXACTO
     */
    public List<Long> getIdPlantillasTyC() {
        try {
            logger.info("🔍 Buscando IDs de plantillas TyC por nombre exacto...");
            
            List<Long> idsEncontrados = new ArrayList<>();
            
            for (String nombrePlantilla : NOMBRES_PLANTILLAS_TYC) {
                // BUSCAR POR NOMBRE EXACTO en lugar de contains
                PlantillaDocumento plantilla = plantillaRepository.findByNombrePlantillaAndActiva(nombrePlantilla, true);
                
                if (plantilla != null) {
                    idsEncontrados.add(plantilla.getIdPlantilla());
                    logger.info("✅ Plantilla TyC encontrada: ID=" + plantilla.getIdPlantilla() + 
                               ", Nombre='" + plantilla.getNombrePlantilla() + "'");
                } else {
                    logger.warning("⚠️ Plantilla TyC NO encontrada: '" + nombrePlantilla + "'");
                }
            }
            
            logger.info("📋 Total plantillas TyC encontradas: " + idsEncontrados.size() + " -> " + idsEncontrados);
            
            return idsEncontrados;
            
        } catch (Exception e) {
            logger.severe("Error obteniendo IDs de plantillas TyC: " + e.getMessage());
            return new ArrayList<>();
        }
    }
    
    /**
     * Verifica si el usuario ha firmado TyC basándose en nombres de plantillas
     */
    public boolean usuarioHaFirmadoTyC(Long idUsuario) throws FirmaException {
        try {
            logger.info("🔍 Verificando TyC firmados para usuario ID: " + idUsuario);
            
            Optional<Usuario> usuarioOpt = usuarioDao.findById(idUsuario);
            if (!usuarioOpt.isPresent()) {
                throw new FirmaException("Usuario no encontrado");
            }
            
            Usuario usuario = usuarioOpt.get();
            logger.info("👤 Usuario: " + usuario.getNombreCompleto() + " - Estado TyC BD: " + usuario.getFirmadoTyc());
            
            // Si ya está marcado como firmado en BD, verificar que realmente tenga las firmas
            if (usuario.getFirmadoTyc()) {
                boolean verificacionReal = verificarFirmasRealesTyC(idUsuario);
                logger.info("🔍 Verificación real de firmas: " + verificacionReal);
                
                if (!verificacionReal) {
                    logger.warning("⚠️ Usuario marcado como firmado pero sin firmas reales, corrigiendo...");
                    usuario.setFirmadoTyc(false);
                    usuarioDao.save(usuario);
                    return false;
                }
                return true;
            }
            
            // Si no está marcado, verificar si tiene las firmas y actualizar
            boolean tieneFiremas = verificarFirmasRealesTyC(idUsuario);
            if (tieneFiremas) {
                logger.info("✅ Usuario tiene firmas TyC, actualizando estado...");
                usuario.setFirmadoTyc(true);
                usuarioDao.save(usuario);
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            logger.severe("Error verificando TyC para usuario: " + e.getMessage());
            throw new FirmaException("Error verificando estado de TyC");
        }
    }
    
    /**
     * Verifica las firmas reales de TyC en archivos firmados - CORREGIDO
     */
    private boolean verificarFirmasRealesTyC(Long idUsuario) {
        try {
            logger.info("🔍 Verificando firmas reales de TyC para usuario: " + idUsuario);
            
            List<Long> idsPlantillasTyC = getIdPlantillasTyC();
            if (idsPlantillasTyC.isEmpty()) {
                logger.warning("⚠️ No se encontraron plantillas TyC configuradas");
                return false;
            }
            
            // ✅ USAR EL TIPO CORRECTO IDatosArchivo
            List<IDatosArchivo> archivosUsuario = archivoFirmaDao.findByArchivoEstado(idUsuario, 2); // Estado 2 = firmado
            
            int firmasTyCEncontradas = 0;
            
            for (IDatosArchivo archivo : archivosUsuario) {
                // ✅ CORREGIR: Usar getIdArchivo() en lugar de getIdArchivoFirma()
                String nombreArchivo = archivo.getNombreArchivo();
                int idArchivo = archivo.getIdArchivo();
                
                if (esArchivoTyCPorNombre(nombreArchivo)) {
                    firmasTyCEncontradas++;
                    logger.info("✅ Firma TyC encontrada: " + nombreArchivo + 
                               " (ID: " + idArchivo + ")");
                } else {
                    logger.info("❌ Archivo NO es TyC: " + nombreArchivo + " (ID: " + idArchivo + ")");
                }
            }
            
            logger.info("📊 Firmas TyC encontradas: " + firmasTyCEncontradas + " de mínimo 2 requeridas");
            
            // Considerar como completo si tiene al menos 2 firmas TyC (términos + datos personales)
            return firmasTyCEncontradas >= 2;
            
        } catch (Exception e) {
            logger.severe("Error verificando firmas reales: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * Verifica si un archivo es de TyC basándose en el nombre - USANDO NOMBRES EXACTOS CORREGIDO
     */
    private boolean esArchivoTyCPorNombre(String nombreArchivo) {
        if (nombreArchivo == null) return false;
        
        // ✅ USAR NOMBRES EXACTOS que coincidan con los de la BD
        String[] nombresExactosTyC = {
            "2025 - TÉRMINOS Y CONDICIONES V2.pdf",
            "2025. Autorización Datos Personales V3.pdf"
        };
        
        logger.info("🔍 Verificando si '" + nombreArchivo + "' es archivo TyC...");
        
        // Verificar con nombre completo
        for (String nombreExacto : nombresExactosTyC) {
            if (nombreArchivo.equals(nombreExacto)) {
                logger.info("🎯 ✅ Nombre exacto TyC detectado: '" + nombreExacto + "'");
                return true;
            }
        }
        
        // También verificar sin extensión
        String nombreSinExtension = nombreArchivo.replaceAll("\\.[^.]+$", "");
        String[] nombresSinExtension = {
            "2025 - TÉRMINOS Y CONDICIONES V2",
            "2025. Autorización Datos Personales V3"
        };
        
        for (String nombreExacto : nombresSinExtension) {
            if (nombreSinExtension.equals(nombreExacto)) {
                logger.info("🎯 ✅ Nombre exacto TyC detectado (sin extensión): '" + nombreExacto + "'");
                return true;
            }
        }
        
        logger.info("❌ Archivo NO es TyC: '" + nombreArchivo + "'");
        return false;
    }
    
    /**
     * Marca TyC como firmados por email - BASADO EN NOMBRES
     */
    @Transactional
    public void marcarTyCFirmadosPorEmail(String email) throws FirmaException {
        try {
            logger.info("=== MARCANDO TYC COMO FIRMADOS POR EMAIL ===");
            logger.info("📧 Email: " + email);
            
            Optional<Usuario> usuarioOpt = usuarioDao.findByEmail(email);
            if (usuarioOpt.isPresent()) {
                Usuario usuario = usuarioOpt.get();
                
                logger.info("👤 Usuario encontrado:");
                logger.info("   - ID: " + usuario.getIdUsuario());
                logger.info("   - Nombre: " + usuario.getNombreCompleto());
                logger.info("   - TyC actual: " + usuario.getFirmadoTyc());
                
                // Verificar que realmente tenga las firmas antes de marcar
                boolean tieneFiremasReales = verificarFirmasRealesTyC(usuario.getIdUsuario());
                
                if (tieneFiremasReales && !usuario.getFirmadoTyc()) {
                    usuario.setFirmadoTyc(true);
                    usuario.setProcesoRegistro(false); // <- Marcar como fuera de proceso de registro
                    usuarioDao.save(usuario);
                    logger.info("✅ TyC marcados como firmados para: " + email);
                    
                } else if (tieneFiremasReales && usuario.getFirmadoTyc()) {
                    logger.info("ℹ️ Usuario ya tenía TyC marcados como firmados");
                    
                } else {
                    logger.warning("⚠️ Usuario no tiene suficientes firmas TyC reales, no se marca como firmado");
                }
                
            } else {
                logger.info("ℹ️ Usuario no encontrado con email: " + email + " (puede ser firmante externo)");
            }
            
        } catch (Exception e) {
            logger.severe("❌ Error marcando TyC firmados por email: " + e.getMessage());
            throw new FirmaException("Error actualizando estado de TyC por email");
        }
    }
    
    /**
     * Obtiene información de debug de plantillas TyC
     */
    public Map<String, Object> getDebugInfoTyC() {
        Map<String, Object> info = new HashMap<>();
        
        try {
            info.put("nombresPlantillasTyCConfigurados", NOMBRES_PLANTILLAS_TYC);
            info.put("idsPlantillasTyCEncontrados", getIdPlantillasTyC());
            
            // Listar todas las plantillas activas para reference
            List<PlantillaDocumento> todasPlantillas = plantillaRepository.findAllActivasOrderByNombre();
            List<Map<String, Object>> plantillasInfo = new ArrayList<>();
            
            for (PlantillaDocumento plantilla : todasPlantillas) {
                Map<String, Object> pInfo = new HashMap<>();
                pInfo.put("id", plantilla.getIdPlantilla());
                pInfo.put("nombre", plantilla.getNombrePlantilla());
                pInfo.put("esTyC", esNombrePlantillaTyC(plantilla.getNombrePlantilla()));
                plantillasInfo.add(pInfo);
            }
            
            info.put("todasLasPlantillasActivas", plantillasInfo);
            
        } catch (Exception e) {
            info.put("error", "Error obteniendo información: " + e.getMessage());
        }
        
        return info;
    }
    
    /**
     * Verifica si un nombre de plantilla corresponde a TyC
     */
    private boolean esNombrePlantillaTyC(String nombrePlantilla) {
        if (nombrePlantilla == null) return false;
        
        return NOMBRES_PLANTILLAS_TYC.stream()
                .anyMatch(nombre -> nombrePlantilla.toLowerCase().contains(nombre.toLowerCase()));
    }
}