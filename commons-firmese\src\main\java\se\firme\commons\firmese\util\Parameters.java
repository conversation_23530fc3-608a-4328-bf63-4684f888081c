/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.commons.firmese.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * @document Parameters
 * <AUTHOR>
 * @fecha viernes, agosto 21 de 2020, 03:37:35 PM
 */
public class Parameters {

	private static Properties properties;
	private static Logger logger = Logger.getLogger(Parameters.class.getName());
//    public static final String HOST_DE_DESPLIEGUE = "HOST_DE_DESPLIEGUE";
//    public static final String CLIENTE_KONIVIN_CLAVE = "CLIENTE_KONIVIN_CLAVE";
//    public static final String CLIENTE_KONIVIN_USUARIO = "CLIENTE_KONIVIN_USUARIO";
	public static final String CORREO_CLAVE = "CORREO_CLAVE";
	public static final String CORREO_PUERTO = "CORREO_PUERTO";
	public static final String CORREO_REMITENTE = "CORREO_REMITENTE";
	public static final String CORREO_SMTP = "CORREO_SMTP";
	public static final String FIRMAS_RUTA_PRINCIPAL = "FIRMAS_RUTA_PRINCIPAL";
	public static final String FUENTE_ESTADO_CEDULA_CODIGO = "FUENTE_ESTADO_CEDULA_CODIGO";
	public static final String HASH_ALGORITMO = "HASH_ALGORITMO";
//    public static final String KONIVIN_URL_SERVICIO_FUENTES = "KONIVIN_URL_SERVICIO_FUENTES";
	public static final String FUENTE_ESTADO_CEDULA_EXTRANJERIA_CODIGO = "FUENTE_ESTADO_CEDULA_EXTRANJERIA_CODIGO";
	public static final String MAXIMO_FIRMANTES_DOCUMENTO = "maximo.firmantes.documento";

	static {
		try {
			getPropertiesFile();
		} catch (Exception ex) {
			logger.log(Level.SEVERE, "ER: {0}", ex.getMessage());
		}
	}

	public static Properties getPropertiesFile() throws IOException {
		properties = new Properties();
		InputStream inputStream;
		try {
			inputStream = new FileInputStream(string.HOME + File.separator + "firmese.properties");
		} catch (FileNotFoundException e) {
			logger.log(Level.INFO, "Cargando archivo default {0}", e.getMessage());
			inputStream = Parameters.class.getClassLoader().getResourceAsStream("firmese.properties");
		}

		try {
			logger.log(Level.INFO, "Cargando parámetros");
			properties.load(inputStream);
			logger.log(Level.INFO, properties.toString());
		} catch (IOException e) {
			throw e;
		} finally {
			try {
				if (inputStream != null) {
					inputStream.close();
				}
			} catch (IOException ex) {
				logger.log(Level.SEVERE, "ER: {0}", ex.getMessage());
			}
		}
		return properties;
	}

	public static String getParameter(String parameter) {
		String property = properties.getProperty(parameter, "NO-DEFINIDO");
		if ("NO-DEFINIDO".equals(property)) {
			logger.log(Level.SEVERE, "Parametro no se ha agregado al archivo {0}", parameter);
		}
		return property;
	}

	public static class string {

		public static final String FIRMESE_BASE_URI_FRONTEND = "app.web.frontend";
		public static int TOKEN_TIPO_REGISTRO_USUARIO = 1;
		public static int TOKEN_TIPO_FIRMA_DOCUMENTO = 2;
		public static int TOKEN_TIPO_CAMBIO_CONTRASENA = 3;
		public static int SERVICIO_CORTESIA_FIRMAS = 5;
		public static int SERVICIO_CORTESIA_DIAS = 5;
		public static String HOME = "/opt/firmese/properties";
		public static String TIPO_SERVICIO_PREPAGO = "PRE";
		public static String TIPO_SERVICIO_POSPAGO = "POS";
		public static final String TIPO_FIRMA_MULTIPLE = "MULTIPLE";
		public static final String TIPO_FIRMA_SINGLE = "SINGLE";
		public static final String TIPO_FIRMA_OTHERS = "OTHERS";
		public static final int ESTADO_ARCHIVO_PENDIENTE_FIRMA = 1;
		public static final int ESTADO_ARCHIVO_FIRMADO = 2;
		public static final String TIPO_VALIDACION_FIRMAS_TIEMPO = "VTF";
		public static final String TIPO_VALIDACION_FIRMAS_CANTIDAD = "VCF";
		public static final String API_KEY_MAILGUN = "**************************************************";
	}
}
