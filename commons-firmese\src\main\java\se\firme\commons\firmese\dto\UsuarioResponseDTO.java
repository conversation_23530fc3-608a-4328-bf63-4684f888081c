/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.commons.firmese.dto;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class UsuarioResponseDTO implements Serializable{

    private static final long serialVersionUID = 483684509339041885L;

    private String numeroDocumento;
    private String correoElectronico;
    private String nombreCompleto;
    private String nombreTipoDocumento;

    public UsuarioResponseDTO() {
    }

    public UsuarioResponseDTO(String nombreCompleto, String correoElectronico) {
		this.nombreCompleto=nombreCompleto;
		this.correoElectronico=correoElectronico;
	}

	public String getNumeroDocumento() {
        return numeroDocumento;
    }

    public void setNumeroDocumento(String numeroDocumento) {
        this.numeroDocumento = numeroDocumento;
    }

    public String getCorreoElectronico() {
        return correoElectronico;
    }

    public void setCorreoElectronico(String correoElectronico) {
        this.correoElectronico = correoElectronico;
    }

    public String getNombreCompleto() {
        return nombreCompleto;
    }

    public void setNombreCompleto(String nombreCompleto) {
        this.nombreCompleto = nombreCompleto;
    }

    public String getNombreTipoDocumento() {
        return nombreTipoDocumento;
    }

    public void setNombreTipoDocumento(String nombreTipoDocumento) {
        this.nombreTipoDocumento = nombreTipoDocumento;
    }
    
    
}
