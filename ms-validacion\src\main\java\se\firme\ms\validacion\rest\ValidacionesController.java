/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package se.firme.ms.validacion.rest;

import java.io.IOException;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import se.firme.commons.exception.FirmaException;
import se.firme.commons.firmese.dto.ApiResponse;
import se.firme.commons.firmese.dto.ArchivoFirmaResponseDTO;
import se.firme.commons.firmese.dto.FirmaRequestDTO;
import se.firme.ms.validacion.negocio.ValidacionNegocio;

/**
 * @document ValidacionesController
 * <AUTHOR> Machado Jaimes
 * @fecha martes, agosto 18 de 2020, 04:23:01 PM
 */
@RestController
@RefreshScope
@RequestMapping("/registro")
public class ValidacionesController {

    @Autowired
    private ValidacionNegocio negocio;
    @Autowired
    private HttpServletRequest request;

    @PostMapping(path = "/validarcelular", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> validarNumero(@RequestParam("ctoken") String ctoken,
            @RequestParam("numero") String numero) {
        try {
            return new ResponseEntity<>(negocio.validaNumero(ctoken, numero), HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping(path = "/validarcodigo", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> validarCodigo(@RequestParam("ctoken") String ctoken) {
        try {
            return new ResponseEntity<>(negocio.validaCodigoTransaccion(ctoken), HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping(path = "/validarsms", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> validarSms(@RequestParam("ctoken") String ctoken,
            @RequestParam("csms") String csms) {
        try {
            return new ResponseEntity<>(negocio.validaCodigoSMS(ctoken, csms), HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping(path = "/validarcodigo-firma", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> validarCodigoFirma(@RequestParam("ctoken") String ctoken) {
        try {
            return new ResponseEntity<>(negocio.validaCodigoTransaccionFirma(ctoken), HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping("/verificar-archivo-firmado-bin")
    public ResponseEntity<ApiResponse> verificarArchivoFirmaBin(@RequestParam("archivo") MultipartFile file) {
        try {
            FirmaRequestDTO datosFirma = new FirmaRequestDTO();
            byte[] bytes = file.getBytes();
            String nombreArchivo = file.getOriginalFilename();
            datosFirma.setNombreArchivo(nombreArchivo);
            ArchivoFirmaResponseDTO archivo = negocio.verificarFirmaArchivo(datosFirma, bytes);
            return new ResponseEntity<>(new ApiResponse.ok().data(archivo).build(), HttpStatus.OK);
        } catch (IOException e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        } catch (Exception ex) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(ex.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping("/verificar-archivo-firmado-64")
    public ResponseEntity<ApiResponse> verificarArchivoFirma64(@RequestBody FirmaRequestDTO datosFirma) {
        try {
            ArchivoFirmaResponseDTO archivo = negocio.verificarFirmaArchivo(datosFirma, null);
            return new ResponseEntity<>(new ApiResponse.ok().data(archivo).build(), HttpStatus.OK);
        } catch (FirmaException e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping("/download/{fileName:.+}")
    public ResponseEntity<Resource> downloadFile(@PathVariable String fileName, HttpServletRequest request) {
        try {
            // Load file as Resource
            Resource resource = negocio.loadFileAsResource(fileName);

            // Try to determine file's content type
            String contentType = null;
            try {
                contentType = request.getServletContext().getMimeType(resource.getFile().getAbsolutePath());
            } catch (IOException ex) {
//            logger.info("Could not determine file type.");
            }

            // Fallback to the default content type if type could not be determined
            if (contentType == null) {
                contentType = "application/octet-stream";
            }

            return ResponseEntity.ok().contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
                    .body(resource);
        } catch (FirmaException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
        }
    }

    @PostMapping("/downloadB64/{id}")
    public ResponseEntity<ApiResponse> downloadFileB64(@PathVariable int id, HttpServletRequest request) {
        try {
            // Load file as Resource
            String archivo = negocio.loadFileAsB64(id);
            return new ResponseEntity<>(new ApiResponse.ok().data(archivo).build(), HttpStatus.OK);
        } catch (FirmaException e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping(path = "/validar-firmante-documento", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> validarSmsMultiple(@RequestParam("csms") String sms,
            @RequestParam("usms") long usms, @RequestBody String token) throws IOException, Exception {
        try {
            String ipAddress = request.getHeader("X-FORWARDED-FOR");
            ipAddress = (ipAddress == null ? request.getRemoteAddr() : ipAddress);
            String userAgent = request.getHeader("User-Agent");
            return new ResponseEntity<>(
                    new ApiResponse.ok().data(negocio.firmarDocumentos(sms, usms, token, ipAddress, userAgent)).build(),
                    HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }
    }
    
    
    @PostMapping(path = "/confirmar-firmado/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> confirmarFirmado(@PathVariable(name = "id") Integer idArchivo) throws IOException, Exception {
        try {
            return new ResponseEntity<>(
                    new ApiResponse.ok().data(negocio.confirmarFirmado(idArchivo)).build(),
                    HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping(path = "/enviar-otp-email", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> enviarOtpEmail(@RequestParam("id") String id,
            @RequestParam("tipo") String tipo) {
        try {
            return new ResponseEntity<>(negocio.enviarOtpEmail(id, tipo), HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }
    }
}
