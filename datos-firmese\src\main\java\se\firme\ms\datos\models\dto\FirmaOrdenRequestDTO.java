package se.firme.ms.datos.models.dto;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonProperty;

public class FirmaOrdenRequestDTO {
    
    @JsonProperty("firmantes")
    private List<FirmanteOrdenDTO> firmantes;
    
    @JsonProperty("documentos")
    private List<DocumentoOrdenDTO> documentos;
    
    @JsonProperty("tipoFirma")
    private String tipoFirma;
    
    @JsonProperty("fechaVigencia")
    private String fechaVigencia;
    
    @JsonProperty("tipoOrden")
    private String tipoOrden = "PARALELO"; // VALOR POR DEFECTO
    
    // Getters y Setters
    public List<FirmanteOrdenDTO> getFirmantes() {
        return firmantes;
    }
    
    public void setFirmantes(List<FirmanteOrdenDTO> firmantes) {
        this.firmantes = firmantes;
    }
    
    public List<DocumentoOrdenDTO> getDocumentos() {
        return documentos;
    }
    
    public void setDocumentos(List<DocumentoOrdenDTO> documentos) {
        this.documentos = documentos;
    }
    
    public String getTipoFirma() {
        return tipoFirma;
    }
    
    public void setTipoFirma(String tipoFirma) {
        this.tipoFirma = tipoFirma;
    }
    
    public String getFechaVigencia() {
        return fechaVigencia;
    }
    
    public void setFechaVigencia(String fechaVigencia) {
        this.fechaVigencia = fechaVigencia;
    }
    
    public String getTipoOrden() {
        return (tipoOrden != null && !tipoOrden.trim().isEmpty()) ? tipoOrden : "PARALELO";
    }
    
    public void setTipoOrden(String tipoOrden) {
        this.tipoOrden = (tipoOrden != null && !tipoOrden.trim().isEmpty()) ? tipoOrden : "PARALELO";
    }
}