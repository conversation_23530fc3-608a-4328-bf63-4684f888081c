/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.commons.firmese.dto;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
public class SendFileEmailRequestDTO implements Serializable{
    
    private String correos;
    private List<FirmaRequestDTO> archivos;

    public String getCorreos() {
        return correos;
    }

    public void setCorreos(String correos) {
        this.correos = correos;
    }

    public List<FirmaRequestDTO> getArchivos() {
        return archivos;
    }

    public void setArchivos(List<FirmaRequestDTO> archivos) {
        this.archivos = archivos;
    }
    
    
}
