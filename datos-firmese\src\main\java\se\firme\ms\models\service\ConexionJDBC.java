/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.models.service;

import java.sql.DriverManager;
import java.sql.SQLException;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
@Component
public class ConexionJDBC {
    private static java.sql.Connection sqlConnection;
    
    public static java.sql.Connection getConnection() {
        String driver = DatosConexionJDBC.driver;
        String url = DatosConexionJDBC.url;
        String user = DatosConexionJDBC.user;
        String pass = DatosConexionJDBC.pass;

        if(sqlConnection != null) return sqlConnection;

        try {
            Class.forName(driver);
            sqlConnection =  DriverManager.getConnection(url, user, pass);
        } catch (ClassNotFoundException | SQLException e) {
            e.printStackTrace();
        }

        return sqlConnection;
    }

    public static void closeConnection(java.sql.Connection sqlConnection) {
        try {
            sqlConnection.close();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
}
