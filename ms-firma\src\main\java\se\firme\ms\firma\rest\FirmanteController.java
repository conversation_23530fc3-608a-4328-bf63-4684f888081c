package se.firme.ms.firma.rest;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import se.firme.commons.exception.FirmaException;
import se.firme.commons.firmese.dto.ApiResponse;
import se.firme.commons.firmese.dto.ArchivoFirmaResponseDTO;
import se.firme.commons.firmese.dto.DocumentoDTO;
import se.firme.commons.firmese.dto.FirmaRequestDTO;
import se.firme.commons.firmese.dto.MultipleFirmaDTO;
import se.firme.commons.firmese.dto.SendFileEmailRequestDTO;
import se.firme.commons.firmese.util.Parameters;
import se.firme.commons.models.projection.IDocumentosEstado;
import se.firme.ms.firma.negocio.FirmaNegocio;

@RestController
@RefreshScope
@RequestMapping("/manager/v2")
public class FirmanteController extends IApiRest {

	@Autowired
	private FirmaNegocio firmaNegocio;
	@Autowired
	private HttpServletRequest request;

	@Override
	public HttpServletRequest getServletRequest() {
		return request;
	}

	@Override
	public ResponseEntity<ApiResponse> firmarDocumento(FirmaRequestDTO datosFirma, HttpServletRequest request) {
		// TODO Auto-generated method stub
		return null;
	}

	
	@PostMapping(path = "/solicitar-firma", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<ApiResponse> solicitarFirma(@RequestBody MultipleFirmaDTO datosFirma) {
		try {

			List<ArchivoFirmaResponseDTO> archivosSubidos = firmaNegocio.subirArchivoMultiple(datosFirma, getInfoRerquest(),Integer.parseInt(request.getHeader("X-USER-ID")));
			return new ResponseEntity<>(new ApiResponse.ok().data(archivosSubidos).build(), HttpStatus.OK);
		} catch (Exception e) {
			return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
					HttpStatus.BAD_REQUEST);
		}
	}
	

	@PostMapping(path = "/solicitar-firma-interesado", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<ApiResponse> solicitarFirmasIntervinientes(@RequestBody MultipleFirmaDTO datosFirma) {
		try {

			List<ArchivoFirmaResponseDTO> archivosSubidos = firmaNegocio.subirArchivoMultiplePreregistro(datosFirma, getInfoRerquest(),Integer.parseInt(request.getHeader("X-USER-ID")));
			return new ResponseEntity<>(new ApiResponse.ok().data(archivosSubidos).build(), HttpStatus.OK);
		} catch (Exception e) {
			return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
					HttpStatus.BAD_REQUEST);
		}
	}
	

	@Override
	public ResponseEntity<ApiResponse> firmarDocumentoMultiple(List<FirmaRequestDTO> datosFirma,
			HttpServletRequest request) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public ResponseEntity<ApiResponse> validarSmsMultiple(String ctoken, long usms) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public ResponseEntity<ApiResponse> enviarNotificacionMultiple(SendFileEmailRequestDTO datosFirma,
			HttpServletRequest request) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public ResponseEntity<ApiResponse> firmarArchivo(MultipartFile file, int idUsuario, int cantidadFirmas,
			HttpServletRequest request) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public ResponseEntity<ApiResponse> firmaPendiente(int idUsuario) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public ResponseEntity<Page<IDocumentosEstado>> getDocumentosPendientesPag(int idUsuario, Integer page,
			Integer per_page, String sortBy, String sortDirection) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public ResponseEntity<ApiResponse> documentoFirmado(int idUsuario) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public ResponseEntity<Page<ArchivoFirmaResponseDTO>> getDocumentosFirmadosPag(int idUsuario, Integer page,
			Integer per_page, String sortBy) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public ResponseEntity<ApiResponse> completar(DocumentoDTO documento) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public ResponseEntity<ApiResponse> findByIdUsuarioCodigoTransaccion(int idUsuario, String codigoTransaccion) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public ResponseEntity<ApiResponse> consultarArchivoByHash(String hash) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public ResponseEntity<ApiResponse> eliminarDocumentoPendiente(long idDocument) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	 @PostMapping(path="/fimar-documentos-multiple-firmante", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> firmarDocumentoMultipleFirma(@RequestBody MultipleFirmaDTO datosFirma, HttpServletRequest request) {
		try {
            String ipAddress = request.getHeader("X-FORWARDED-FOR");
            if (ipAddress == null) {
                ipAddress = request.getRemoteAddr();
            }
            switch (datosFirma.getTipoFirma()) {
			case Parameters.string.TIPO_FIRMA_MULTIPLE:
				firmaNegocio.iniciarProcesoFirma(datosFirma,Parameters.string.TIPO_FIRMA_MULTIPLE,Integer.parseInt(request.getHeader("X-USER-ID")));
				break;
			case Parameters.string.TIPO_FIRMA_OTHERS:
				firmaNegocio.iniciarProcesoFirma(datosFirma,Parameters.string.TIPO_FIRMA_OTHERS,Integer.parseInt(request.getHeader("X-USER-ID")));
				break;
			default:
				firmaNegocio.iniciarProcesoFirma(datosFirma,Parameters.string.TIPO_FIRMA_MULTIPLE,Integer.parseInt(request.getHeader("X-USER-ID")));
				break;
			}
            
            return new ResponseEntity<>(new ApiResponse.ok().build(), HttpStatus.OK);
        } catch (FirmaException e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(), HttpStatus.BAD_REQUEST);
        }
	}

	@Override
	public ResponseEntity<ApiResponse> verFirmantes( int idArchivo) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public ResponseEntity<ApiResponse> subirDocumentoMultiple(List<FirmaRequestDTO> datosFirma,
			HttpServletRequest request) {
		// TODO Auto-generated method stub
		return null;
	}

}
