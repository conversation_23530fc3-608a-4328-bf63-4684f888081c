/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.commons.firmese.dto;

/**
 * <AUTHOR>
 * @document ServicioDTO
 * @fecha jueves, enero 14 de 2021, 09:51:23 PM
 */
public class ServicioDTO {

    private long idServicio;
    private long cantidadFirmas;
    private String fechaVencimiento;
    private String fechaActualizacion;
    private String tipoServicio;
    private String endpointCBack;
    private boolean endpointCBackHabilitado;
    private String tipoValidacion;
    private int cantOtros;
    private String detalleTipoValidacion;

    private boolean notificarFirma;

    private boolean orisHabilitado;

    public long getIdServicio() {
        return idServicio;
    }

    public void setIdServicio(long idServicio) {
        this.idServicio = idServicio;
    }

    public long getCantidadFirmas() {
        return cantidadFirmas;
    }

    public void setCantidadFirmas(long cantidadFirmas) {
        this.cantidadFirmas = cantidadFirmas;
    }

    public String getFechaVencimiento() {
        return fechaVencimiento;
    }

    public void setFechaVencimiento(String fechaVencimiento) {
        this.fechaVencimiento = fechaVencimiento;
    }

    public String getFechaActualizacion() {
        return fechaActualizacion;
    }

    public void setFechaActualizacion(String fechaActualizacion) {
        this.fechaActualizacion = fechaActualizacion;
    }

    public String getTipoServicio() {
        return tipoServicio;
    }

    public void setTipoServicio(String tipoServicio) {
        this.tipoServicio = tipoServicio;
    }

    public String getEndpointCBack() {
        return endpointCBack;
    }

    public void setEndpointCBack(String endpointCBack) {
        this.endpointCBack = endpointCBack;
    }

    public String getTipoValidacion() {
        return tipoValidacion;
    }

    public void setTipoValidacion(String tipoValidacion) {
        this.tipoValidacion = tipoValidacion;
    }

    public int getCantOtros() {
        return cantOtros;
    }

    public void setCantOtros(int cantOtros) {
        this.cantOtros = cantOtros;
    }

    public String getDetalleTipoValidacion() {
        return detalleTipoValidacion;
    }

    public void setDetalleTipoValidacion(String detalleTipoValidacion) {
        this.detalleTipoValidacion = detalleTipoValidacion;
    }

    public boolean isNotificarFirma() {
        return notificarFirma;
    }

    public void setNotificarFirma(boolean notificarFirma) {
        this.notificarFirma = notificarFirma;
    }

    public boolean isEndpointCBackHabilitado() {
        return endpointCBackHabilitado;
    }

    public void setEndpointCBackHabilitado(boolean endpointCBackHabilitado) {
        this.endpointCBackHabilitado = endpointCBackHabilitado;
    }

    public boolean isOrisHabilitado() {
        return orisHabilitado;
    }

    public void setOrisHabilitado(boolean orisHabilitado) {
        this.orisHabilitado = orisHabilitado;
    }
}
