/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package se.firme.ms.models.service.helper;

import java.util.Date;
import se.firme.commons.exception.FirmaException;
import se.firme.commons.firmese.dto.PaqueteServicioDTO;
import se.firme.ms.datos.models.entity.PaqueteServicio;
import se.firme.ms.datos.models.entity.Servicio;

/**
 * @document PaqueteServicioHelper
 * <AUTHOR>
 * @fecha lunes, enero 18 de 2021, 10:15:44 AM
 */
public class PaqueteServicioHelper {

    public static PaqueteServicio convert(PaqueteServicioDTO dto) throws FirmaException {
        try {
            PaqueteServicio paqueteServicio=new PaqueteServicio();
            paqueteServicio.setCantidadFirmas(dto.getCantidadFirmas());
            paqueteServicio.setFechaRegistro(new Date());
            paqueteServicio.setIdServicio(new Servicio(dto.getIdServicio()));
            paqueteServicio.setJsonServicio(dto.getJsonServicio());
            paqueteServicio.setIdSku(dto.getIdSku());
            return paqueteServicio;
        } catch (Exception e) {
            throw new FirmaException("No se pudo hacer conversión de objeto ");
        }
    }

}
