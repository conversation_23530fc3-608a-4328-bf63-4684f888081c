package se.firme.commons.firmese.dto;

public class UserLoginDTO {
	private String usuario;
	private String direccionIP;
	private String firmaNavegador;

	public String getUsuario() {
		return usuario;
	}

	public void setUsuario(String usuario) {
		this.usuario = usuario;
	}

	public String getDireccionIP() {
		return direccionIP;
	}

	public void setDireccionIP(String direccionIP) {
		this.direccionIP = direccionIP;
	}

	public String getFirmaNavegador() {
		return firmaNavegador;
	}

	public void setFirmaNavegador(String firmaNavegador) {
		this.firmaNavegador = firmaNavegador;
	}

	@Override
	public String toString() {
		return "UserLoginDTO [usuario=" + usuario + ", direccionIP=" + direccionIP + ", firmaNavegador="
				+ firmaNavegador + "]";
	}

}
