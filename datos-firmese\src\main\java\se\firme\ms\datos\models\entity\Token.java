/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.datos.models.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "token")
@XmlRootElement
@NamedQueries({@NamedQuery(name = "Token.findAll", query = "SELECT t FROM Token t"), @NamedQuery(name = "Token.findByIdToken", query = "SELECT t FROM Token t WHERE t.idToken = :idToken"), @NamedQuery(name = "Token.findByFechaRegistro", query = "SELECT t FROM Token t WHERE t.fechaRegistro = :fechaRegistro"), @NamedQuery(name = "Token.findByFechaVencimiento", query = "SELECT t FROM Token t WHERE t.fechaVencimiento = :fechaVencimiento"), @NamedQuery(name = "Token.findByCodigoSms", query = "SELECT t FROM Token t WHERE t.codigoSms = :codigoSms"), @NamedQuery(name = "Token.findByTipo", query = "SELECT t FROM Token t WHERE t.tipo = :tipo"), @NamedQuery(name = "Token.findByActivo", query = "SELECT t FROM Token t WHERE t.activo = :activo")})
@JsonIgnoreProperties(ignoreUnknown = true)
public class Token implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @Basic(optional = false)
    @Column(name = "id_token")
    private String idToken;
    @Basic(optional = false)
    @Column(name = "fecha_registro")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaRegistro;
    @Basic(optional = false)
    @Column(name = "fecha_vencimiento")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaVencimiento;
    @Column(name = "codigo_sms")
    private String codigoSms;
    @Column(name = "codigo_transaccion")
    private String codigoTransaccion;
    @Basic(optional = false)
    @Column(name = "tipo")
    private int tipo;
    @Basic(optional = false)
    @Column(name = "activo")
    private boolean activo;
    @JoinColumn(name = "id_usuario", referencedColumnName = "id_usuario")
    @ManyToOne(optional = false)
    @JsonManagedReference
    private Usuario idUsuario;
    @Basic(optional = false)
    @Column(name = "id_archivo_firma")
    private Long idArchivoFirma;
    @Column(name = "email_firmante")
    private String emailFirmante;
    @Column(name = "ids")
    private String ids;

    public Token() {
    }

    public Token(String idToken) {
        this.idToken = idToken;
    }

    public Token(String idToken, Date fechaRegistro, Date fechaVencimiento, int tipo, boolean activo) {
        this.idToken = idToken;
        this.fechaRegistro = fechaRegistro;
        this.fechaVencimiento = fechaVencimiento;
        this.tipo = tipo;
        this.activo = activo;
    }

    public String getIdToken() {
        return idToken;
    }

    public void setIdToken(String idToken) {
        this.idToken = idToken;
    }

    public Date getFechaRegistro() {
        return fechaRegistro;
    }

    public void setFechaRegistro(Date fechaRegistro) {
        this.fechaRegistro = fechaRegistro;
    }

    public Date getFechaVencimiento() {
        return fechaVencimiento;
    }

    public void setFechaVencimiento(Date fechaVencimiento) {
        this.fechaVencimiento = fechaVencimiento;
    }

    public String getCodigoSms() {
        return codigoSms;
    }

    public void setCodigoSms(String codigoSms) {
        this.codigoSms = codigoSms;
    }

    public int getTipo() {
        return tipo;
    }

    public void setTipo(int tipo) {
        this.tipo = tipo;
    }

    public boolean getActivo() {
        return activo;
    }

    public void setActivo(boolean activo) {
        this.activo = activo;
    }

    public Usuario getIdUsuario() {
        return idUsuario;
    }

    public void setIdUsuario(Usuario idUsuario) {
        this.idUsuario = idUsuario;
    }

    public String getCodigoTransaccion() {
        return codigoTransaccion;
    }

    public void setCodigoTransaccion(String codigoTransaccion) {
        this.codigoTransaccion = codigoTransaccion;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (idToken != null ? idToken.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Token)) {
            return false;
        }
        Token other = (Token) object;
        if ((this.idToken == null && other.idToken != null) || (this.idToken != null && !this.idToken.equals(other.idToken))) {
            return false;
        }
        return true;
    }

    public Long getIdArchivoFirma() {
        return idArchivoFirma;
    }

    public void setIdArchivoFirma(Long idArchivoFirma) {
        this.idArchivoFirma = idArchivoFirma;
    }

    public String getEmailFirmante() {
        return emailFirmante;
    }

    public void setEmailFirmante(String emailFirmante) {

        if (emailFirmante != null) {
            emailFirmante = emailFirmante.toLowerCase().trim().replace(" ", "");
        }

        this.emailFirmante = emailFirmante;
    }

    public String getIds() {
        return ids;
    }

    public void setIds(String ids) {
        this.ids = ids;
    }

    @Override
    public String toString() {
        return "Token [idToken=" + idToken + ", fechaRegistro=" + fechaRegistro + ", fechaVencimiento=" + fechaVencimiento + ", codigoSms=" + codigoSms + ", codigoTransaccion=" + codigoTransaccion + ", tipo=" + tipo + ", activo=" + activo + ", idUsuario=" + idUsuario + ", idArchivoFirma=" + idArchivoFirma + ", emailFirmante=" + emailFirmante + ", ids=" + ids + "]";
    }


}
