/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.datos.models.dao;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import se.firme.ms.datos.models.entity.Servicio;

/**
 * @document IServicioDAO
 * <AUTHOR>
 * @fecha miércoles, enero 13 de 2021, 02:02:57 PM
 */
public interface IServicioDAO extends PagingAndSortingRepository<Servicio, Long> {

    @Modifying(clearAutomatically = true)
    @Query(value = "update servicio set cantidad_firmas = ?1, cant_otros = ?2 where id_servicio = ?3", nativeQuery = true)
    public void actualizarCantidadfirmas(int cantidadFirmas, int cantOtros, Long idUsuario);

    @Query(value = "select * from servicio where id_servicio = ?1 ", nativeQuery = true)
    public Servicio findServicioById(Long idUsuario);

    @Modifying(clearAutomatically = true)
    @Query(value = "update servicio set cantidad_firmas = ?1 , fecha_actualizacion = NOW(), fecha_vencimiento = DATE_ADD(now(), INTERVAL ?2 DAY ), cant_otros = ?4 where id_servicio = ?3", nativeQuery = true)
    public void recargarCantidadfirmas(int cantidadFirmas, int tiempoValidez, long idusuario, long cantOtros);
    
    
    @Modifying(clearAutomatically = true)
    @Query(value = "update servicio set endpoint_cback = ?2, endpoint_cback_habilitado = ?3 where id_servicio = ?1 ", nativeQuery = true)
    public void actualizarServicio(Long idUsuario, String endpoint, boolean endpointCBackHabilitado);

    @Modifying(clearAutomatically = true)
    @Query(value = "update servicio set notificar_firma = ?2 where id_servicio = ?1 ", nativeQuery = true)
    public void actualizarNotificarFirma(Long idUsuario, boolean notificar_firma);
}
