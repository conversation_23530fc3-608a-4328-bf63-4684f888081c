package se.firme.ms.datos.models.dao;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import se.firme.ms.datos.models.entity.UsuarioFirmaArchivoOtp;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface IUsuarioFirmaArchivoOtpDao extends PagingAndSortingRepository<UsuarioFirmaArchivoOtp, Long> {
    
    @Modifying
    @Query(value = "INSERT INTO usuario_firma_archivo_otp (id_proceso_firma, id_archivo_firma, estado) VALUES (?1, ?2, 1)", nativeQuery = true)
    public void associateProcesoWithArchivo(long idProcesoFirma, long idArchivoFirma);
    
    @Query(value = "SELECT * FROM usuario_firma_archivo_otp WHERE id_proceso_firma = ?1", nativeQuery = true)
    public List<UsuarioFirmaArchivoOtp> findByIdProcesoFirma(long idProcesoFirma);

    // Validar si un archivo específico está asociado a un proceso OTP
    @Query(value = "SELECT COUNT(*) FROM usuario_firma_archivo_otp ufao " +
           "INNER JOIN proceso_firma pf ON ufao.id_proceso_firma = pf.id_proceso_firma " +
           "WHERE pf.otp = ?1 AND pf.id_usuario = ?2 AND ufao.id_archivo_firma = ?3 " +
           "AND ufao.estado = 1", nativeQuery = true)
    public int validateArchivoForOtp(String otp, long idUsuario, long idArchivo);

    // Obtener todos los archivos válidos para un OTP específico
    @Query(value = "SELECT ufao.* FROM usuario_firma_archivo_otp ufao " +
           "INNER JOIN proceso_firma pf ON ufao.id_proceso_firma = pf.id_proceso_firma " +
           "WHERE pf.otp = ?1 AND pf.id_usuario = ?2 AND ufao.estado = 1 " +
           "AND pf.activo = 1 AND pf.fecha_vencimiento > NOW()", 
           nativeQuery = true)
    public List<UsuarioFirmaArchivoOtp> findValidArchivosForOtp(String otp, long idUsuario);

    // Validar que el proceso esté activo y tenga archivos asociados
    @Query(value = "SELECT COUNT(*) FROM usuario_firma_archivo_otp ufao " +
           "INNER JOIN proceso_firma pf ON ufao.id_proceso_firma = pf.id_proceso_firma " +
           "WHERE pf.otp = ?1 AND pf.id_usuario = ?2 AND pf.activo = 1 " +
           "AND pf.fecha_vencimiento > NOW() AND ufao.estado = 1", 
           nativeQuery = true)
    public int validateActiveOtpWithFiles(String otp, long idUsuario);

    //método para desactivar registros cuando se usa el OTP
    @Modifying
    @Query(value = "UPDATE usuario_firma_archivo_otp ufao " +
           "INNER JOIN proceso_firma pf ON ufao.id_proceso_firma = pf.id_proceso_firma " +
           "SET ufao.estado = 0 " +
           "WHERE pf.otp = ?1 AND pf.id_usuario = ?2", 
           nativeQuery = true)
    public void desactivarRegistrosPorOtp(String otp, long idUsuario);

    @Modifying
    @Query(value = "DELETE FROM usuario_firma_archivo_otp WHERE id_archivo_firma = ?1", nativeQuery = true)
    public void deleteByIdArchivoFirma(long idArchivoFirma);
}