/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.models.service;

import se.firme.commons.firmese.dto.*;
import se.firme.ms.datos.models.dao.ISolicitudFirmaDao;
import se.firme.ms.datos.models.dao.IUsuarioFirmaArchivoOtpDao;
import se.firme.ms.datos.models.entity.Usuario;
import se.firme.ms.models.service.interfaz.IUsuarioService;
import se.firme.ms.models.service.interfaz.IParametroService;
import se.firme.ms.models.service.helper.ArchivoFirmaHelper;
import se.firme.ms.models.service.interfaz.IArchivoFirmaService;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import se.firme.commons.exception.FirmaException;
import se.firme.commons.firmese.util.Parameters;
import se.firme.commons.models.projection.IArchivoFirma;
import se.firme.commons.models.projection.IDatosArchivo;
import se.firme.commons.models.projection.IDocumentosEstado;
import se.firme.commons.models.projection.IFirmante;
import se.firme.ms.datos.models.dao.IArchivoFirmaDao;
import se.firme.ms.datos.models.dao.IFirmaArchivoUsuarioDao;
import se.firme.ms.datos.models.entity.ArchivoFirma;
import se.firme.ms.datos.models.entity.FirmaArchivoUsuario;


/**
 * <AUTHOR>
 */
@Service
public class ArchivoFirmaServiceImpl implements IArchivoFirmaService {

    @Autowired
    IParametroService parametroService;

    @Autowired
    IArchivoFirmaDao archivoFirmaDao;

    @Autowired
    private FirmaArchivoUsuarioServiceImpl firmaArchivoUsuarioService;

    @Autowired
    IUsuarioService usuarioService;

    @Autowired
    private IFirmaArchivoUsuarioDao firmaArchivoUsuarioDao;

    @Autowired
    private ISolicitudFirmaDao solicitudFirmaDao;

    @Autowired
    private IUsuarioFirmaArchivoOtpDao usuarioFirmaArchivoOtpDao;

    private static Logger logger = Logger.getLogger(ArchivoFirmaServiceImpl.class.getName());

    @Override
    @Transactional
    public ArchivoFirma guardarArchivoFirma(String nombreArchivo, String hashArchivo, int cantidadFirmas, int estado,
                                            String ip, long idUsuario, String rutaRelativa) {
        return guardarArchivoFirma(nombreArchivo, hashArchivo, cantidadFirmas, estado, ip, idUsuario, rutaRelativa, null);
    }

    @Override
    @Transactional
    public ArchivoFirma guardarArchivoFirma(String nombreArchivo, String hashArchivo, int cantidadFirmas, int estado,
                                            String ip, long idUsuario, String rutaRelativa, String descripcion) {
        ArchivoFirma archivoFirmado = new ArchivoFirma();
        archivoFirmado.setCantidadFirmas(cantidadFirmas);
        archivoFirmado.setCantidadFirmado(0);
        archivoFirmado.setHashArchivo(hashArchivo);
        archivoFirmado.setNombreArchivo(nombreArchivo);
        archivoFirmado.setEstado(estado);
        archivoFirmado.setIp(ip);
        archivoFirmado.setIdUsuario(idUsuario);
        archivoFirmado.setRutaRelativaArchivo(rutaRelativa);

        if (descripcion != null && !descripcion.trim().isEmpty()) {
            archivoFirmado.setDescripcion(descripcion);
        }

        return archivoFirmaDao.save(archivoFirmado);
    }

    @Override
    @Transactional
    public ArchivoFirma validarSePuedeFirmar(String hashArchivo) {
        List<ArchivoFirma> firmaList = getArchivoFirmadoByHash(hashArchivo);

        if (firmaList.isEmpty()) {
            return new ArchivoFirma();
        } else {
            // validamos la cantidad de firmas
            ArchivoFirma docFirmado = firmaList.get(0);

            if (getFirmasArchivo(docFirmado.getIdArchivoFirma()).size() < docFirmado.getCantidadFirmas()) {
                return docFirmado;
            } else {
                return null;
            }
        }
    }

    @Override
    @Transactional
    public List<ArchivoFirma> getArchivoFirmadoByHash(String hashArchivo) {
        return archivoFirmaDao.findByBothHash(hashArchivo, hashArchivo);
    }

    @Transactional
    private List<FirmaArchivoUsuario> getFirmasArchivo(long idArchivoFirma) {
        return firmaArchivoUsuarioService.findByIdArchivoFirma(idArchivoFirma);
    }

    @Override
    @Transactional
    public void actualizarCantidadConsultas(long idArchivoFirma) {
        archivoFirmaDao.actualizarCantidadConsultas(idArchivoFirma);
    }

    @Override
    public List<IDocumentosEstado> findByEstado(long idUsuario, int estado) {
        return archivoFirmaDao.findByEstado(idUsuario, estado);
    }

    @Override
    public Page<IDocumentosEstado> findByEstadoPag(int idUsuario, int estado, String nombre, Pageable paging) throws FirmaException {

        try{
            Usuario usuario = usuario.Service.findById(idUsario);
            if(usuario == null){
                throw new FirmaException("No se pudo realizar la busqueda porque el usuario no existe");
            }
            logger.info("Realizando busqueda para la tabla general de firmados y propietario");
            Page<IDocumentosEstado> pages1 = archivoFirmaDao.findDocumentosUnificadosConBusqueda(idUsuario, estado, usuario.getCorreoElectronico(), nombre, paging);
    	    return pages1;
        }
        catch(Exception e){
            logger.severe("Error al buscar documentos unificados: ");
            throw new FirmaException("Error al buscar documentos unificados: "+ e.getMessage());
        }
        
    }

    @Override
    public List<IDocumentosEstado> findByIdUsuarioCodigoTransaccion(long idUsuario, String codigoTransaccion) {
        return archivoFirmaDao.findByIdUsuarioCodigoTransaccion(idUsuario, codigoTransaccion);
    }

    @Override
    public ArchivoFirma findById(long idArchivoFirma) {
        return archivoFirmaDao.findById(idArchivoFirma).orElse(null);
    }

    @Override
    @Transactional
    public void actualizarCantidadFirmas(int cantidadFirmado, long idArchivoFirma) {
        archivoFirmaDao.actualizarCantidadFirmas(cantidadFirmado, idArchivoFirma);
    }

    @Override
    @Transactional
    public void actualizarEstadoArchivoFirma(long idArchivoFirma, int idEstado) {
        archivoFirmaDao.actualizarEstadoArchivoFirma(idArchivoFirma, idEstado);
    }

    @Override
    public List<IDatosArchivo> findByArchivoEstado(long idDusuario, int i) {
        return archivoFirmaDao.findByArchivoEstado(idDusuario, i);
    }

    @Override
    public List<ArchivoFirma> findByIdUsuarioArchivoFirmado(long idDusuario) {
        return archivoFirmaDao.findByIdUsuarioArchivoFirmado(idDusuario);
    }
    

    @Override
    public Page<ArchivoFirma> findByIdUsuarioArchivoFirmadoPag(long idDusuario, Pageable paging) {
        return archivoFirmaDao.findAllByUserAndEstado(idDusuario, 2, paging);
    }

    @Override
    public List<ArchivoFirma> findByCodProcess(String csms, long idUsuario) throws FirmaException {
        return archivoFirmaDao.findByCodProcess(csms, idUsuario);
    }

    @Override
    public List<ArchivoFirma> findByIds(List<String> idsArchivos) {
        return archivoFirmaDao.findByIds(idsArchivos);
    }

    @Override
    public List<ArchivoFirma> findByHash(String hash) {
        return archivoFirmaDao.findByHash(hash);
    }

    @Override
    @Transactional
    public boolean eliminarArchivo(long idDocument, long idUsuario) throws FirmaException {
        try {
            Optional<ArchivoFirma> optional = archivoFirmaDao.findById(idDocument);
            if (optional.isPresent()) {
                if (idUsuario == optional.get().getIdUsuario()) {
                    List<FirmaArchivoUsuario> firmas = firmaArchivoUsuarioDao.findByIdArchivoFirma(idDocument);

                    if (firmas.size() > 0) {
                        throw new FirmaException("el documento ya contiene firmas");
                    }

                    // Eliminar registros relacionados en el orden correcto
                    solicitudFirmaDao.deleteAllByIdArchivoFirma(idDocument);
                    usuarioFirmaArchivoOtpDao.deleteByIdArchivoFirma(idDocument);
                    firmaArchivoUsuarioDao.deletePendingDocument(idDocument, idUsuario);
                    archivoFirmaDao.deletePendingDocument(idDocument, idUsuario);
                    return true;
                }
                throw new FirmaException("Usuario no es el propietario del documento");
            }
            throw new FirmaException("Documento no existe");
        } catch (Exception e) {
            logger.log(Level.SEVERE, e.getMessage());
            throw new FirmaException("No se pudo completar el proceso de eliminación de documento, " + e.getMessage());
        }
    }

    @Override
    public void update(ArchivoFirma archivoFirma) throws FirmaException {
        try {
            archivoFirmaDao.save(archivoFirma);
        } catch (Exception e) {
            logger.severe("Error while updating document with id " + archivoFirma.getIdArchivoFirma() + " :: " + e.getMessage());
            throw new FirmaException("No se pudo actualizar el registro de archivo firma");
        }
    }

    @Override
    public List<ArchivoFirmaResponseDTO> buscarArchivos(String ids) throws FirmaException {
        try {

            return ArchivoFirmaHelper.convertList(buscarArchivosFirma(ids));

        } catch (Exception e) {
            throw new FirmaException(e.getMessage());
        }
    }

    public void verificarTipoFirma(List<FirmaRequestDTO> documentos) throws FirmaException {
        try {
            for (FirmaRequestDTO dto : documentos) {
                ArchivoFirma archivoFirma = findById(dto.getIdArchivo());
                if (archivoFirma != null) {
                    if (Parameters.string.TIPO_FIRMA_MULTIPLE.equals(archivoFirma.getTipoFirma())
                            || Parameters.string.TIPO_FIRMA_SINGLE.equals(archivoFirma.getTipoFirma())) {
                        logger.info(" ya inicio proceso de firma de tipo " + archivoFirma.getTipoFirma());
                        throw new FirmaException("El archivo con nombre " + archivoFirma.getNombreArchivo()
                                + " ya inicio proceso de firma de tipo " + archivoFirma.getTipoFirma());
                    }
                } else {
                    throw new FirmaException("El documento con nombre " + dto.getNombreArchivo() + " ya existe");
                }
            }
        } catch (FirmaException e) {
            throw e;
        }

    }

    @Override
    public List<ArchivoFirma> buscarArchivosFirma(String ids) throws FirmaException {
        try {
            logger.info("IDs consultados: " + ids);
            List<String> idsList = Arrays.asList(ids.split("\\-"));
            logger.info("IDs en lista: " + idsList.size());
            List<ArchivoFirma> lista = archivoFirmaDao.buscarArchivos(idsList);
            if (lista != null && !lista.isEmpty()) {
                logger.info("Cantidad de archivos encontrados con los id " + ids + " :: " + lista.size());
                return lista;
            }
            throw new FirmaException("No encontraron  archivo firma con ids " + ids);
        } catch (Exception e) {
            throw new FirmaException("No encontró archivo firma :: " + e.getMessage());
        }
    }

    @Override
    public DetalleSolicitudDTO buscarFirmantesSolicitados(int idUsuario, int idArchivo) throws FirmaException {

        ArchivoFirma archivoFirma = findById(idArchivo);
        if (archivoFirma != null) {
            String emailFirmantes = archivoFirma.getEmailFirmantes();
            ArrayList<Integer> ids = new ArrayList<>();

            if (emailFirmantes != null) {
                String[] emails = emailFirmantes.split(",");

                for (String email : emails) {
                    try {
                        ids.add(usuarioService.findByEmail(email.trim()).getIdUsuario().intValue());
                    } catch (FirmaException e) {
                        logger.severe("Error while searching user with email " + email.trim() + " :: " + e.getMessage());
                    }
                }
            }

            if (idUsuario != archivoFirma.getIdUsuario() && !ids.contains(idUsuario)) {
                throw new FirmaException("No eres propietario del archivo solicitado");
            }
            DetalleSolicitudDTO detalleSolicitudDTO = new DetalleSolicitudDTO();
            detalleSolicitudDTO.setNombreArchivo(archivoFirma.getNombreArchivo());
            detalleSolicitudDTO.setTipoFirma(archivoFirma.getTipoFirma());
            detalleSolicitudDTO.setEstado(archivoFirma.getEstado() + "");

            List<IFirmante> lista = archivoFirmaDao.buscarFirmantesSolicitados((int) archivoFirma.getIdUsuario(),
                    Parameters.string.TOKEN_TIPO_FIRMA_DOCUMENTO, "%-" + idArchivo + "-%");

            if (archivoFirma.getTipoFirma().equals("MULTIPLE") && idUsuario != archivoFirma.getIdUsuario()) {
                try {
                    Usuario propietario = usuarioService.findById(archivoFirma.getIdUsuario());
                    if (propietario != null) {
                        IFirmante firmante = new IFirmante() {
                            @Override
                            public String getFirmante() {
                                return propietario.getCorreoElectronico();
                            }

                            @Override
                            public String getSolicitud() {
                                return archivoFirma.getFechaRegistro().toString();
                            }

                            @Override
                            public String getVencimiento() {
                                return null;
                            }

                            @Override
                            public String getCodigo() {
                                return null;
                            }

                            @Override
                            public String getNombreFirmante() {
                                return propietario.getNombreCompleto();
                            }

                            @Override
                            public String getActivo() {
                                return "SI";
                            }

                            @Override
                            public Integer getIdusuario() {
                                return Math.toIntExact(propietario.getIdUsuario());
                            }
                        };

                        lista.add(firmante);
                    }
                } catch (Exception e) {
                    logger.severe("Error while searching user with id " + archivoFirma.getIdUsuario() + " :: " + e.getMessage());
                }
            }

            List<FirmanteSolicitudDTO> firmantes = new ArrayList<>();
            for (IFirmante firmante : lista) {
                logger.info("Nombre de firmante: " + firmante.getNombreFirmante() + " ID " + firmante.getIdusuario());
                FirmanteSolicitudDTO dto = new FirmanteSolicitudDTO();
                dto.setCorreoFirmante(firmante.getFirmante());
                dto.setNombreFirmante(firmante.getNombreFirmante());
                dto.setFechaSolicitud(firmante.getSolicitud());
                dto.setVigenciaSolicitud(firmante.getVencimiento());
                dto.setSolicitudActiva(firmante.getActivo());
                if (firmante.getIdusuario() == null) {
                    dto.setDetalleSolicitud("Interviniente no registrado sin firma");
                } else {
                    List<FirmaArchivoUsuario> firmasList = firmaArchivoUsuarioService
                            .findByIdArchivoFirmaAndIdusuario(idArchivo, firmante.getIdusuario());
                    if (firmasList != null && !firmasList.isEmpty()) {
                        FirmaArchivoUsuario firmaArchivoUsuario = firmasList.get(0);

                        logger.info("Firmado: " + firmaArchivoUsuario.getNombreArchivoFirma());
                        dto.setDetalleSolicitud("Firmado por interviniente");
                        dto.setFechaFirma(firmaArchivoUsuario.getFechaRegistro().toString());
                        dto.setIpFirma(firmaArchivoUsuario.getIp());
                    } else {
                        dto.setDetalleSolicitud("Interviniente sin firma");
                    }
                }
                firmantes.add(dto);
            }
            detalleSolicitudDTO.setFirmantes(firmantes);

            return detalleSolicitudDTO;
        }
        throw new FirmaException("No se encontró registro de archivo");
    }

    public Page<IArchivoFirma> findArchivosFirmados(int idUsuario, Pageable pageable) {
        List<IArchivoFirma> lista = archivoFirmaDao.findArchivosFirmados(idUsuario, pageable);

        final int toIndex = Math.min((pageable.getPageNumber() + 1) * pageable.getPageSize(), lista.size());
        final int fromIndex = Math.max(toIndex - pageable.getPageSize(), 0);
        System.out.println("indice de " + fromIndex + " a " + toIndex);
        Page<IArchivoFirma> pages = new PageImpl<IArchivoFirma>(lista, pageable, lista.size());

        return pages;
    }

    @Override
    public List<ArchivoFirma> findByIdUsuario(Long idUsuario) {
        return archivoFirmaDao.findByIdUsuarioArchivoFirmado(idUsuario);
    }

    /**
     * Busca archivos firmados recientemente por un usuario
     */
    public List<ArchivoFirma> findRecentlySignedByUser(Long idUsuario) {
        try {
            // Fecha límite: 5 minutos atrás
            Date tiempoLimite = new Date(System.currentTimeMillis() - (5 * 60 * 1000));
            
            logger.info("🔍 Buscando archivos firmados para usuario ID: " + idUsuario + " después de: " + tiempoLimite);
            
            // 1. USAR MÉTODO EXISTENTE: Obtener todos los archivos firmados del usuario
            List<ArchivoFirma> todosArchivos = archivoFirmaDao.findByIdUsuarioArchivoFirmado(idUsuario);
            List<ArchivoFirma> archivosRecientes = new ArrayList<>();
            
            logger.info("📄 Total archivos del usuario: " + todosArchivos.size());
            
            // 2. FILTRAR por fecha de registro reciente
            for (ArchivoFirma archivo : todosArchivos) {
                if (archivo.getFechaRegistro() != null && archivo.getFechaRegistro().after(tiempoLimite)) {
                    archivosRecientes.add(archivo);
                    logger.info("✅ Archivo reciente encontrado: " + archivo.getNombreArchivo() + " - " + archivo.getFechaRegistro());
                }
            }
            
            logger.info("📄 Archivos recientes encontrados: " + archivosRecientes.size());
            return archivosRecientes;
            
        } catch (Exception e) {
            logger.severe("Error buscando archivos firmados recientemente: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }
    
    /**
    * Busca archivos TyC individuales creados para un firmante específico por email y usuario propietario.
    */
    public List<ArchivoFirma> findArchivosTyCIndividualesPorEmail(String emailFirmante, Long idUsuarioPropietario) {
        List<ArchivoFirma> resultado = new ArrayList<>();
        try {
            // Buscar todos los archivos del usuario propietario
            List<ArchivoFirma> archivos = findByIdUsuario(idUsuarioPropietario);
            if (archivos == null || archivos.isEmpty()) {
                return resultado;
            }

            // Normalizar email para comparación en ruta
            String emailNormalizado = emailFirmante.toLowerCase().replaceAll("[^a-zA-Z0-9]", "_");

            for (ArchivoFirma archivo : archivos) {
                String nombre = archivo.getNombreArchivo() != null ? archivo.getNombreArchivo().toLowerCase() : "";
                String ruta = archivo.getRutaRelativaArchivo() != null ? archivo.getRutaRelativaArchivo().toLowerCase() : "";

                // Verificar si es TyC por nombre
                boolean esTyC = nombre.contains("tyc") || nombre.contains("términos") || nombre.contains("terminos") || nombre.contains("condiciones")
                                || nombre.contains("autorizacion") || nombre.contains("autorización") || nombre.contains("datos personales");

                // Verificar si la ruta contiene el email normalizado (patrón de directorio individual)
                boolean rutaContieneEmail = ruta.contains(emailNormalizado);

                // Solo agregar si es TyC y la ruta corresponde al email del firmante
                if (esTyC && rutaContieneEmail) {
                    resultado.add(archivo);
                }
            }
        } catch (Exception e) {
            logger.warning("Error buscando archivos TyC individuales para " + emailFirmante + ": " + e.getMessage());
        }
        return resultado;
    }
    
    // implementación de la busqueda cuando para archivos firmados
    public Page<IDocumentosEstado> findFirmadosConBusqueda(int userId, int estado, String nombre, Pageable paging) throws FirmaException {
        try{
            logger.info("Realizando busqueda para archivos firmados");
            Page<IDocumentosEstado> pages1 = archivoFirmaDao.findFirmadosConBusqueda(userId, estado, nombre, paging);
            return pages1;
        } catch(Exception e){
            logger.severe("Error al buscar documentos firmados: ");
            throw new FirmaException("Error al buscar documentos firmados"+ e.getMessage());
        }
    }

}
