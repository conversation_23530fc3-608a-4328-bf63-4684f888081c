package se.firme.ms.datos.models.dto;

public class PlantillaRequestDTO {
    private Long idPlantilla;
    private String descripcion;
    
    // Constructores
    public PlantillaRequestDTO() {}
    
    public PlantillaRequestDTO(Long idPlantilla, String descripcion) {
        this.idPlantilla = idPlantilla;
        this.descripcion = descripcion;
    }
    
    // Getters y Setters
    public Long getIdPlantilla() {
        return idPlantilla;
    }
    
    public void setIdPlantilla(Long idPlantilla) {
        this.idPlantilla = idPlantilla;
    }
    
    public String getDescripcion() {
        return descripcion;
    }
    
    public void setDescripcion(String descripcion) {
        this.descripcion = descripcion;
    }
    
    @Override
    public String toString() {
        return "PlantillaRequestDTO{" +
                "idPlantilla=" + idPlantilla +
                ", descripcion='" + descripcion + '\'' +
                '}';
    }
}
