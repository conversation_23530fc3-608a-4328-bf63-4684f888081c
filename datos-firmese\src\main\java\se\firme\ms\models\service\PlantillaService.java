package se.firme.ms.models.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import se.firme.commons.exception.FirmaException;
import se.firme.commons.firmese.util.Utilities;
import se.firme.ms.datos.models.dao.IPlantillaDocumentoRepository;
import se.firme.ms.datos.models.dto.CargarPlantillaDTO;
import se.firme.ms.datos.models.entity.PlantillaDocumento;

import java.io.FileOutputStream;
import java.io.IOException;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.logging.Logger;

@Service
public class PlantillaService {
    
    private static Logger logger = Logger.getLogger(PlantillaService.class.getName());
    
    @Autowired
    private IPlantillaDocumentoRepository plantillaRepository;
    
    @Autowired
    private Environment env;
    
    @Transactional
    public PlantillaDocumento cargarPlantillaDesdeJson(CargarPlantillaDTO request) throws FirmaException {
        
        try {
            logger.info("Iniciando carga de plantilla: " + request.getNombrePlantilla());
            
            // 1. Validar datos obligatorios
            validarRequest(request);
            
            // 2. Decodificar archivo PDF
            byte[] archivoBytes = Base64.getDecoder().decode(request.getArchivo64());
            
            // 3. Generar ruta para la plantilla
            String rutaRelativa = generarRutaPlantilla(request);
            String rutaBase = env.getProperty("routes.custom.file", "/opt/firmese/files");
            String directorioCompleto = rutaBase + "/plantillas/" + rutaRelativa;
            String rutaCompleta = directorioCompleto + request.getNombreArchivo();
            
            // 4. Crear directorio y guardar archivo
            Utilities.crearDirectorio(directorioCompleto, true);
            guardarArchivo(archivoBytes, rutaCompleta);
            
            // 5. Generar hash del archivo
            String hashArchivo = generarHash(archivoBytes);
            
            // 6. Verificar que no exista una plantilla con el mismo hash
            PlantillaDocumento existente = plantillaRepository.findByHashArchivo(hashArchivo);
            if (existente != null) {
                throw new FirmaException("Ya existe una plantilla con el mismo contenido");
            }
            
            // 7. Crear y guardar entidad PlantillaDocumento
            PlantillaDocumento plantilla = new PlantillaDocumento();
            plantilla.setNombrePlantilla(request.getNombrePlantilla());
            plantilla.setDescripcion(request.getDescripcion());
            plantilla.setNombreArchivo(request.getNombreArchivo());
            plantilla.setRutaRelativaArchivo(rutaRelativa + request.getNombreArchivo());
            plantilla.setHashArchivo(hashArchivo);
            plantilla.setTipoDocumento(request.getTipoDocumento());
            plantilla.setTipoFirma(request.getTipoFirma());
            plantilla.setIdUsuarioCreador(request.getIdUsuarioCreador());
            plantilla.setActiva(true);
            // Sin establecer fechaCreacion
            
            plantilla = plantillaRepository.save(plantilla);
            
            logger.info("Plantilla cargada exitosamente con ID: " + plantilla.getIdPlantilla());
            logger.info("Archivo guardado en: " + rutaCompleta);
            
            return plantilla;
            
        } catch (FirmaException e) {
            logger.severe("Error de negocio cargando plantilla: " + e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.severe("Error inesperado cargando plantilla: " + e.getMessage());
            throw new FirmaException("Error cargando plantilla: " + e.getMessage());
        }
    }
    
    private void validarRequest(CargarPlantillaDTO request) throws FirmaException {
        if (request.getNombrePlantilla() == null || request.getNombrePlantilla().trim().isEmpty()) {
            throw new FirmaException("El nombre de la plantilla es obligatorio");
        }
        if (request.getNombreArchivo() == null || request.getNombreArchivo().trim().isEmpty()) {
            throw new FirmaException("El nombre del archivo es obligatorio");
        }
        if (request.getArchivo64() == null || request.getArchivo64().trim().isEmpty()) {
            throw new FirmaException("El archivo PDF es obligatorio");
        }
        if (request.getIdUsuarioCreador() == null) {
            throw new FirmaException("El ID del usuario creador es obligatorio");
        }
        if (!request.getNombreArchivo().toLowerCase().endsWith(".pdf")) {
            throw new FirmaException("Solo se permiten archivos PDF");
        }
    }
    
    private String generarRutaPlantilla(CargarPlantillaDTO request) {
        // Para plantillas, usar estructura específica
        String tipoDoc = request.getTipoDocumento() != null ? 
            request.getTipoDocumento().toLowerCase().replaceAll("[^a-zA-Z0-9]", "_") : "general";
        
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String fechaPath = sdf.format(new Date());
        
        // Estructura para plantillas: plantillas/{tipo}/{fecha}/
        return tipoDoc + "/" + fechaPath + "/";
    }
    
    private void guardarArchivo(byte[] archivoBytes, String rutaCompleta) throws FirmaException {
        try {
            try (FileOutputStream fos = new FileOutputStream(rutaCompleta)) {
                fos.write(archivoBytes);
                fos.flush();
            }
            logger.info("Archivo guardado exitosamente: " + rutaCompleta);
        } catch (IOException e) {
            throw new FirmaException("Error guardando archivo: " + e.getMessage());
        }
    }
    
    private String generarHash(byte[] archivoBytes) throws FirmaException {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(archivoBytes);
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            throw new FirmaException("Error generando hash del archivo: " + e.getMessage());
        }
    }
}
