package se.firme.ms.token.rest.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import co.venko.ms.models.entity.AdmUsuario;

@FeignClient(name = "vms-usuario")
public interface UsuarioAdminClient {
	@PostMapping("/usuario")
    public AdmUsuario guardarUsuarioAdm(@RequestBody AdmUsuario usuario);
}
