/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Enum.java to edit this template
 */
package se.firme.ms.utils;

/**
 *
 * <AUTHOR>
 */
public enum UserParameter {
    ADD_WATERMARK("enable"),
    ADD_STAMP("enable"),
    ADD_VERTICAL_STAMP("enable"),
    ADD_OTHER("SI");
    
    private final String expected;

    private UserParameter(String expected) {
        this.expected = expected;
    }

    public String getExpected() {
        return expected;
    }

}
