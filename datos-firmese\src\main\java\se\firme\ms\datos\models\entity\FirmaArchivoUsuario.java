/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.datos.models.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.xml.bind.annotation.XmlRootElement;
import org.hibernate.annotations.CreationTimestamp;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "firma_archivo_usuario")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "FirmaArchivoUsuario.findAll", query = "SELECT f FROM FirmaArchivoUsuario f")
    , @NamedQuery(name = "FirmaArchivoUsuario.findByIdFirmaArchivoUsuario", query = "SELECT f FROM FirmaArchivoUsuario f WHERE f.idFirmaArchivoUsuario = :idFirmaArchivoUsuario")
    , @NamedQuery(name = "FirmaArchivoUsuario.findBySubio", query = "SELECT f FROM FirmaArchivoUsuario f WHERE f.subio = :subio")
    , @NamedQuery(name = "FirmaArchivoUsuario.findByHashArchivo", query = "SELECT f FROM FirmaArchivoUsuario f WHERE f.hashArchivo = :hashArchivo")
    , @NamedQuery(name = "FirmaArchivoUsuario.findByFechaRegistro", query = "SELECT f FROM FirmaArchivoUsuario f WHERE f.fechaRegistro = :fechaRegistro")})
@JsonIgnoreProperties(ignoreUnknown=true)
public class FirmaArchivoUsuario implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id_firma_archivo_usuario")
    private Long idFirmaArchivoUsuario;
    @Basic(optional = false)
    @Column(name = "subio")
    private boolean subio;
    @Basic(optional = false)
    @Column(name = "hash_archivo")
    private String hashArchivo;
    @Basic(optional = false)
    @Column(name = "fecha_registro")
    @Temporal(TemporalType.TIMESTAMP)
    @CreationTimestamp
    private Date fechaRegistro;
    @JoinColumn(name = "id_archivo_firma", referencedColumnName = "id_archivo_firma")
    @ManyToOne(optional = false)
    @JsonBackReference
    private ArchivoFirma idArchivoFirma;
    @JoinColumn(name = "id_usuario", referencedColumnName = "id_usuario")
    @ManyToOne(optional = false)
    @JsonManagedReference
    private Usuario idUsuario;
    @Basic(optional = false)
    @Column(name = "ruta_relativa_archivo")
    private String rutaRelativaArchivo;
    @Basic(optional = false)
    @Column(name = "nombre_archivo_firma")
    private String nombreArchivoFirma;
    @Basic(optional = false)
    @Column(name = "ip")
    private String ip;
    @Column(name = "agente_navegador")
    private String agenteNavegador;

    public FirmaArchivoUsuario() {
    }

    public FirmaArchivoUsuario(Long idFirmaArchivoUsuario) {
        this.idFirmaArchivoUsuario = idFirmaArchivoUsuario;
    }

    public FirmaArchivoUsuario(Long idFirmaArchivoUsuario, boolean subio, String hashArchivo, Date fechaRegistro) {
        this.idFirmaArchivoUsuario = idFirmaArchivoUsuario;
        this.subio = subio;
        this.hashArchivo = hashArchivo;
        this.fechaRegistro = fechaRegistro;
    }

    public Long getIdFirmaArchivoUsuario() {
        return idFirmaArchivoUsuario;
    }

    public void setIdFirmaArchivoUsuario(Long idFirmaArchivoUsuario) {
        this.idFirmaArchivoUsuario = idFirmaArchivoUsuario;
    }

    public boolean getSubio() {
        return subio;
    }

    public void setSubio(boolean subio) {
        this.subio = subio;
    }

    public String getHashArchivo() {
        return hashArchivo;
    }

    public void setHashArchivo(String hashArchivo) {
        this.hashArchivo = hashArchivo;
    }

    public Date getFechaRegistro() {
        return fechaRegistro;
    }

    public void setFechaRegistro(Date fechaRegistro) {
        this.fechaRegistro = fechaRegistro;
    }

    public ArchivoFirma getIdArchivoFirma() {
        return idArchivoFirma;
    }

    public void setIdArchivoFirma(ArchivoFirma idArchivoFirma) {
        this.idArchivoFirma = idArchivoFirma;
    }

    public Usuario getIdUsuario() {
        return idUsuario;
    }

    public void setIdUsuario(Usuario idUsuario) {
        this.idUsuario = idUsuario;
    }

    public String getAgenteNavegador() {
        return agenteNavegador;
    }

    public void setAgenteNavegador(String agenteNavegador) {
        this.agenteNavegador = agenteNavegador;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (idFirmaArchivoUsuario != null ? idFirmaArchivoUsuario.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof FirmaArchivoUsuario)) {
            return false;
        }
        FirmaArchivoUsuario other = (FirmaArchivoUsuario) object;
        if ((this.idFirmaArchivoUsuario == null && other.idFirmaArchivoUsuario != null) || (this.idFirmaArchivoUsuario != null && !this.idFirmaArchivoUsuario.equals(other.idFirmaArchivoUsuario))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "se.firme.commons.firmese.models.entity.FirmaArchivoUsuario[ idFirmaArchivoUsuario=" + idFirmaArchivoUsuario + " ]";
    }

    public String getRutaRelativaArchivo() {
        return rutaRelativaArchivo;
    }

    public void setRutaRelativaArchivo(String rutaRelativaArchivo) {
        this.rutaRelativaArchivo = rutaRelativaArchivo;
    }

    public String getNombreArchivoFirma() {
        return nombreArchivoFirma;
    }

    public void setNombreArchivoFirma(String nombreArchivoFirma) {
        this.nombreArchivoFirma = nombreArchivoFirma;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    
}
