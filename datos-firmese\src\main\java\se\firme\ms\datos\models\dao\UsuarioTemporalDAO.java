package se.firme.ms.datos.models.dao;

import java.util.Optional;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import se.firme.ms.datos.models.entity.UsuarioTemporal;

@Repository
public interface UsuarioTemporalDAO extends CrudRepository<UsuarioTemporal, Long> {

	@Query(value = "SELECT * FROM usuario_temporal WHERE id_tipo_documento = ?1 and  numero_documento = ?2", nativeQuery = true)
	public Optional<UsuarioTemporal> findByNumeroDocumento(String tipoDocumento, String numeroDocumento);

}
