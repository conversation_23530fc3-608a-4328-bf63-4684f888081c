/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.datos.models.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.xml.bind.annotation.XmlRootElement;

import org.hibernate.annotations.CreationTimestamp;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "bitacora_registro")
@XmlRootElement
@NamedQueries({@NamedQuery(name = "BitacoraRegistro.findAll", query = "SELECT b FROM BitacoraRegistro b"), @NamedQuery(name = "BitacoraRegistro.findByIdBitacoraRegistro", query = "SELECT b FROM BitacoraRegistro b WHERE b.idBitacoraRegistro = :idBitacoraRegistro"), @NamedQuery(name = "BitacoraRegistro.findByCodigoSms", query = "SELECT b FROM BitacoraRegistro b WHERE b.codigoSms = :codigoSms"), @NamedQuery(name = "BitacoraRegistro.findByCorreoElectronico", query = "SELECT b FROM BitacoraRegistro b WHERE b.correoElectronico = :correoElectronico"), @NamedQuery(name = "BitacoraRegistro.findByNumeroCelular", query = "SELECT b FROM BitacoraRegistro b WHERE b.numeroCelular = :numeroCelular"), @NamedQuery(name = "BitacoraRegistro.findByFechaRegistro", query = "SELECT b FROM BitacoraRegistro b WHERE b.fechaRegistro = :fechaRegistro")})
@JsonIgnoreProperties(ignoreUnknown = true)
public class BitacoraRegistro implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id_bitacora_registro")
    private Long idBitacoraRegistro;
    @Basic(optional = false)
    @Column(name = "codigo_sms")
    private String codigoSms;
    @Basic(optional = false)
    @Column(name = "correo_electronico")
    private String correoElectronico;
    @Basic(optional = false)
    @Column(name = "numero_celular")
    private String numeroCelular;
    @Basic(optional = false)
    @Column(name = "fecha_registro")
    @Temporal(TemporalType.TIMESTAMP)
    @CreationTimestamp
    private Date fechaRegistro;
    @JoinColumn(name = "id_usuario", referencedColumnName = "id_usuario")
    @ManyToOne(optional = false)
    @JsonManagedReference
    private Usuario idUsuario;

    public BitacoraRegistro() {
    }

    public BitacoraRegistro(Long idBitacoraRegistro) {
        this.idBitacoraRegistro = idBitacoraRegistro;
    }

    public BitacoraRegistro(Long idBitacoraRegistro, String codigoSms, String correoElectronico, String numeroCelular, Date fechaRegistro) {
        this.idBitacoraRegistro = idBitacoraRegistro;
        this.codigoSms = codigoSms;
        this.correoElectronico = correoElectronico;
        this.numeroCelular = numeroCelular;
        this.fechaRegistro = fechaRegistro;
    }

    public Long getIdBitacoraRegistro() {
        return idBitacoraRegistro;
    }

    public void setIdBitacoraRegistro(Long idBitacoraRegistro) {
        this.idBitacoraRegistro = idBitacoraRegistro;
    }

    public String getCodigoSms() {
        return codigoSms;
    }

    public void setCodigoSms(String codigoSms) {
        this.codigoSms = codigoSms;
    }

    public String getCorreoElectronico() {
        return correoElectronico;
    }

    public void setCorreoElectronico(String correoElectronico) {

        if (correoElectronico != null) {
            correoElectronico = correoElectronico.toLowerCase();
        }

        this.correoElectronico = correoElectronico;
    }

    public String getNumeroCelular() {
        return numeroCelular;
    }

    public void setNumeroCelular(String numeroCelular) {
        this.numeroCelular = numeroCelular;
    }

    public Date getFechaRegistro() {
        return fechaRegistro;
    }

    public void setFechaRegistro(Date fechaRegistro) {
        this.fechaRegistro = fechaRegistro;
    }

    public Usuario getIdUsuario() {
        return idUsuario;
    }

    public void setIdUsuario(Usuario idUsuario) {
        this.idUsuario = idUsuario;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (idBitacoraRegistro != null ? idBitacoraRegistro.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof BitacoraRegistro)) {
            return false;
        }
        BitacoraRegistro other = (BitacoraRegistro) object;
        if ((this.idBitacoraRegistro == null && other.idBitacoraRegistro != null) || (this.idBitacoraRegistro != null && !this.idBitacoraRegistro.equals(other.idBitacoraRegistro))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "se.firme.commons.firmese.models.entity.BitacoraRegistro[ idBitacoraRegistro=" + idBitacoraRegistro + " ]";
    }

}
