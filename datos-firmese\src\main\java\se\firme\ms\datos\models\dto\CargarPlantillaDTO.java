package se.firme.ms.datos.models.dto;

public class CargarPlantillaDTO {
    
    private String nombrePlantilla;
    private String descripcion;
    private String nombreArchivo;
    private String archivo64; // Base64 del PDF
    private String tipoDocumento;
    private String tipoFirma = "MULTIPLE";
    private String rutaRelativa; // Opcional - se generará automáticamente si no se proporciona
    private Long idUsuarioCreador;
    
    // Constructores
    public CargarPlantillaDTO() {}
    
    // Getters y Setters
    public String getNombrePlantilla() { return nombrePlantilla; }
    public void setNombrePlantilla(String nombrePlantilla) { this.nombrePlantilla = nombrePlantilla; }
    
    public String getDescripcion() { return descripcion; }
    public void setDescripcion(String descripcion) { this.descripcion = descripcion; }
    
    public String getNombreArchivo() { return nombreArchivo; }
    public void setNombreArchivo(String nombreArchivo) { this.nombreArchivo = nombreArchivo; }
    
    public String getArchivo64() { return archivo64; }
    public void setArchivo64(String archivo64) { this.archivo64 = archivo64; }
    
    public String getTipoDocumento() { return tipoDocumento; }
    public void setTipoDocumento(String tipoDocumento) { this.tipoDocumento = tipoDocumento; }
    
    public String getTipoFirma() { return tipoFirma; }
    public void setTipoFirma(String tipoFirma) { this.tipoFirma = tipoFirma; }
    
    public String getRutaRelativa() { return rutaRelativa; }
    public void setRutaRelativa(String rutaRelativa) { this.rutaRelativa = rutaRelativa; }
    
    public Long getIdUsuarioCreador() { return idUsuarioCreador; }
    public void setIdUsuarioCreador(Long idUsuarioCreador) { this.idUsuarioCreador = idUsuarioCreador; }
    
    @Override
    public String toString() {
        return "CargarPlantillaDTO{" +
                "nombrePlantilla='" + nombrePlantilla + '\'' +
                ", descripcion='" + descripcion + '\'' +
                ", nombreArchivo='" + nombreArchivo + '\'' +
                ", tipoDocumento='" + tipoDocumento + '\'' +
                ", tipoFirma='" + tipoFirma + '\'' +
                ", idUsuarioCreador=" + idUsuarioCreador +
                '}';
    }
}
