package se.firme.ms.datos.models.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class DocumentoOrdenDTO {
    
    @JsonProperty("nombreArchivo")
    private String nombreArchivo;
    
    @JsonProperty("archivo64")
    private String archivo64;
    
    @JsonProperty("descripcion")
    private String descripcion;
    
    @JsonProperty("cantidadFirmas")
    private int cantidadFirmas;
    
    @JsonProperty("idUsuario")
    private long idUsuario;
    
    // Getters y Setters
    public String getNombreArchivo() {
        return nombreArchivo;
    }
    
    public void setNombreArchivo(String nombreArchivo) {
        this.nombreArchivo = nombreArchivo;
    }
    
    public String getArchivo64() {
        return archivo64;
    }
    
    public void setArchivo64(String archivo64) {
        this.archivo64 = archivo64;
    }
    
    public String getDescripcion() {
        return descripcion;
    }
    
    public void setDescripcion(String descripcion) {
        this.descripcion = descripcion;
    }
    
    public int getCantidadFirmas() {
        return cantidadFirmas;
    }
    
    public void setCantidadFirmas(int cantidadFirmas) {
        this.cantidadFirmas = cantidadFirmas;
    }
    
    public long getIdUsuario() {
        return idUsuario;
    }
    
    public void setIdUsuario(long idUsuario) {
        this.idUsuario = idUsuario;
    }
}