package se.firme.ms.datos.models.dto;

import java.util.List;

public class SolicitudFirmaPlantillaDTO {
    
    private Long idPlantilla;
    private String tipoOrden; // SECUENCIAL, PARALELO
    private String fechaVigencia;
    private Long idUsuario; // Usuario que solicita la firma
    private String descripcionPersonalizada;
    private List<FirmanteOrdenDTO> firmantes;
    private String tipoFirma = "MULTIPLE";
    
    // Constructores, getters y setters
    public SolicitudFirmaPlantillaDTO() {}
    
    public Long getIdPlantilla() { return idPlantilla; }
    public void setIdPlantilla(Long idPlantilla) { this.idPlantilla = idPlantilla; }
    
    public String getTipoOrden() { return tipoOrden; }
    public void setTipoOrden(String tipoOrden) { this.tipoOrden = tipoOrden; }
    
    public String getFechaVigencia() { return fechaVigencia; }
    public void setFechaVigencia(String fechaVigencia) { this.fechaVigencia = fechaVigencia; }
    
    public Long getIdUsuario() { return idUsuario; }
    public void setIdUsuario(Long idUsuario) { this.idUsuario = idUsuario; }
    
    public String getDescripcionPersonalizada() { return descripcionPersonalizada; }
    public void setDescripcionPersonalizada(String descripcionPersonalizada) { this.descripcionPersonalizada = descripcionPersonalizada; }
    
    public List<FirmanteOrdenDTO> getFirmantes() { return firmantes; }
    public void setFirmantes(List<FirmanteOrdenDTO> firmantes) { this.firmantes = firmantes; }
    
    public String getTipoFirma() { 
        return tipoFirma; 
    }
    
    public void setTipoFirma(String tipoFirma) { 
        this.tipoFirma = tipoFirma; 
    }
}
