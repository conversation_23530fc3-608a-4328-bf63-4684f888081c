/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.models.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
@Component
public class DatosConexionJDBC {
    public static String driver;
    public static String url;
    public static String user;
    public static String pass;
    
    @Autowired
    public DatosConexionJDBC getRDSConnection(@Value("${spring.datasource.driver-class-name}") String driver,
                                          @Value("${spring.datasource.url}") String url,
                                          @Value("${spring.datasource.username}") String user,
                                          @Value("${spring.datasource.password}") String pass) {
        DatosConexionJDBC.driver = driver;
        DatosConexionJDBC.url = url;
        DatosConexionJDBC.user = user;
        DatosConexionJDBC.pass = pass;

        return this;
    }
}


