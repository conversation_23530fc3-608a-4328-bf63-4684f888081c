/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package se.firme.ms.firma.rest;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import se.firme.commons.exception.FirmaException;
import se.firme.commons.firmese.dto.ApiResponse;
import se.firme.commons.firmese.dto.ArchivoFirmaResponseDTO;
import se.firme.commons.firmese.dto.DocumentoDTO;
import se.firme.commons.firmese.dto.FirmaRequestDTO;
import se.firme.commons.firmese.dto.MultipleFirmaDTO;
import se.firme.commons.firmese.dto.SendFileEmailRequestDTO;
import se.firme.commons.firmese.util.Parameters;
import se.firme.commons.firmese.util.Utilities;
import se.firme.commons.models.projection.IArchivoFirma;
import se.firme.commons.models.projection.IDocumentosEstado;
import se.firme.ms.datos.models.dto.FirmaOrdenRequestDTO;
import se.firme.ms.datos.models.entity.SolicitudFirma;
import se.firme.ms.datos.models.entity.Token;
import se.firme.ms.datos.models.entity.Usuario;
import se.firme.ms.firma.Utils.UsuarioHeaderUtil;
import se.firme.ms.firma.Utils.FirmaPaginacionUtil;
import se.firme.ms.firma.negocio.FirmaNegocio;

/**
 * <AUTHOR>
 * @document CatalogoController
 * @fecha martes, agosto 19 de 2020, 07:30:00 PM
 */
@RestController
@RefreshScope
@RequestMapping("/manager")
public class FirmaController extends IApiRest {

    @Autowired
    private FirmaNegocio firmaNegocio;
    @Autowired
    private HttpServletRequest request;


    @Override
    public HttpServletRequest getServletRequest() {
        return request;
    }

    @PostMapping("/subir/b64")
    public ResponseEntity<ApiResponse> firmarDocumento(@RequestBody FirmaRequestDTO datosFirma,
                                                       HttpServletRequest request) {
        try {
            String ipAddress = request.getHeader("X-FORWARDED-FOR");
            if (ipAddress == null) {
                ipAddress = request.getRemoteAddr();
            }
            datosFirma.setIp(ipAddress);
            datosFirma.setIdUsuario(Integer.parseInt(request.getHeader("X-USER-ID")));
            Token token = firmaNegocio.subirArchivo(datosFirma, null, false);

            if (token != null && !Utilities.isVacio(token.getIdToken())) {
                return new ResponseEntity<>(new ApiResponse.ok().data(token.getCodigoTransaccion()).build(),
                        HttpStatus.OK);
            } else {
                return new ResponseEntity<>(
                        new ApiResponse.error().mensaje("Inconvenientes al generar el código de transacción").build(),
                        HttpStatus.BAD_REQUEST);
            }
        } catch (Exception e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping("/subir-multiple/b64")
    public ResponseEntity<ApiResponse> subirDocumentoMultiple(@RequestBody List<FirmaRequestDTO> datosFirma,
                                                              HttpServletRequest request) {
        try {
            String ipAddress = request.getHeader("X-FORWARDED-FOR");
            if (ipAddress == null) {
                ipAddress = request.getRemoteAddr();
            }

            List<ArchivoFirmaResponseDTO> archivosSubidos = firmaNegocio.subirArchivoMultiple(datosFirma, true,
                    ipAddress, Integer.parseInt(request.getHeader("X-USER-ID")));
            return new ResponseEntity<>(new ApiResponse.ok().data(archivosSubidos).build(), HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping("/fimar-multiple")
    public ResponseEntity<ApiResponse> firmarDocumentoMultiple(@RequestBody List<FirmaRequestDTO> datosFirma,
                                                               HttpServletRequest request) {
        try {
            String ipAddress = request.getHeader("X-FORWARDED-FOR");
            if (ipAddress == null) {
                ipAddress = request.getRemoteAddr();
            }

            firmaNegocio.getProcesoFirma(datosFirma, Integer.parseInt(request.getHeader("X-USER-ID")));

            return new ResponseEntity<>(new ApiResponse.ok().build(), HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping(path = "/validarsms-multiple", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> validarSmsMultiple(@RequestParam("csms") String ctoken,
                                                          @RequestParam("usms") long usms) {
        try {

            return new ResponseEntity<>(
                    new ApiResponse.ok().data(firmaNegocio.firmarDocumentos(ctoken, usms, request)).build(),
                    HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping("/enviar-notificacion")
    public ResponseEntity<ApiResponse> enviarNotificacionMultiple(@RequestBody SendFileEmailRequestDTO datosFirma,
                                                                  HttpServletRequest request) {
        try {
            String ipAddress = request.getHeader("X-FORWARDED-FOR");
            if (ipAddress == null) {
                ipAddress = request.getRemoteAddr();
            }

            firmaNegocio.enviarNotificacionMultiple(datosFirma);
            return new ResponseEntity<>(new ApiResponse.ok().build(), HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping("/subir/bin")
    public ResponseEntity<ApiResponse> firmarArchivo(@RequestParam("archivo") MultipartFile file,
                                                     @RequestParam("idUsuario") int idUsuario, @RequestParam("cantidadFirmas") int cantidadFirmas,
                                                     HttpServletRequest request) {
        try {
            String ipAddress = request.getHeader("X-FORWARDED-FOR");
            if (ipAddress == null) {
                ipAddress = request.getRemoteAddr();
            }
            FirmaRequestDTO datosFirma = new FirmaRequestDTO();
            byte[] bytes = file.getBytes();
            String nombreArchivo = file.getOriginalFilename();
            datosFirma.setIp(ipAddress);
            datosFirma.setIdUsuario(idUsuario);
            datosFirma.setCantidadFirmas(cantidadFirmas);
            datosFirma.setNombreArchivo(nombreArchivo);

            Token token = firmaNegocio.subirArchivo(datosFirma, bytes, false);

            if (token != null && !Utilities.isVacio(token.getIdToken())) {
                return new ResponseEntity<>(new ApiResponse.ok().data(token.getCodigoTransaccion()).build(),
                        HttpStatus.OK);
            } else {
                return new ResponseEntity<>(
                        new ApiResponse.error().mensaje("Inconvenientes al generar el código de transacción").build(),
                        HttpStatus.BAD_REQUEST);
            }
        } catch (IOException e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        } catch (Exception ex) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(ex.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }

    }

    @PostMapping("/firma-pendiente")
    public ResponseEntity<ApiResponse> firmaPendiente(@RequestParam("idUsuario") int idUsuario) {
        try {
            return new ResponseEntity<>(new ApiResponse.ok().data(firmaNegocio.firmasPendientes(Integer.parseInt(request.getHeader("X-USER-ID")))).build(),
                    HttpStatus.OK);
        } catch (Exception ex) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(ex.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping("/docs-pendiente")
    public ResponseEntity<Page<IDocumentosEstado>> getDocumentosPendientesPag(@RequestParam("idUsuario") int idUsuario,
                                                                              @RequestParam(defaultValue = "1") Integer page, @RequestParam(defaultValue = "5") Integer per_page,
                                                                              @RequestParam(defaultValue = "id_archivo_firma") String sortBy,
                                                                              @RequestParam(defaultValue = "asc") String sortDirection) {
        try {


            System.out.println("tiempoInicio: " + request.getHeader("X-USER-M") + " :: " + request.getHeader("X-USER-ID"));
            return new ResponseEntity<Page<IDocumentosEstado>>(firmaNegocio.getDocumentsPending(Integer.parseInt(request.getHeader("X-USER-ID")), page,
                    per_page, sortBy, sortDirection, Parameters.string.ESTADO_ARCHIVO_PENDIENTE_FIRMA), new HttpHeaders(),
                    HttpStatus.OK);
        } catch (FirmaException ex) {
            return new ResponseEntity<Page<IDocumentosEstado>>(null, new HttpHeaders(), HttpStatus.BAD_GATEWAY);
        }

    }

    @PostMapping("/documento-firmado")
    public ResponseEntity<ApiResponse> documentoFirmado(@RequestParam("idUsuario") int idUsuario) {
        try {
            return new ResponseEntity<>(new ApiResponse.ok().data(firmaNegocio.documentosFirmados(Integer.parseInt(request.getHeader("X-USER-ID")))).build(),
                    HttpStatus.OK);
        } catch (Exception ex) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(ex.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping("/docs-firmado")
    public ResponseEntity<Page<ArchivoFirmaResponseDTO>> getDocumentosFirmadosPag(
            @RequestParam("idUsuario") int idUsuario, @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "5") Integer per_page,
            @RequestParam(defaultValue = "id_archivo_firma") String sortBy) {
        try {
            return new ResponseEntity<Page<ArchivoFirmaResponseDTO>>(
                    firmaNegocio.documentosFirmadosPag(Integer.parseInt(request.getHeader("X-USER-ID")), page, per_page, sortBy), new HttpHeaders(),
                    HttpStatus.OK);
        } catch (FirmaException ex) {
            return new ResponseEntity<Page<ArchivoFirmaResponseDTO>>(null, new HttpHeaders(), HttpStatus.BAD_REQUEST);
        }
    }
    
    @PutMapping(consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> completar(@RequestBody DocumentoDTO documento) {
        try {
            firmaNegocio.completarFirma(documento);
            return new ResponseEntity<>(new ApiResponse.ok().data("Firma exitosa").build(), HttpStatus.OK);
        } catch (FirmaException e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping("/archivo-codigo-transaccion")
    public ResponseEntity<ApiResponse> findByIdUsuarioCodigoTransaccion(@RequestParam("idUsuario") int idUsuario,
                                                                        @RequestParam("codigoTransaccion") String codigoTransaccion) {
        try {
            return new ResponseEntity<>(
                    new ApiResponse.ok()
                            .data(firmaNegocio.findByIdUsuarioCodigoTransaccion(Integer.parseInt(request.getHeader("X-USER-ID")), codigoTransaccion)).build(),
                    HttpStatus.OK);
        } catch (Exception ex) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(ex.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping("/consutlar-by-hash")
    public ResponseEntity<ApiResponse> consultarArchivoByHash(@RequestParam("hash") String hash) {
        try {
            return new ResponseEntity<>(new ApiResponse.ok().data(firmaNegocio.getArchivoFirmaByHash(hash)).build(),
                    HttpStatus.OK);
        } catch (Exception ex) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(ex.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping(path = "/delete", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> eliminarDocumentoPendiente(@RequestParam("idDocumento") long idDocument) {
        try {
            firmaNegocio.eliminarDocumentoPendiente(idDocument, Integer.parseInt(request.getHeader("X-USER-ID")));
            return new ResponseEntity<>(new ApiResponse.ok().data("Documento eliminado").build(), HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping(path = "/fimar-documentos-multiple-firmante", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> firmarDocumentoMultipleFirma(@RequestBody MultipleFirmaDTO datosFirma,
                                                                    HttpServletRequest request) {
        try {
            String ipAddress = request.getHeader("X-FORWARDED-FOR");
            if (ipAddress == null) {
                ipAddress = request.getRemoteAddr();
            }
            firmaNegocio.iniciarProcesoFirma(datosFirma, Parameters.string.TIPO_FIRMA_MULTIPLE, Integer.parseInt(request.getHeader("X-USER-ID")));
            return new ResponseEntity<>(new ApiResponse.ok().build(), HttpStatus.OK);
        } catch (FirmaException e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping(path = "/ver-firmante", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> verFirmantes(@RequestParam("idArchivo") int idArchivo) {
        try {
            return new ResponseEntity<>(
                    new ApiResponse.ok().data(firmaNegocio.verFirmantes(Integer.parseInt(request.getHeader("X-USER-ID")), idArchivo)).build(), HttpStatus.OK);
        } catch (FirmaException e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping("/documentos-firmados-propietario")
    public ResponseEntity<Page<IArchivoFirma>> mostrarDocumentosPropiedad(@RequestParam("idUsuario") int idUsuario,
                                                                          @RequestParam(defaultValue = "1") Integer page, @RequestParam(defaultValue = "5") Integer per_page,
                                                                          @RequestParam(defaultValue = "id_archivo_firma") String sortBy) {
        try {
            return new ResponseEntity<Page<IArchivoFirma>>(
                    firmaNegocio.findArchivosFirmados(Integer.parseInt(request.getHeader("X-USER-ID")), page, per_page, sortBy), new HttpHeaders(),
                    HttpStatus.OK);
        } catch (FirmaException ex) {
            return new ResponseEntity<Page<IArchivoFirma>>(null, new HttpHeaders(), HttpStatus.BAD_GATEWAY);
        }

    }

    @PostMapping("/docs-propietario")
    public ResponseEntity<Page<IDocumentosEstado>> getDocumentosPropietarioPag(@RequestParam("idUsuario") int idUsuario,
                                                                               @RequestParam(defaultValue = "1") Integer page, @RequestParam(defaultValue = "5") Integer per_page,
                                                                               @RequestParam(defaultValue = "id_archivo_firma") String sortBy,
                                                                               @RequestParam(defaultValue = "asc") String sortDirection) {
        try {
            return new ResponseEntity<Page<IDocumentosEstado>>(firmaNegocio.getDocumentsPending(Integer.parseInt(request.getHeader("X-USER-ID")), page,
                    per_page, sortBy, sortDirection, Parameters.string.ESTADO_ARCHIVO_FIRMADO), new HttpHeaders(), HttpStatus.OK);
        } catch (FirmaException ex) {
            return new ResponseEntity<Page<IDocumentosEstado>>(null, new HttpHeaders(), HttpStatus.BAD_GATEWAY);
        }

    }

    @PostMapping("/docs-firmados-usuario")
    public ResponseEntity<Page<IDocumentosEstado>> getDocumentosFirmadosUsuarioPag(
            @RequestParam("idUsuario") int idUsuario, @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "5") Integer per_page,
            @RequestParam(defaultValue = "id_archivo_firma") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDirection) {
        try {
            return new ResponseEntity<Page<IDocumentosEstado>>(firmaNegocio.getDocumentosFirmadosUsuario(Integer.parseInt(request.getHeader("X-USER-ID")),
                    page, per_page, sortBy, sortDirection, Parameters.string.ESTADO_ARCHIVO_FIRMADO), new HttpHeaders(),
                    HttpStatus.OK);
        } catch (FirmaException ex) {
            return new ResponseEntity<Page<IDocumentosEstado>>(null, new HttpHeaders(), HttpStatus.BAD_GATEWAY);
        }
    }
    
    // endpoint para listado y busqueda en documentos firmados
    @PostMapping("/docs-firmados-busqueda")
    public ResponseEntity<Page<IDocumentosEstado>> getDocumentosFirmadosConBusqueda(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "12") Integer per_page,
            @RequestParam(defaultValue = "id_archivo_firma") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDirection,
            @RequestParam(required = false) String nombre) {
        Long idUsuario;
        try{
            idUsuario = UsuarioHeaderUtil.obtenerIdUsuario(request);
        } catch(IllegalArgumentException e) {
        	return FirmaPaginacionUtil.respuestaErrorPage(e.getMessage(), HttpStatus.UNAUTHORIZED);
        }

        try {
            // validaciones de paginación
            FirmaPaginacionUtil.validarPaginacion(page, per_page, nombre, sortBy, sortDirection);
        }
        catch(IllegalArgumentException e){
            return FirmaPaginacionUtil.respuestaErrorPage(e.getMessage(), HttpStatus.BAD_REQUEST);
        }    
        try{
            Page<IDocumentosEstado> resultado = firmaNegocio.getDocumentosFirmadosConBusqueda(
                    idUsuario.intValue(),
                    nombre,
                    page,
                    per_page,
                    sortBy,
                    sortDirection,
                    Parameters.string.ESTADO_ARCHIVO_FIRMADO
        );
        return new ResponseEntity<Page<IDocumentosEstado>>(resultado, new HttpHeaders(), HttpStatus.OK);
        } catch (FirmaException ex) {
            return FirmaPaginacionUtil.respuestaErrorPage(ex.getMessage(), HttpStatus.BAD_REQUEST);
        }
    }
    
    // endpoint para listado y busqueda de documentos propietario y firmados unificados
    @PostMapping("/docs-unificados-busqueda")
    public ResponseEntity<Page<IDocumentosEstado>> getDocumentosUnificadosConBusqueda(
            @RequestParam(defaultValue = "1") Integer page, @RequestParam(defaultValue = "5") Integer per_page,
            @RequestParam(defaultValue = "id_archivo_firma") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDirection,
            @RequestParam(required = false) String nombre) {
        
        Long idUsuario;
        try{
            idUsuario = UsuarioHeaderUtil.obtenerIdUsuario(request);
        } catch(IllegalArgumentException e) {
        	return FirmaPaginacionUtil.respuestaErrorPage(e.getMessage(), HttpStatus.UNAUTHORIZED);
        }

        try{
            // validaciones de paginación
            FirmaPaginacionUtil.validarPaginacion(page, per_page, nombre, sortBy, sortDirection);
        } catch(IllegalArgumentException e) {
        	return FirmaPaginacionUtil.respuestaErrorPage(e.getMessage(), HttpStatus.BAD_REQUEST);
        }
        try{
            Page<IDocumentosEstado> resultado = firmaNegocio.getDocumentosUnificadosConBusqueda(idUsuario.intValue(),
                nombre,
                page,
                per_page,
                sortBy,
                sortDirection,
                Parameters.string.ESTADO_ARCHIVO_FIRMADO);

            return new ResponseEntity<Page<IDocumentosEstado>>(resultado, new HttpHeaders(), HttpStatus.OK);
        } catch (FirmaException ex) {
            return FirmaPaginacionUtil.respuestaErrorPage(ex.getMessage(), HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping(path = "/reenviar-solicitud-firmante", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> reenviarSolicitudFirma(@RequestBody String input) {
        try {
            return new ResponseEntity<>(
                    new ApiResponse.ok().data(firmaNegocio.reenviarSolicitudFirma(Integer.parseInt(request.getHeader("X-USER-ID")), input)).build(), HttpStatus.OK);
        } catch (FirmaException e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping(path = "/solicitudes-usuario", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> verSolicitudesFirma() {
        try {
            return new ResponseEntity<>(
                    new ApiResponse.ok().data(firmaNegocio.getSolicitudesFirma(Integer.parseInt(request.getHeader("X-USER-ID")))).build(), HttpStatus.OK);
        } catch (FirmaException e) {
            return new ResponseEntity<>(new ApiResponse.error().mensaje(e.getMessage()).build(),
                    HttpStatus.BAD_REQUEST);
        }
    }

    @PostMapping(path = "/v2/solicitar-firma-orden", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> solicitarFirmaOrden(@RequestBody FirmaOrdenRequestDTO request,
                                                          HttpServletRequest httpRequest) {
        try {
            String ipAddress = httpRequest.getHeader("X-FORWARDED-FOR");
            if (ipAddress == null) {
                ipAddress = httpRequest.getRemoteAddr();
            }

            // Usar el servicio de firma en orden
            Map<String, Object> resultado = firmaNegocio.procesarSolicitudFirmaOrden(request, ipAddress);

            return new ResponseEntity<>(
                new ApiResponse.ok()
                    .data(resultado)
                    .build(),
                HttpStatus.OK
            );

        } catch (FirmaException e) {
            return new ResponseEntity<>(
                new ApiResponse.error()
                    .mensaje(e.getMessage())
                    .build(),
                HttpStatus.BAD_REQUEST
            );
        } catch (Exception e) {
            return new ResponseEntity<>(
                new ApiResponse.error()
                    .mensaje("Error interno del servidor: " + e.getMessage())
                    .build(),
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @PostMapping(path = "/v2/consultar-estado-orden", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> consultarEstadoOrden(@RequestParam("idArchivo") Long idArchivo) {
        try {
            Map<String, Object> response = firmaNegocio.consultarEstadoOrdenFirma(idArchivo);

            return new ResponseEntity<>(
                new ApiResponse.ok()
                    .data(response)
                    .build(),
                HttpStatus.OK
            );

        } catch (FirmaException e) {
            return new ResponseEntity<>(
                new ApiResponse.error()
                    .mensaje(e.getMessage())
                    .build(),
                HttpStatus.BAD_REQUEST
            );
        } catch (Exception e) {
            return new ResponseEntity<>(
                new ApiResponse.error()
                    .mensaje("Error consultando estado del archivo: " + e.getMessage())
                    .build(),
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    @PostMapping(path = "/v2/validar-orden-firma", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> validarOrdenFirma(@RequestParam("idArchivo") Long idArchivo,
                                                        @RequestParam("emailFirmante") String emailFirmante) {
        try {
            boolean puedeFirema = firmaNegocio.validarOrdenFirma(idArchivo, emailFirmante);

            Map<String, Object> response = new HashMap<>();
            response.put("idArchivo", idArchivo);
            response.put("emailFirmante", emailFirmante);
            response.put("puedeFirmar", puedeFirema);

            return new ResponseEntity<>(
                new ApiResponse.ok()
                    .data(response)
                    .build(),
                HttpStatus.OK
            );

        } catch (FirmaException e) {
            return new ResponseEntity<>(
                new ApiResponse.error()
                    .mensaje(e.getMessage())
                    .build(),
                HttpStatus.BAD_REQUEST
            );
        } catch (Exception e) {
            return new ResponseEntity<>(
                new ApiResponse.error()
                    .mensaje("Error validando orden de firma: " + e.getMessage())
                    .build(),
                HttpStatus.BAD_REQUEST
            );
        }
    }

    @PostMapping(path = "/v2/consultar-firmantes-pendientes", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> consultarFirmantesPendientes(@RequestParam("idArchivo") Long idArchivo) {
        try {
            List<SolicitudFirma> solicitudes = firmaNegocio.consultarSolicitudesPorArchivo(idArchivo);

            Map<String, Object> response = new HashMap<>();
            response.put("idArchivo", idArchivo);
            response.put("solicitudes", solicitudes);
            response.put("totalSolicitudes", solicitudes.size());

            return new ResponseEntity<>(
                new ApiResponse.ok()
                    .data(response)
                    .build(),
                HttpStatus.OK
            );

        } catch (FirmaException e) {
            return new ResponseEntity<>(
                new ApiResponse.error()
                    .mensaje(e.getMessage())
                    .build(),
                HttpStatus.BAD_REQUEST
            );
        } catch (Exception e) {
            return new ResponseEntity<>(
                new ApiResponse.error()
                    .mensaje("Error consultando firmantes pendientes: " + e.getMessage())
                    .build(),
                HttpStatus.BAD_REQUEST
            );
        }
    }

}
