package se.firme.ms.firma.rest;

import java.util.List;
import javax.servlet.http.HttpServletRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;
import se.firme.commons.firmese.dto.ApiResponse;
import se.firme.commons.firmese.dto.ArchivoFirmaResponseDTO;
import se.firme.commons.firmese.dto.DocumentoDTO;
import se.firme.commons.firmese.dto.FirmaRequestDTO;
import se.firme.commons.firmese.dto.MultipleFirmaDTO;
import se.firme.commons.firmese.dto.SendFileEmailRequestDTO;
import se.firme.commons.firmese.dto.UserLoginDTO;
import se.firme.commons.models.projection.IDocumentosEstado;

public abstract class IApiRest {
	
	public abstract HttpServletRequest getServletRequest();
	
	public abstract ResponseEntity<ApiResponse> firmarDocumento(FirmaRequestDTO datosFirma, HttpServletRequest request);

	public abstract ResponseEntity<ApiResponse> subirDocumentoMultiple(List<FirmaRequestDTO> datosFirma,
			HttpServletRequest request);

	public abstract ResponseEntity<ApiResponse> firmarDocumentoMultiple(List<FirmaRequestDTO> datosFirma,
			HttpServletRequest request);

	public abstract ResponseEntity<ApiResponse> validarSmsMultiple(String ctoken, long usms);

	public abstract ResponseEntity<ApiResponse> enviarNotificacionMultiple(SendFileEmailRequestDTO datosFirma,
			HttpServletRequest request);

	public abstract ResponseEntity<ApiResponse> firmarArchivo(MultipartFile file, int idUsuario, int cantidadFirmas,
			HttpServletRequest request);

	public abstract ResponseEntity<ApiResponse> firmaPendiente(int idUsuario);

	public abstract ResponseEntity<Page<IDocumentosEstado>> getDocumentosPendientesPag(int idUsuario, Integer page,
			Integer per_page, String sortBy, String sortDirection);

	public abstract ResponseEntity<ApiResponse> documentoFirmado(int idUsuario);

	public abstract ResponseEntity<Page<ArchivoFirmaResponseDTO>> getDocumentosFirmadosPag(int idUsuario, Integer page,
			Integer per_page, String sortBy);

	public abstract ResponseEntity<ApiResponse> completar(DocumentoDTO documento);

	public abstract ResponseEntity<ApiResponse> findByIdUsuarioCodigoTransaccion(int idUsuario, String codigoTransaccion);

	public abstract ResponseEntity<ApiResponse> consultarArchivoByHash(String hash);

	public abstract ResponseEntity<ApiResponse> eliminarDocumentoPendiente(long idDocument);

	public abstract ResponseEntity<ApiResponse> firmarDocumentoMultipleFirma(MultipleFirmaDTO datosFirma,
			HttpServletRequest request);

	public abstract ResponseEntity<ApiResponse> verFirmantes(int idArchivo);
	
	public UserLoginDTO getInfoRerquest() {
		UserLoginDTO dto=new UserLoginDTO();
		try {
			
			String ipAddress = getServletRequest().getHeader("X-FORWARDED-FOR");
            if (ipAddress == null) {
                ipAddress = getServletRequest().getRemoteAddr();
            }
			dto.setDireccionIP(ipAddress);
			dto.setUsuario(getServletRequest().getUserPrincipal()==null ? "error": getServletRequest().getUserPrincipal().getName());
			dto.setFirmaNavegador(getServletRequest().getHeader("User-Agent"));
		} catch (Exception e) {
			dto.setDireccionIP("0.0.0.0");
			dto.setUsuario("Invitado");
			dto.setFirmaNavegador("no definido");
		}
		System.out.println("Usuario autenticado: "+dto.getUsuario());
		return dto;
	}
}
