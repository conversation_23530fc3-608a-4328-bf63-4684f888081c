/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.datos.models.dao;

import java.util.List;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import se.firme.ms.datos.models.entity.Parametro;

/**
 *
 * <AUTHOR>
 */
public interface IParametroDao extends PagingAndSortingRepository<Parametro, Long>  {
    
    @Query(value="select * from tipo_documento where activo = 1 ", nativeQuery = true)
    public List<Parametro> findTipoDocumentoActivo();
    
    @Query(value="select * from parametro where id_usuario = ?1 ", nativeQuery = true)
    public List<Parametro> findParametersByUsuario(long idUsuario);
}
