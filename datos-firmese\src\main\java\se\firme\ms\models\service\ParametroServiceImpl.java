/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.models.service;

import se.firme.ms.models.service.interfaz.IParametroService;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import se.firme.ms.datos.models.dao.IParametroDao;
import se.firme.ms.datos.models.entity.Parametro;
import se.firme.ms.utils.UserParameter;

/**
 *
 * <AUTHOR>
 */
@Service
public class ParametroServiceImpl implements IParametroService {

    @Autowired
    private IParametroDao parametroService;
    private static final Logger logger = LoggerFactory.getLogger(ParametroServiceImpl.class);

    @Override
    public Map<String, String> getParametrosMap() {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    public boolean verificarParametro(UserParameter userParametes, long idUsuario) {
        logger.info("verificando parametro: {}",userParametes);
        List<Parametro> parametros = parametroService.findParametersByUsuario(idUsuario);
        if (parametros != null && !parametros.isEmpty()) {
            Optional<Parametro> optional = parametros.stream().filter(p -> p.getNombre().equals(userParametes.name())).findAny();
            if (optional.isPresent()) {
                Parametro parametro = optional.get();
                logger.info("Parametro {} encontrado con valor {} {}", parametro.getNombre(), parametro.getValorParametro(), parametro.getDescripcion());
                return parametro.getValorParametro().equals(userParametes.getExpected());
            }
            return false;
        }
        return false;
    }

}
