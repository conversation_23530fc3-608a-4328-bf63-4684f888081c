/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.commons.firmese.dto;

import java.io.Serializable;

/**
 * @document PersonaDTO
 * <AUTHOR>
 * @fecha lunes, agosto 24 de 2020, 05:53:12 PM
 */
public class PersonaDTO implements Serializable {

    public PersonaDTO() {
    }

    private String birthDay;
    private String birthDayFormat2;
    private String bloodType;
    private String cedulaType;
    private String codeAfis;
    private String decadentCard;
    private String department;
    private String documentId;
    private String gender;
    private String municipality;
    private DocumentType documentType;
    private Name name;

    public String getBirthDay() {
        return birthDay;
    }

    public void setBirthDay(String birthDay) {
        this.birthDay = birthDay;
    }

    public String getBirthDayFormat2() {
        return birthDayFormat2;
    }

    public void setBirthDayFormat2(String birthDayFormat2) {
        this.birthDayFormat2 = birthDayFormat2;
    }

    public String getBloodType() {
        return bloodType;
    }

    public void setBloodType(String bloodType) {
        this.bloodType = bloodType;
    }

    public String getCedulaType() {
        return cedulaType;
    }

    public void setCedulaType(String cedulaType) {
        this.cedulaType = cedulaType;
    }

    public String getCodeAfis() {
        return codeAfis;
    }

    public void setCodeAfis(String codeAfis) {
        this.codeAfis = codeAfis;
    }

    public String getDecadentCard() {
        return decadentCard;
    }

    public void setDecadentCard(String decadentCard) {
        this.decadentCard = decadentCard;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getDocumentId() {
        return documentId;
    }

    public void setDocumentId(String documentId) {
        this.documentId = documentId;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getMunicipality() {
        return municipality;
    }

    public void setMunicipality(String municipality) {
        this.municipality = municipality;
    }

    public DocumentType getDocumentType() {
        return documentType;
    }

    public void setDocumentType(DocumentType documentType) {
        this.documentType = documentType;
    }

    public Name getName() {
        return name;
    }

    public void setName(Name name) {
        this.name = name;
    }

    public static class DocumentType {

        public DocumentType() {
        }

        private long barcodeType;
        private String country;
        private String id;
        private String name;
        private String abreviature;

        public long getBarcodeType() {
            return barcodeType;
        }

        public void setBarcodeType(long barcodeType) {
            this.barcodeType = barcodeType;
        }

        public String getCountry() {
            return country;
        }

        public void setCountry(String country) {
            this.country = country;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getAbreviature() {
            return abreviature;
        }

        public void setAbreviature(String abreviature) {
            this.abreviature = abreviature;
        }

    }

    public static class Name {

        public Name() {
        }

        private String firstName;
        private String fullName;
        private long nameType;
        private String secondName;
        private String secondSurename;
        private String surename;

        public String getFirstName() {
            return firstName;
        }

        public void setFirstName(String firstName) {
            this.firstName = firstName;
        }

        public String getFullName() {
            return fullName;
        }

        public void setFullName(String fullName) {
            this.fullName = fullName;
        }

        public long getNameType() {
            return nameType;
        }

        public void setNameType(long nameType) {
            this.nameType = nameType;
        }

        public String getSecondName() {
            return secondName;
        }

        public void setSecondName(String secondName) {
            this.secondName = secondName;
        }

        public String getSecondSurename() {
            return secondSurename;
        }

        public void setSecondSurename(String secondSurename) {
            this.secondSurename = secondSurename;
        }

        public String getSurename() {
            return surename;
        }

        public void setSurename(String surename) {
            this.surename = surename;
        }

    }
}
