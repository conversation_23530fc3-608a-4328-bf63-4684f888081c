/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package se.firme.ms.datos.models.dao;

import java.util.List;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import se.firme.ms.datos.models.entity.ProcesoFirma;
import se.firme.ms.datos.models.entity.Usuario;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
public interface IProcesoFirmaDao extends PagingAndSortingRepository<ProcesoFirma, Long>{
    
    @Query(value = "select * from proceso_firma where otp = ?1", nativeQuery = true)
    public List<ProcesoFirma> findByCodSMSUnico(String codSMS);
    
    @Modifying
    @Query(value = "update archivo_firma set id_proceso_firma = ?1 where id_archivo_firma IN (?2) ", nativeQuery = true)
    public void actualizarIdProcesoArchivoFirma(long idProcesoFirma, List<String> idsArchivos);
    
    @Query(value = "select * from proceso_firma where otp = ?1 and id_usuario = ?2", nativeQuery = true)
    public List<ProcesoFirma> findByCodSMSIdUsuario(String codSMS, long idUsuario);
    
    @Modifying
    @Query(value = "update proceso_firma set activo = 0 where otp = ?1 and id_usuario = ?2", nativeQuery = true)
    public void desactivarProcesoFirmaBySMSIdUSuario(String codSMS, long idUsuario);
    
    @Query(value = "select * from proceso_firma pf where id_usuario = ?1 and pf.activo = true and pf.fecha_registro between DATE_SUB(now(), INTERVAL 40 MINUTE ) and now() order by pf.fecha_registro desc", nativeQuery = true)
	public List<ProcesoFirma> buscarUltimoEnvio(String id);
    
    public List<ProcesoFirma> findAllByIdUsuario(Usuario usuario);
    
    @Query(value = "SELECT * FROM proceso_firma WHERE id_usuario = ?1 AND activo = 1 " +
       "AND fecha_vencimiento > NOW() ORDER BY fecha_registro DESC LIMIT 1", 
       nativeQuery = true)
    public ProcesoFirma findValidOtpByUsuario(long idUsuario);
}
