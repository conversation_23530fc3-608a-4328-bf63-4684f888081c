/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.datos.models.dao;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import se.firme.commons.models.projection.IUsuario;
import se.firme.commons.models.projection.IUsuarioOris;
import se.firme.ms.datos.models.entity.Usuario;

/**
 *
 * <AUTHOR>
 */
public interface IUsuarioDao extends PagingAndSortingRepository<Usuario, Long> {

	@Query(value = "select * from (select * from usuario where  numero_documento = ?1 or correo_electronico = ?2 or numero_celular = ?3)  A where A.estado = true", nativeQuery = true)
	public List<Usuario> buscarUsuarioExistente(String numeroDocumento, String correoElectronico, String numeroCelular);

	@Query(value = "update usuario set proceso_registro = false where id_usuario = ?1", nativeQuery = true)
	@Modifying
	public void actualizarPreregistro(long idUsuario);

	@Modifying
	@Query(value = "update usuario set verificado_fuente = ?1, fecha_verificacion = ?2, observacion_verificacion = ?3, nombre_completo = ?4 where id_usuario = ?5", nativeQuery = true)
	public void actualizarVerificacionEnFuente(boolean verificadoFuente, Date fechaVerificacion,
			String obervacionVerificacion, String nombreCompleto, long idUsuairo);

	@Query(value = "select * from usuario where  correo_electronico = ?1 and estado= true", nativeQuery = true)
	public Optional<Usuario> findByEmail(String email);

	@Query(value = "select u.numero_documento as numeroDocumento, u.id_tipo_documento as tipoDocumento , u.nombre_completo as nombreCompleto, u.correo_electronico as correoElectronico, t.codigo_transaccion as cod  from usuario u left join token t on u.id_usuario =t.id_usuario where t.tipo =1 and u.id_usuario = ?1 order by t.fecha_registro desc", nativeQuery = true)
	public List<IUsuario> findByID(String id);

	/**
	 * Método retorna el usuario activo filtrado por cédula y correo
	 * 
	 * @param numeroDocumento
	 * @param email
	 * @return
	 */
	@Query(value = "select * from usuario where  numero_documento = ?1 and correo_electronico = ?2 and estado= true", nativeQuery = true)
	public Optional<Usuario> findByCedulaEmail(String numeroDocumento, String email);

	@Modifying
	@Query(value = "delete from gestion_adm.adm_usuario where idUsuarioEnCliente = ?1 ", nativeQuery = true)
	public void eliminarRegistroUsuarioGestion(Long idUsuario);

	@Query(value = "select u.numero_documento as numeroDocumento, u.id_tipo_documento as tipoDocumento , u.fecha_expedicion_documento as fechaExpedicionDocumento, u.numero_celular as numeroCelular, u.nombre_completo as nombreCompleto, u.correo_electronico as correoElectronico, count(pf.id_usuario) as firmas from usuario u left join proceso_firma pf on u.id_usuario = pf.id_usuario where (u.numero_documento = ?1 or u.correo_electronico = ?1) and u.estado = true group by u.id_usuario", nativeQuery = true)
	public Optional<IUsuarioOris> findByNumeroDocumentoOrCorreoElectronico(String docOrEmail);

	@Query(value = "select * from usuario where  numero_documento = ?1 and estado= true", nativeQuery = true)
	public Optional<Usuario> findByNumeroDocumento(String doc);

	@Query(value = "select * from usuario where numero_celular = ?1 and estado = true", nativeQuery = true)
	public Optional<Usuario> findByNumeroCelular(String numeroCelular);
}
