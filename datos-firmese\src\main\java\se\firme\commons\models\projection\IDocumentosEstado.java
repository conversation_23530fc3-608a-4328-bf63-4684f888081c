/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.commons.models.projection;

/**
 *
 * <AUTHOR>
 */
public interface IDocumentosEstado {
    String getNombreArchivo();
    String getCodigoTransaccion();
    String getEstado();
    String getFechaRegistro();
    String getIp();
    String getIdArchivo();
    String getHashArchivo();
    String getFirmasRequeridas();
    String getTotalFirmas();
    String getTipoFirma();
    String getFirmantes();

    String getDescripcion();
    String getEsSolicitud();
    String getPropietario();
    String getSolicitudFirmada();

    String getFechaVencimiento();
    String getNumeroDocumentoPropietario();
}
