package se.firme.ms.models.service.helper;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import se.firme.commons.firmese.dto.FirmaArchivoUsuarioResponseDTO;
import se.firme.commons.firmese.util.Utilities;
import se.firme.ms.datos.models.entity.FirmaArchivoUsuario;


public class FirmaArchivoUsuarioHelper {

	public static List<FirmaArchivoUsuarioResponseDTO> convert(List<FirmaArchivoUsuario> firmas) {
		List<FirmaArchivoUsuarioResponseDTO> dtoList=new ArrayList<>();
		if(firmas!=null && !firmas.isEmpty()) {
			for(FirmaArchivoUsuario firma: firmas) {
				dtoList.add(convert(firma));
			}
		}
		return dtoList;
	}

	public static FirmaArchivoUsuarioResponseDTO convert(FirmaArchivoUsuario firma) {
		FirmaArchivoUsuarioResponseDTO dto =new FirmaArchivoUsuarioResponseDTO();
		dto.setAgenteNavegador(firma.getAgenteNavegador());
		dto.setFechaFirma(firma.getFechaRegistro());
		 try {
			dto.setFechaFirmaStr(Utilities.getFechaDateAFechaTexto(firma.getFechaRegistro(), "EEEE, dd 'de' MMMM 'de' yyyy HH:mm"));
		} catch (ParseException e) {
			dto.setFechaFirmaStr("No definida");
		}
		dto.setFirma(UsuarioHelper.convertUser(firma.getIdUsuario()));
		dto.setHashfirma(firma.getHashArchivo());
		dto.setIpFirma(firma.getIp());
		dto.setSubioArchivo(firma.getSubio());
		return dto;
	}

}
