/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.datos.models.entity;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * @document PaqueteServicio
 * <AUTHOR>
 * @fecha mi<PERSON>rcole<PERSON>, enero 13 de 2021, 03:27:27 PM
 */
@Entity
@Table(name = "paquete_servicio")
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "PaqueteServicio.findAll", query = "SELECT p FROM PaqueteServicio p"),
    @NamedQuery(name = "PaqueteServicio.findByIdPaqueteServicio", query = "SELECT p FROM PaqueteServicio p WHERE p.idPaqueteServicio = :idPaqueteServicio"),
    @NamedQuery(name = "PaqueteServicio.findByCantidadFirmas", query = "SELECT p FROM PaqueteServicio p WHERE p.cantidadFirmas = :cantidadFirmas"),
    @NamedQuery(name = "PaqueteServicio.findByFechaRegistro", query = "SELECT p FROM PaqueteServicio p WHERE p.fechaRegistro = :fechaRegistro"),
    @NamedQuery(name = "PaqueteServicio.findByJsonServicio", query = "SELECT p FROM PaqueteServicio p WHERE p.jsonServicio = :jsonServicio")})
public class PaqueteServicio implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id_paquete_servicio")
    private Integer idPaqueteServicio;
    @Basic(optional = false)
    @NotNull
    @Column(name = "cantidad_firmas")
    private int cantidadFirmas;
    @Basic(optional = false)
    @NotNull
    @Column(name = "fecha_registro")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaRegistro;
    @Size(max = 900)
    @Column(name = "json_servicio")
    private String jsonServicio;
    @JoinColumn(name = "id_servicio", referencedColumnName = "id_servicio")
    @ManyToOne(optional = false)
    private Servicio idServicio;
    
    @Column(name = "id_sku")
    private int idSku;

    public PaqueteServicio() {
    }

    public PaqueteServicio(Integer idPaqueteServicio) {
        this.idPaqueteServicio = idPaqueteServicio;
    }

    public PaqueteServicio(Integer idPaqueteServicio, int cantidadFirmas, Date fechaRegistro) {
        this.idPaqueteServicio = idPaqueteServicio;
        this.cantidadFirmas = cantidadFirmas;
        this.fechaRegistro = fechaRegistro;
    }

    public Integer getIdPaqueteServicio() {
        return idPaqueteServicio;
    }

    public void setIdPaqueteServicio(Integer idPaqueteServicio) {
        this.idPaqueteServicio = idPaqueteServicio;
    }

    public int getCantidadFirmas() {
        return cantidadFirmas;
    }

    public void setCantidadFirmas(int cantidadFirmas) {
        this.cantidadFirmas = cantidadFirmas;
    }

    public Date getFechaRegistro() {
        return fechaRegistro;
    }

    public void setFechaRegistro(Date fechaRegistro) {
        this.fechaRegistro = fechaRegistro;
    }

    public String getJsonServicio() {
        return jsonServicio;
    }

    public void setJsonServicio(String jsonServicio) {
        this.jsonServicio = jsonServicio;
    }

    public Servicio getIdServicio() {
        return idServicio;
    }

    public void setIdServicio(Servicio idServicio) {
        this.idServicio = idServicio;
    }

    public int getIdSku() {
		return idSku;
	}

	public void setIdSku(int idSku) {
		this.idSku = idSku;
	}

	@Override
    public int hashCode() {
        int hash = 0;
        hash += (idPaqueteServicio != null ? idPaqueteServicio.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof PaqueteServicio)) {
            return false;
        }
        PaqueteServicio other = (PaqueteServicio) object;
        if ((this.idPaqueteServicio == null && other.idPaqueteServicio != null) || (this.idPaqueteServicio != null && !this.idPaqueteServicio.equals(other.idPaqueteServicio))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "se.firme.ms.datos.models.entity.PaqueteServicio[ idPaqueteServicio=" + idPaqueteServicio + " ]";
    }

}
