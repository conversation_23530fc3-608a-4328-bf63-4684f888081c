/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.models.service.interfaz;

import java.util.List;
import se.firme.commons.exception.FirmaException;
import se.firme.commons.models.projection.IUsuario;
import se.firme.ms.datos.models.entity.Usuario;

/**
 *
 * <AUTHOR>
 */
public interface IUsuarioService {
    public Usuario crearRegistroUsuario(Usuario usuario) throws Exception ;
    public Usuario findById(long idUsuario);

    public void editarRegistro(Usuario usuario) throws FirmaException;
    public List<Usuario> getVerificarUsuarioRegistro(String numeroDocumento, String email, String numeroCelular);
	public List<IUsuario> consultarUsuario(String id);

    public Usuario findByEmail(String email) throws FirmaException;
}
