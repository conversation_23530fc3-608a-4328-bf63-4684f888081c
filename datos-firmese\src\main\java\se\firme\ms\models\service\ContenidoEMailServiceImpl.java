/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.models.service;

import se.firme.ms.models.service.interfaz.IContenidoEMailService;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class ContenidoEMailServiceImpl implements IContenidoEMailService {

    @Override
    public String getMensajeConfirmarCorreo(String url) {
        String contenido = "";
        contenido = "<html><h2>Estimado usuario</h2>"
                + "<p>El rpesente correo tiene como finalidad validar su correo electrónico.</p>"
                + "<p>Por favor de click en el sigueitne enlace:</p>"
                + "<table >"
                + " <thead style=\"background: #ccc;\">"
                + "     <tr>"
                + "         <th style=\"padding: 10px\"></th>"
                + "     </tr>"
                + " </thead>"
                + " <tbody style=\"background:#f3f5f7\">";

        contenido += "<tr>"
                + "     <td>" + url + "</td>"
                + "   </tr>";

        contenido = contenido + "</tbody></table>"
                + "<br/>"
                + "<br/>"
                + "<p>Saludos cordiales.</p></html>";

        return contenido;
    }
}
