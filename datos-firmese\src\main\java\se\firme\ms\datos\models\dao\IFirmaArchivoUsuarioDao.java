/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.datos.models.dao;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import se.firme.ms.datos.models.entity.FirmaArchivoUsuario;

/**
 *
 * <AUTHOR>
 */
public interface IFirmaArchivoUsuarioDao extends PagingAndSortingRepository<FirmaArchivoUsuario, Long> {

    @Query(value = "select * from firma_archivo_usuario where id_archivo_firma = ?1 ", nativeQuery = true)
    public List<FirmaArchivoUsuario> findByIdArchivoFirma(long idArchivoFirma);

    @Query(value = "select * from firma_archivo_usuario where id_archivo_firma = ?1 and id_usuario = ?2 ", nativeQuery = true)
    public List<FirmaArchivoUsuario> findByIdArchivoFirmaAndIdusuario(long idArchivoFirma, long idUsuario);

    @Modifying
    @Query(value = "update firma_archivo_usuario set hash_archivo = ?1, ruta_relativa_archivo = ?2, nombre_archivo_firma = ?3 where id_firma_archivo_usuario = ?4 ", nativeQuery = true)
    public void actualizarFirma(String hashArchivo, String rutaRelativa, String nombreArchivo, long idFirmaArchivoUsuario);

    @Modifying
    @Query(value = "delete au from firma_archivo_usuario au inner join archivo_firma  af on au.id_archivo_firma = af.id_archivo_firma where au.id_archivo_firma = ?1 and au.id_usuario = ?2 and af.estado = 1", nativeQuery = true)
    public void deletePendingDocument(long idDocument, long idUsuario);

    @Query(value = "select fau.* from firma_archivo_usuario fau inner join usuario u on fau.id_usuario =u.id_usuario where fau .id_archivo_firma = ?2 and u.correo_electronico = ?1", nativeQuery = true)
    public Optional<FirmaArchivoUsuario> consultarFirmaDocumento(String correoElectronico, long idArchivoFirma);

    @Query(value = "select count(*) from firma_archivo_usuario where id_archivo_firma = ?1 ", nativeQuery = true)
    public int contarfirmantes(long idArchivoFirma);

    @Query(value = "SELECT fau.* from firma_archivo_usuario fau \n"
            + "inner join archivo_firma af on fau.id_archivo_firma = af.id_archivo_firma \n"
            + " where fau.id_archivo_firma = ?1 and fau.subio =true", nativeQuery = true)
    public List<FirmaArchivoUsuario> findByIdArchivoFirmaUsuarioPropietario(long idArchivoFirma);

    @Query(value = "select * from firma_archivo_usuario where id_usuario = ?1", nativeQuery = true)
    public List<FirmaArchivoUsuario> findByUsuario(Long idUsuario);

    @Query(value = "select fau.* from archivo_firma af \n"
            + "inner join firma_archivo_usuario fau on af.id_archivo_firma =fau.id_archivo_firma \n"
            + " inner join solicitud_firma sf on af.id_archivo_firma = sf.id_archivo_firma \n"
            + " inner join usuario u on fau.id_usuario =u.id_usuario \n"
            + "where af.id_archivo_firma =?1 and sf.firmado =false and sf.email_firmante =u.correo_electronico", nativeQuery = true)
    public List<FirmaArchivoUsuario> firmadosSolicitudPendiente(Integer idArchivo);
}
