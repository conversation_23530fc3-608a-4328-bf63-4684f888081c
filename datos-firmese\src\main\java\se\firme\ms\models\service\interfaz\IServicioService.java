/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.models.service.interfaz;

import se.firme.commons.exception.FirmaException;
import se.firme.commons.exception.ServicioFirmeseException;
import se.firme.commons.firmese.dto.ServicioDTO;
import se.firme.ms.datos.models.entity.Usuario;

/**
 * @document IServicioService
 * <AUTHOR>
 * @fecha miércoles, enero 13 de 2021, 02:05:39 PM
 */
public interface IServicioService {

    public void crearServicio(Usuario usuario) throws FirmaException;

    public boolean validarServicio(Long idUsuario,boolean propietario) throws ServicioFirmeseException;

    public boolean retornarBolsa(Long idUsuario,boolean propietario);

    public ServicioDTO findById(long idUsuario) throws FirmaException;

    public boolean agregarPaqueteServicio(long idusuario, int cantidad, int tiempoValidez, int cantOtros) throws FirmaException;
}
