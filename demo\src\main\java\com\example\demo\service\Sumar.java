/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.example.demo.service;

import java.util.List;
import java.util.stream.Collectors;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR>
 */
@RestController
public class Sumar {
    
    @GetMapping("/sumar/{num1}/{num2}")
    public int listar(@PathVariable int num1, @PathVariable int num2){
        
        
        return num1 + num2;
    }
    
}
