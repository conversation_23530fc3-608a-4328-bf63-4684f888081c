<?xml version="1.0" encoding="UTF-8"?>
<actions>
        <action>
            <actionName>run</actionName>
            <packagings>
                <packaging>jar</packaging>
            </packagings>
            <goals>
                <goal>process-classes</goal>
                <goal>org.codehaus.mojo:exec-maven-plugin:1.5.0:exec</goal>
            </goals>
            <properties>
                <exec.args>-classpath %classpath se.firme.ms.MainToken --spring.profiles.active=dev2</exec.args>
                <exec.executable>java</exec.executable>
            </properties>
        </action>
        <action>
            <actionName>debug</actionName>
            <packagings>
                <packaging>jar</packaging>
            </packagings>
            <goals>
                <goal>process-classes</goal>
                <goal>org.codehaus.mojo:exec-maven-plugin:1.5.0:exec</goal>
            </goals>
            <properties>
                <exec.args>-agentlib:jdwp=transport=dt_socket,server=n,address=${jpda.address} -classpath %classpath se.firme.ms.MainToken --spring.profiles.active=dev2</exec.args>
                <exec.executable>java</exec.executable>
                <jpda.listen>true</jpda.listen>
            </properties>
        </action>
        <action>
            <actionName>profile</actionName>
            <packagings>
                <packaging>jar</packaging>
            </packagings>
            <goals>
                <goal>process-classes</goal>
                <goal>org.codehaus.mojo:exec-maven-plugin:1.5.0:exec</goal>
            </goals>
            <properties>
                <exec.args>-classpath %classpath se.firme.ms.MainToken --spring.profiles.active=dev2</exec.args>
                <exec.executable>java</exec.executable>
            </properties>
        </action>
    </actions>
