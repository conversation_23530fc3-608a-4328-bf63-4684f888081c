package se.firme.ms.usuario.negocio;

import java.util.Date;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import se.firme.commons.exception.FirmaException;
import se.firme.commons.firmese.dto.RegistroTemporalDTO;
import se.firme.commons.firmese.util.Utilities;
import se.firme.ms.datos.models.dao.UsuarioTemporalDAO;
import se.firme.ms.datos.models.entity.UsuarioTemporal;
import se.firme.ms.models.service.UsuarioTemporalService;

@Component
public class RegistroTemporalNegocio {
	@Autowired
	private UsuarioTemporalService service;
	@Autowired
	private UsuarioTemporalDAO dao;

	public void agregarRegistro(RegistroTemporalDTO datosRegistro) throws FirmaException {
		try {

			String json = Utilities.converB64ToString(datosRegistro.getDocumento().getDocument());
			json = json.substring(json.indexOf("{"), json.indexOf("}") + 1);
			JsonElement element = new JsonParser().parse(json);
			JsonObject jsonObject = element.getAsJsonObject();
			String documentID = jsonObject.get("documentID").getAsString();
			if (!documentID.equals(datosRegistro.getNumeroDocumento())) {
				throw new FirmaException("no coinciden los datos escáneados");
			}
			Optional<UsuarioTemporal> optional = dao.findByNumeroDocumento(datosRegistro.getTipoDocumento(),
					datosRegistro.getNumeroDocumento());
			if (optional.isPresent()) {
				// throw new FirmaException("Usuario ya tiene un preregistro creado");
				UsuarioTemporal temporal = optional.get();
				temporal.setIdReferido(datosRegistro.getIdReferido());
				temporal.setFechaRegistro(new Date());
				//dao.save(temporal);
			} else {
				UsuarioTemporal temporal = new UsuarioTemporal();
				temporal.setDocumentoPersona(datosRegistro.getDocumento().getDocument());
				temporal.setFechaRegistro(new Date());
				temporal.setIdTipoDocumento(datosRegistro.getTipoDocumento());
				temporal.setNombreCompleto(datosRegistro.getNombreCompleto());
				temporal.setNumeroDocumento(datosRegistro.getNumeroDocumento());
				temporal.setRowData(datosRegistro.getDocumento().getRowData());
				temporal.setIdReferido(datosRegistro.getIdReferido());
				service.crearRegistro(temporal);
			}
		} catch (Exception e) {
			throw new FirmaException(e.getMessage());
		}
	}

}
