/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.models.service.helper;

import co.venko.ms.models.entity.AdmUsuario;
import se.firme.commons.firmese.dto.UsuarioResponseDTO;
import se.firme.ms.datos.models.entity.Usuario;

/**
 * @document UsuarioHelper
 * <AUTHOR>
 * @fecha lunes, agosto 24 de 2020, 04:49:54 PM
 */
public class UsuarioHelper {

	public static UsuarioResponseDTO convertUser(Usuario usuario) {
		UsuarioResponseDTO adm = new UsuarioResponseDTO();
		adm.setCorreoElectronico(usuario.getCorreoElectronico());
		adm.setNombreCompleto(usuario.getNombreCompleto());
		adm.setNombreTipoDocumento(usuario.getIdTipoDocumento().getNombreTipoDocumento());
		adm.setNumeroDocumento(usuario.getNumeroDocumento());
		return adm;
	}

	public static AdmUsuario convert(Usuario usuario) {
		AdmUsuario adm = new AdmUsuario();

		adm.setIdUsuario(usuario.getCorreoElectronico());
		adm.setNombreUsuario(usuario.getNombreCompleto());
		adm.setTelefonoUsuario(usuario.getNumeroCelular());
		adm.setDireccionUsuario("");
		adm.setClaveUsuario(usuario.getClave());
		adm.setActivoUsuario(usuario.getActivo());
		adm.seteMailUsuario(usuario.getCorreoElectronico());
		adm.setClientId("firmese");
		adm.setIdUsuarioEnCliente(usuario.getIdUsuario() + "");
		System.out.println("ESTOS SON LOS DATOS DE USUARIO: " + adm.getIdUsuario() + " - " + adm.getNombreUsuario()
				+ " - " + adm.getTelefonoUsuario() + " - " + adm.getDireccionUsuario() + " - " + adm.getClaveUsuario()
				+ " - " + adm.getActivoUsuario() + " - " + adm.geteMailUsuario() + " - " + adm.getClientId() + " - "
				+ adm.getIdUsuarioEnCliente());
		return adm;
	}

}
