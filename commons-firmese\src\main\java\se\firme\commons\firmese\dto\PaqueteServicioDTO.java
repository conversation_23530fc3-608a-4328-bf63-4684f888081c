/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.commons.firmese.dto;

/**
 * @document PaqueteServicioDTO
 * <AUTHOR>
 * @fecha lunes, enero 18 de 2021, 10:13:10 AM
 */
public class PaqueteServicioDTO {

    private Integer idPaqueteServicio;
    private int cantidadFirmas;
    private String fechaRegistro;
    private String jsonServicio;
    private long idServicio;
    private int idSku;

    public Integer getIdPaqueteServicio() {
        return idPaqueteServicio;
    }

    public void setIdPaqueteServicio(Integer idPaqueteServicio) {
        this.idPaqueteServicio = idPaqueteServicio;
    }

    public int getCantidadFirmas() {
        return cantidadFirmas;
    }

    public void setCantidadFirmas(int cantidadFirmas) {
        this.cantidadFirmas = cantidadFirmas;
    }

    public String getFechaRegistro() {
        return fechaRegistro;
    }

    public void setFechaRegistro(String fechaRegistro) {
        this.fechaRegistro = fechaRegistro;
    }

    public String getJsonServicio() {
        return jsonServicio;
    }

    public void setJsonServicio(String jsonServicio) {
        this.jsonServicio = jsonServicio;
    }

    public long getIdServicio() {
        return idServicio;
    }

    public void setIdServicio(long idServicio) {
        this.idServicio = idServicio;
    }

	public int getIdSku() {
		return idSku;
	}

	public void setIdSku(int idSku) {
		this.idSku = idSku;
	}

}
