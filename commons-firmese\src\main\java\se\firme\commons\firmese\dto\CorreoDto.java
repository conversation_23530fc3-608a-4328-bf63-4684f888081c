/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.commons.firmese.dto;

import java.io.Serializable;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CorreoDto implements Serializable {

    private static final long serialVersionUID = -4508231721284023062L;

    private String destinatario;
    private String destinatarioCC;
    private String destinatarioBCC;
    private String asunto;
    private String mensaje;
    private String rutaAdjunto;
    private String nombreAdjunto;
    private String smtp;
    private String puerto;
    private String remitente;
    private String clave;
    private Map<String, String> mapInlineImages;

    public CorreoDto() {
        destinatario = "";
        destinatarioCC = "";
        destinatarioBCC = "";
        asunto = "";
        mensaje = "";
        rutaAdjunto = "";
        nombreAdjunto = "";
        smtp = "";
        puerto = "";
        remitente = "";
        clave = "";
    }

    public String getDestinatario() {
        return destinatario;
    }

    public void setDestinatario(String destinatario) {
        this.destinatario = destinatario;
    }

    public String getDestinatarioCC() {
        return destinatarioCC;
    }

    public void setDestinatarioCC(String destinatarioCC) {
        this.destinatarioCC = destinatarioCC;
    }

    public String getDestinatarioBCC() {
        return destinatarioBCC;
    }

    public void setDestinatarioBCC(String destinatarioBCC) {
        this.destinatarioBCC = destinatarioBCC;
    }

    public String getAsunto() {
        return asunto;
    }

    public void setAsunto(String asunto) {
        this.asunto = asunto;
    }

    public String getMensaje() {
        return mensaje;
    }

    public void setMensaje(String mensaje) {
        this.mensaje = mensaje;
    }

    public String getRutaAdjunto() {
        return rutaAdjunto;
    }

    public void setRutaAdjunto(String rutaAdjunto) {
        this.rutaAdjunto = rutaAdjunto;
    }

    public String getNombreAdjunto() {
        return nombreAdjunto;
    }

    public void setNombreAdjunto(String nombreAdjunto) {
        this.nombreAdjunto = nombreAdjunto;
    }

    public String getSmtp() {
        return smtp;
    }

    public void setSmtp(String smtp) {
        this.smtp = smtp;
    }

    public String getPuerto() {
        return puerto;
    }

    public void setPuerto(String puerto) {
        this.puerto = puerto;
    }

    public String getRemitente() {
        return remitente;
    }

    public void setRemitente(String remitente) {
        this.remitente = remitente;
    }

    public String getClave() {
        return clave;
    }

    public void setClave(String clave) {
        this.clave = clave;
    }

    public Map<String, String> getMapInlineImages() {
        return mapInlineImages;
    }

    public void setMapInlineImages(Map<String, String> mapInlineImages) {
        this.mapInlineImages = mapInlineImages;
    }

}
