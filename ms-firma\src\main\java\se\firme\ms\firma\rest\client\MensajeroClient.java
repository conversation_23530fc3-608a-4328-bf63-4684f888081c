package se.firme.ms.firma.rest.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import se.firme.commons.firmese.dto.MensajeDTO;


@FeignClient(name = "ms-mensajero", url = "${mensajero.base.uri}")
public interface MensajeroClient {
	
	@PostMapping(value = "/sms")
	public String send(@RequestBody MensajeDTO mensajeDTO);
}
