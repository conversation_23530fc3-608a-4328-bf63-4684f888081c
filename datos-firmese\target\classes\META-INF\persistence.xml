<?xml version="1.0" encoding="UTF-8"?>
<persistence version="2.1" xmlns="http://xmlns.jcp.org/xml/ns/persistence" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/persistence http://xmlns.jcp.org/xml/ns/persistence/persistence_2_1.xsd">
  <persistence-unit name="se.firme_datos-firmese_jar_1.0PU" transaction-type="RESOURCE_LOCAL">
    <provider>org.eclipse.persistence.jpa.PersistenceProvider</provider>
    <class>se.firme.ms.datos.models.entity.Parametro</class>
    <class>se.firme.ms.datos.models.entity.ProcesoFirma</class>
    <class>se.firme.ms.datos.models.entity.PaqueteServicio</class>
    <class>se.firme.ms.datos.models.entity.Servicio</class>
      <class>se.firme.ms.datos.models.entity.SolicitudFirma</class>
      <properties>
      <property name="javax.persistence.jdbc.url" value="*************************************************************************"/>
      <property name="javax.persistence.jdbc.user" value="root"/>
      <property name="javax.persistence.jdbc.driver" value="com.mysql.jdbc.Driver"/>
      <property name="javax.persistence.jdbc.password" value="amapola"/>
    </properties>
  </persistence-unit>
</persistence>
