package se.firme.ms.firma.Utils;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import se.firme.commons.firmese.dto.ApiResponse; 

import java.util.Arrays;
import se.firme.commons.exception.FirmaException;

public class FirmaPaginacionUtil {
    // Lista de campos permitidos para ordenamiento basados en los alias de las consultas SQL
    private static final List<String> ordenamientosPermitidos = Arrays.asList(
        "id_archivo_firma",
        "nombre_archivo",
        "cantidad_firmas",
        "cantidad_firmado",
        "email_firmantes",
        "fecha_registro",
        "ip",
        "hash_archivo",
        "descripcion",
        "fecha_vencimiento"
    );
    public static void validarPaginacion(Integer page, Integer perPage, String nombre, String sortBy, String sortDirection){
        if (page < 0) {
            throw new IllegalArgumentException("Número de página no válido");
        }

        if (perPage <= 0 || perPage > 20) {
            throw new IllegalArgumentException("Tamaño de página no válido, el máximo es "+20);
        }

        if (nombre != null && nombre.length() > 200) {
            throw new IllegalArgumentException("El criterio de búsqueda excede la longitud máxima permitida (100 caracteres)");
        }
        // Validar que el campo de ordenamiento sea permitido
        if (!ordenamientosPermitidos.contains(sortBy)) {
            throw new IllegalArgumentException("Campo de ordenamiento no válido. Campos permitidos: " + String.join(", ", ordenamientosPermitidos));
        }

        if (!sortDirection.equalsIgnoreCase("asc") && !sortDirection.equalsIgnoreCase("desc")) {
            throw new IllegalArgumentException("Dirección de ordenamiento inválida");
        }

    }
    public static ResponseEntity<?> respuestaErrorPage(String mensaje, HttpStatus status) {
        return new ResponseEntity<>(
            new ApiResponse.error().mensaje(mensaje).build(), 
            status
        );
    }
    
}
