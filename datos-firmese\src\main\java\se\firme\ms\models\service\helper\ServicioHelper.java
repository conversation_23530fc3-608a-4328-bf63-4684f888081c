/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package se.firme.ms.models.service.helper;

import java.text.ParseException;
import se.firme.commons.firmese.dto.ServicioDTO;
import se.firme.commons.exception.FirmaException;
import se.firme.commons.firmese.util.Parameters;
import se.firme.commons.firmese.util.Utilities;
import se.firme.ms.datos.models.entity.Servicio;

/**
 * @document ServicioHelper
 * <AUTHOR>
 * @fecha jueves, enero 14 de 2021, 09:54:49 PM
 */
public class ServicioHelper {

	public static ServicioDTO convert(Servicio servicio) throws FirmaException {
		try {
			ServicioDTO dto = new ServicioDTO();
			dto.setCantidadFirmas(servicio.getCantidadFirmas());
			dto.setFechaActualizacion(Utilities.getFechaDateAFechaTexto(servicio.getFechaActualizacion(),
					"EEEE, dd 'de' MMMM 'de' yyyy HH:mm"));
			dto.setFechaVencimiento(Utilities.getFechaDateAFechaTexto(servicio.getFechaVencimiento(),
					"EEEE, dd 'de' MMMM 'de' yyyy HH:mm"));
			dto.setIdServicio(servicio.getIdServicio());
			dto.setTipoServicio(servicio.getTipoServicio().equals("POS") ? "POSPAGO" : "PREPAGO");
			dto.setEndpointCBack(servicio.getEndpointCBack());
			dto.setEndpointCBackHabilitado(servicio.isEndpointCBackHabilitado());
			dto.setTipoValidacion(servicio.getTipoValidacion());
			dto.setCantOtros(servicio.getCantOtros());
			dto.setDetalleTipoValidacion(getDetalleValidacion(servicio.getTipoValidacion()));
			dto.setNotificarFirma(servicio.isNotificarFirma());
			dto.setOrisHabilitado(servicio.isOrisHabilitado());
			return dto;
		} catch (ParseException | NullPointerException e) {
			throw new FirmaException(e.getMessage());
		}
	}

	private static String getDetalleValidacion(String tipoValidacion) {
		switch (tipoValidacion) {
		case Parameters.string.TIPO_VALIDACION_FIRMAS_TIEMPO:
			return "Validación de servicio de firmas por tiempo";
		case Parameters.string.TIPO_VALIDACION_FIRMAS_CANTIDAD:
			return "Validación de servicio por cantidad de firmas";
		default:
			return "No definida";
		}
	}

}
