/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.models.service;

import se.firme.ms.models.service.interfaz.IBitacoraRegistroService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import se.firme.ms.datos.models.dao.IBitacoraRegistroDao;
import se.firme.ms.datos.models.entity.BitacoraRegistro;
import se.firme.ms.datos.models.entity.Usuario;

/**
 *
 * <AUTHOR>
 */
@Service
public class BitacoraRegistroServiceImpl implements IBitacoraRegistroService {

    @Autowired
    IBitacoraRegistroDao bitacoraDao;
    
    @Override
    @Transactional
    public void registrarBitacora(Usuario usuario) {

        BitacoraRegistro br = new BitacoraRegistro();
        br.setCodigoSms("");
        br.setCorreoElectronico(usuario.getCorreoElectronico());
        br.setIdUsuario(usuario);
        br.setNumeroCelular(usuario.getNumeroCelular());
        
        bitacoraDao.save(br);
    }
}
