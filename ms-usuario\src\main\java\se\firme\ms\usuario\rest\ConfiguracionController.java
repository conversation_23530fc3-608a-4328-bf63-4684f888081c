/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.usuario.rest;

import java.util.HashMap;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 *
 * <AUTHOR>
 */
@RefreshScope // actualizar la configuración en caliente sin tener que reiniciar mediante un endpoint de actuator ej. localhost:49792/actuator/refresh )(POST)
@RestController
public class ConfiguracionController {
    
    @Autowired
    private Environment env;

    
    @GetMapping("/obtener-configuracion")
    public ResponseEntity<?> obtenerConfiguracion(){
        Map<String, String> json = new HashMap<>();

        json.put("port", env.getProperty("server.port"));
        json.put("string database", env.getProperty("spring.datasource.url"));

        return new ResponseEntity<Map<String, String>>(json, HttpStatus.OK);
    }
}
