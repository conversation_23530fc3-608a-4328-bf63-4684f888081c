/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * @document MainCatalogo
 * <AUTHOR>
 * @fecha martes, agosto 19 de 2020, 07:00:00 am
 */
@EnableFeignClients
@EnableEurekaClient
@SpringBootApplication
@EntityScan({"se.firme.ms.datos.models.entity"})
@EnableJpaRepositories("se.firme.ms.datos.models.dao")
public class MainFirma {

    public static void main(String[] args) {
        new SpringApplication(MainFirma.class).run(args);
    }
}
