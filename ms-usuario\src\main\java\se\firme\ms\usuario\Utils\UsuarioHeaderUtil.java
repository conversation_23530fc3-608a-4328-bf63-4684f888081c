package se.firme.ms.usuario.Utils;
import javax.servlet.http.HttpServletRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpStatus;
import se.firme.commons.firmese.dto.ApiResponse;

public class UsuarioHeaderUtil {  // Algoritmo 35

    public static Long obtenerIdUsuario(HttpServletRequest request) throws IllegalArgumentException {
        String xUserId = request.getHeader("X-USER-ID");
        if (xUserId == null || xUserId.trim().isEmpty()) {
            throw new IllegalArgumentException("Header X-USER-ID requerido");
        }
        Long idUsuario;
        try {
            idUsuario = Long.parseLong(xUserId.trim());
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("X-USER-ID debe ser un número válido");
        }
        if (idUsuario <= 0) {
            throw new IllegalArgumentException("X-USER-ID debe ser mayor a 0");
        }
        return idUsuario;
    }

    public static ResponseEntity<ApiResponse> respuestaError(String mensaje, HttpStatus status) {
        return new ResponseEntity<>(new ApiResponse.error().mensaje(mensaje).build(), status);
    }
}