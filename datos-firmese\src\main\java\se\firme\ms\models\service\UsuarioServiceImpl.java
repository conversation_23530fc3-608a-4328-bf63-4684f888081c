/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.models.service;

import se.firme.commons.models.projection.IUsuarioOris;
import se.firme.ms.models.service.interfaz.IUsuarioService;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import se.firme.commons.exception.FirmaException;
import se.firme.commons.models.projection.IUsuario;
import se.firme.ms.datos.models.entity.Usuario;
import se.firme.ms.datos.models.dao.IUsuarioDao;
import se.firme.ms.models.service.interfaz.IServicioService;

/**
 *
 * <AUTHOR>
 */
@Service
public class UsuarioServiceImpl implements IUsuarioService {

	@Autowired
	private IUsuarioDao dao;
	@Autowired
	private IServicioService iServicioDAO;

	@Autowired
	private PasswordEncoder passwordEncoder;

	@Override
	@Transactional
	public Usuario crearRegistroUsuario(Usuario usuario) throws Exception {
		// VALIDAR SI EXISTE EL USUARIO
		List<Usuario> u = dao.buscarUsuarioExistente(usuario.getNumeroDocumento(), usuario.getCorreoElectronico(),
				usuario.getNumeroCelular());

		if (u != null && !u.isEmpty()) {
			throw new Exception(
					"El usuario con el correo electrónico o número de documento o número de celular indicado ya se encuentra registrado o en proceso de registro");
		}

		usuario.setActivo(false);
		usuario.setProcesoRegistro(true);
		usuario.setVerificadoFuente(false);
		usuario.setObervacionVerificacion("Pendiente verificar");

		// ENCRIPTAR CLAVE
		String passwordBCrypt = passwordEncoder.encode(usuario.getClave());
		usuario.setClave(passwordBCrypt);
		return dao.save(usuario);
	}

	@Bean
	public PasswordEncoder passwordEncoder() {
		return new BCryptPasswordEncoder();
	}

	@Transactional
	public void actualizarPreregistro(long idUsuario) throws FirmaException {
		try {
			dao.actualizarPreregistro(idUsuario);
		} catch (Exception e) {
			throw new FirmaException(e.getMessage());
		}
	}

	@Override
	public Usuario findById(long idUsuario) {
		return dao.findById(idUsuario).orElse(null);
	}

	@Override
	@Transactional
	public void editarRegistro(Usuario usuario) throws FirmaException {
		try {
			dao.save(usuario);

			iServicioDAO.crearServicio(usuario);
		} catch (Exception e) {
			throw new FirmaException("No se pudo completar el registro " + e.getMessage());
		}
	}

	@Override
	public List<Usuario> getVerificarUsuarioRegistro(String numeroDocumento, String email, String numeroCelular) {
		return dao.buscarUsuarioExistente(numeroDocumento, email, numeroCelular);
	}

	public Usuario findByEmail(String email) throws FirmaException {
		try {
			Optional<Usuario> optional = dao.findByEmail(email);
			if (optional.isPresent()) {
				return optional.get();
			}
			throw new FirmaException("No se encontro registro para el correo " + email);
		} catch (Exception e) {
			throw new FirmaException("No se pudo consultar en la base de datos " + e.getMessage());
		}
	}

	@Override
	public List<IUsuario> consultarUsuario(String id) {
		
		return dao.findByID(id);
	}

	public Usuario findByCedulaEmail(String numeroDocumento, String email) {
		Optional<Usuario> optional = dao.findByCedulaEmail(numeroDocumento,email);
		if (optional.isPresent()) {
			return optional.get();
		}
		return null;
	}

	@Transactional
	public boolean eliminarCuenta(long idUsuario) throws FirmaException {
		try {
			Optional<Usuario> optional=dao.findById(idUsuario);
			if(optional.isPresent()) {
				Usuario usuario=optional.get();
				if(usuario.isEstado()) {
					usuario.setEstado(false);
					usuario.setActivo(false);
					usuario.setClave("USUARIO-ELIMINADO");
					dao.save(usuario);
					dao.eliminarRegistroUsuarioGestion(idUsuario);
					return true;
				}
				return false;
			}
			return false;
		} catch (Exception e) {
			throw new FirmaException(""+e.getMessage());
		}
	}

	public IUsuarioOris findByNumeroDocumentoOrCorreoElectronico(String docOrEmail) {
		Optional<IUsuarioOris> optional = dao.findByNumeroDocumentoOrCorreoElectronico(docOrEmail);

		return optional.orElse(null);
	}

	public Usuario findByNumeroDocumento(String doc) {
		Optional<Usuario> optional = dao.findByNumeroDocumento(doc);

		return optional.orElse(null);
	}
}
