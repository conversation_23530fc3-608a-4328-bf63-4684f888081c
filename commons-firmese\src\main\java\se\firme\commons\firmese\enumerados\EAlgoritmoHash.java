/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.commons.firmese.enumerados;

/**
 *
 * <AUTHOR>
 */
public enum EAlgoritmoHash {
    SHA512 {
        @Override
        public String getId() {
            return "SHA-512";
        }
    },
    SHA256 {
        @Override
        public String getId() {
            return "SHA-256";
        }
    },
    MD5 {
        @Override
        public String getId() {
            return "MD5";
        }
    },
    
    ;

    public abstract String getId();


    public static boolean isHashValido(String tipo) {
        boolean rta = false;
        for (int i = 0; i < EAlgoritmoHash.values().length; i++) {
            EAlgoritmoHash get = EAlgoritmoHash.values()[i];

            if (get.getId().equals(tipo)) {
                rta = true;
                break;
            }
        }
        return rta;
    }
    
}
