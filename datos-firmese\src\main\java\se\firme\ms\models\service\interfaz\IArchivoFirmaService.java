/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.models.service.interfaz;

import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import se.firme.commons.exception.FirmaException;
import se.firme.commons.firmese.dto.ArchivoFirmaResponseDTO;
import se.firme.commons.firmese.dto.DetalleSolicitudDTO;
import se.firme.commons.firmese.dto.FirmaRequestDTO;
import se.firme.ms.datos.models.entity.ArchivoFirma;
import se.firme.commons.models.projection.IDatosArchivo;
import se.firme.commons.models.projection.IDocumentosEstado;

public interface IArchivoFirmaService {
    public ArchivoFirma validarSePuedeFirmar(String hashArchivo);

    public ArchivoFirma guardarArchivoFirma(String nombreArchivo, String hashArchivo, int cantidadFirmas, int estado,
                                            String ip, long idUsuario, String rutaRelativa);
    public ArchivoFirma guardarArchivoFirma(String nombreArchivo, String hashArchivo, int cantidadFirmas, int estado,
            String ip, long idUsuario, String rutaRelativa, String descripcion);

    public List<ArchivoFirma> getArchivoFirmadoByHash(String hashArchivo);

    public void actualizarCantidadConsultas(long idArchivoFirma);

    public List<IDocumentosEstado> findByEstado(long idUsuario, int estado);

    public List<IDocumentosEstado> findByIdUsuarioCodigoTransaccion(long idUsuario, String codigoTransaccion);

    public ArchivoFirma findById(long idArchivoFirma);

    public void actualizarCantidadFirmas(int cantidadFirmado, long idArchivoFirma);

    public void actualizarEstadoArchivoFirma(long idArchivoFirma, int idEstado);

    public List<IDatosArchivo> findByArchivoEstado(long idDusuario, int i);

    public List<ArchivoFirma> findByIdUsuarioArchivoFirmado(long idDusuario);

    public Page<IDocumentosEstado> findByEstadoPag(int idUsuario, int estado, String nombre, Pageable paging) throws FirmaException;

    public Page<ArchivoFirma> findByIdUsuarioArchivoFirmadoPag(long idDusuario, Pageable paging);

    public List<ArchivoFirma> findByCodProcess(String csms, long idUsuario) throws FirmaException;

    public List<ArchivoFirma> findByIds(List<String> idsArchivos);

    public List<ArchivoFirma> findByHash(String hash);

    public boolean eliminarArchivo(long idDocument, long idUsuario) throws FirmaException;

    public void update(ArchivoFirma archivoFirma) throws FirmaException;

    public List<ArchivoFirmaResponseDTO> buscarArchivos(String ids) throws FirmaException;
    public List<ArchivoFirma> buscarArchivosFirma(String ids) throws FirmaException;
    
    public  void verificarTipoFirma(List<FirmaRequestDTO> documentos) throws FirmaException;

    public DetalleSolicitudDTO buscarFirmantesSolicitados(int idUsuario, int idArchivo) throws FirmaException;

    public List<ArchivoFirma> findByIdUsuario(Long idUsuario) throws FirmaException;

    public List<ArchivoFirma> findArchivosTyCIndividualesPorEmail(String emailFirmante, Long idUsuarioPropietario);
}
