/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package se.firme.ms.datos.models.dao;

import java.util.Date;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import se.firme.commons.models.projection.IArchivoFirma;
import se.firme.commons.models.projection.IDatosArchivo;
import se.firme.commons.models.projection.IDocumentosEstado;
import se.firme.commons.models.projection.IFirmante;
import se.firme.ms.datos.models.entity.ArchivoFirma;

@Repository
public interface IArchivoFirmaDao extends JpaRepository<ArchivoFirma, Long> {

    @Query(value = "select * from archivo_firma where hash_archivo = ?1 ", nativeQuery = true)
    public List<ArchivoFirma> findByHash(String hashArchivo);
    
    @Query(value = "select af.* from archivo_firma af left join firma_archivo_usuario fau ON af.id_archivo_firma =fau.id_archivo_firma where af.hash_archivo =?1 or fau.hash_archivo = ?2",nativeQuery = true)
    public List<ArchivoFirma> findByBothHash(String hashArchivo,String h2);

    @Modifying
    @Query(value = "update archivo_firma set cantidad_firmado = ?1 where id_archivo_firma = ?2 ", nativeQuery = true)
    public void actualizarCantidadFirmas(int cantidadFirmado, long idArchivoFirma);

    @Modifying
    @Query(value = "update archivo_firma set cantidad_consultas = cantidad_consultas + 1 where id_archivo_firma = ?1 ", nativeQuery = true)
    public void actualizarCantidadConsultas(long idArchivoFirma);

    @Query(value = "select af.id_archivo_firma as idArchivo,af.nombre_archivo as nombreArchivo, 'N/A' as codigoTransaccion, case when af.estado = 1 then 'Pendiente Firma' else 'Firmado' end  as estado ,af.fecha_registro as fechaRegistro ,af.ip as ip , af.hash_archivo as hashArchivo "
            + "from archivo_firma af  where af.id_usuario = ?1 and af.estado = ?2", nativeQuery = true)
    public List<IDocumentosEstado> findByEstado(long idUsuario, int estado);

    @Query(value = "select af.id_archivo_firma as idArchivo,af.nombre_archivo as nombreArchivo,'' as codigoTransaccion, af.cantidad_firmas as firmasRequeridas, af.cantidad_firmado as totalFirmas, case af.tipo_firma when 'MULTIPLE' then 'Otros y yo' when 'OTHERS' then 'Solo otros' when 'SINGLE' then 'Solo yo' else 'Sin firmar' end as tipoFirma, af.email_firmantes as firmantes, "
            + " case when af.estado = 1 then 'Pendiente Firma' else 'Firmado' end  as estado ,af.fecha_registro as fechaRegistro ,af.ip as ip , af.hash_archivo as hashArchivo, af.descripcion as descripcion "
            + " from archivo_firma af  where af.id_usuario = :id and af.estado = :estado order by af.id_archivo_firma desc"
            + " ",
             countQuery = "select count(1) "
            + " from archivo_firma af where af.id_usuario = :id and af.estado = :estado",
             nativeQuery = true)
    public Page<IDocumentosEstado> findByEstado(@Param("id") int id,@Param("estado") int estado, Pageable paging);

    @Query(value = "select af.id_archivo_firma as idArchivo,af.nombre_archivo as nombreArchivo,'' as codigoTransaccion, af.cantidad_firmas as firmasRequeridas, af.cantidad_firmado as totalFirmas, case af.tipo_firma when 'MULTIPLE' then 'Otros y yo' when 'OTHERS' then 'Solo otros' when 'SINGLE' then 'Solo yo' else 'Sin firmar' end as tipoFirma, af.email_firmantes as firmantes, "
            + " case when af.estado = 1 then 'Pendiente Firma' else 'Firmado' end  as estado ,af.fecha_registro as fechaRegistro ,af.ip as ip , af.hash_archivo as hashArchivo, af.descripcion as descripcion "
            + ",IF(sf.id_archivo_firma IS NOT NULL, 'SI', 'NO') as esSolicitud" +
            ", u.nombre_completo as propietario" +
            ", u.numero_documento as numeroDocumentoPropietario" +
            ", IF(sf.firmado = 1, 'SI', 'NO') as solicitudFirmada" +
            ", sf.fecha_vencimiento as fechaVencimiento" +
            " from archivo_firma af" +
            " inner join usuario u on af.id_usuario = u.id_usuario" +
            " left join solicitud_firma sf on af.id_archivo_firma = sf.id_archivo_firma and sf.firmado = IF(:estado = 1, 0, 1) and sf.id_usuario != :id" +
            " where (af.id_usuario = :id or (sf.email_firmante = :email and sf.id_usuario != :id))" +
            " and af.estado = :estado" +
            " and (sf.fecha_vencimiento > now() or sf.fecha_vencimiento IS NULL)" +
            " order by af.id_archivo_firma desc"
            + " ",
            countQuery = "select count(1) "
                    + " from archivo_firma af" +
                    " left join solicitud_firma sf on af.id_archivo_firma = sf.id_archivo_firma and sf.firmado = IF(:estado = 1, 0, 1) and sf.id_usuario != :id" +
                    " where (af.id_usuario = :id or (sf.email_firmante = :email and sf.id_usuario != :id)) and af.estado = :estado" +
                    " and (sf.fecha_vencimiento > now() or sf.fecha_vencimiento IS NULL)" +
                    "",
            nativeQuery = true)
    public Page<IDocumentosEstado> findByEstado(@Param("id") int id,@Param("estado") int estado, @Param("email") String email, Pageable paging);
    
     @Query(value = "select af.* from archivo_firma af left join firma_archivo_usuario fau on af.id_archivo_firma = fau.id_archivo_firma where fau.id_usuario = ?1 and af.estado = ?2 order by af.id_archivo_firma",
             countQuery = "select count(1) from archivo_firma af left join firma_archivo_usuario fau on af.id_archivo_firma = fau.id_archivo_firma where fau.id_usuario = ?1 and af.estado = ?2",
             nativeQuery = true)
    public Page<ArchivoFirma> findAllByUserAndEstado(long idUsuario, int estado, Pageable paging);
     

    @Query(value = "select af.* \n"
            + "from archivo_firma af \n"
            + "	inner join firma_archivo_usuario fau on af.id_archivo_firma =fau.id_archivo_firma \n"
            + "where  fau.id_usuario = ?1 and af.estado = 2 "
            + " order by ?#{#paging}"
            , countQuery = "select count(1) \n"
            + "from archivo_firma af \n"
            + "	inner join firma_archivo_usuario fau on af.id_archivo_firma =fau.id_archivo_firma \n"
            + "where  fau.id_usuario = ?1 and af.estado = 2 "
            , nativeQuery = true)
    public Page<ArchivoFirma> findByIdUsuarioArchivoFirmadoPag(long idUsuario, Pageable paging);

    @Query(value = "select af.nombre_archivo as nombreArchivo \n"
            + "     ,t.codigo_transaccion as codigoTransaccion \n"
            + "     ,case when af.estado = 1 then 'Pendiente Firma' else 'Firmado' end  as estado \n"
            + "     ,af.fecha_registro as fechaRegistro \n"
            + "     ,af.ip as ip \n"
            + "from archivo_firma af \n"
            + "     inner join token t on af.id_archivo_firma = t.id_archivo_firma \n"
            + "where t.id_usuario = ?1 \n"
            + "and t.codigo_transaccion = ?2 ", nativeQuery = true)
    public List<IDocumentosEstado> findByIdUsuarioCodigoTransaccion(long idUsuario, String codigoTransaccion);

    @Modifying
    @Query(value = "update archivo_firma set estado = ?2 where id_archivo_firma = ?1 ", nativeQuery = true)
    public void actualizarEstadoArchivoFirma(long idArchivoFirma, int idEstado);

    @Query(value = "select af.id_archivo_firma as idArchivo, af.nombre_archivo  as nombreArchivo, af.hash_archivo as hashArchivo, ifnull(af.ip,'0.0.0.0') as ipOrigen, af.cantidad_firmas as cantidadFirmas, af.cantidad_firmado as cantidadFirmado, DATE_FORMAT(af.fecha_registro,'%Y-%m-%d %T')  as fechaRegistro, af.cantidad_consultas  as cantidadConsultas from archivo_firma af inner join firma_archivo_usuario fau on af.id_archivo_firma =fau.id_archivo_firma where  fau.id_usuario = ?1 and af.estado = ?2", nativeQuery = true)
    public List<IDatosArchivo> findByArchivoEstado(long idDusuario, int i);

    @Query(value = "select af.* \n"
            + "from archivo_firma af \n"
            + "	inner join firma_archivo_usuario fau on af.id_archivo_firma =fau.id_archivo_firma \n"
            + "where  fau.id_usuario = ?1 and af.estado = 2 ", nativeQuery = true)
    public List<ArchivoFirma> findByIdUsuarioArchivoFirmado(long idUsuario);

    @Query(value = "SELECT af.* FROM archivo_firma af " +
       "INNER JOIN usuario_firma_archivo_otp ufao ON af.id_archivo_firma = ufao.id_archivo_firma " +
       "INNER JOIN proceso_firma pf ON ufao.id_proceso_firma = pf.id_proceso_firma " +
       "WHERE pf.otp = ?1 AND pf.id_usuario = ?2 AND ufao.estado = 1", 
       nativeQuery = true)
    public List<ArchivoFirma> findByCodProcess(String csms, long idUsuario);

    @Query(value = "select * from archivo_firma where id_archivo_firma IN (?1) ", nativeQuery = true)
    public List<ArchivoFirma> findByIds(List<String> idsArchivos);

    @Modifying
    @Query(value = "delete from archivo_firma where estado = 1 and id_archivo_firma = ?1 and id_usuario = ?2", nativeQuery = true)
    public void deletePendingDocument(long idDocument, long idUsuario);
    
    @Query(value = "select * from archivo_firma af where af.id_archivo_firma in ( ?1 ) and af.estado = 1", nativeQuery = true)
	public List<ArchivoFirma> buscarArchivos(List<String> ids);

    @Query(value = "select t.email_firmante as firmante,u.id_usuario as idusuario, t.fecha_registro as solicitud, t.fecha_vencimiento as vencimiento, t.codigo_transaccion as codigo, ifnull(u.nombre_completo,'No registrado') as nombreFirmante, case  t.activo when true then 'SI' else 'NO' end as activo from token t left join usuario u on t.email_firmante = u.correo_electronico where t.id_usuario = ?1 and t.tipo = ?2 and t.ids like ?3 GROUP BY t.email_firmante, u.id_usuario, t.fecha_registro, t.fecha_vencimiento, t.codigo_transaccion, t.activo", nativeQuery = true)
	public List<IFirmante> buscarFirmantesSolicitados(int idUsuario, int i, String idArchivo);
    
    @Query(value = "select af.id_archivo_firma as idArchivo, case when af.id_usuario = :id then 'SI' else 'NO' end as propietario,  af.nombre_archivo as nombreArchivo,af.hash_archivo as  hashArchivo,case af.tipo_firma when 'SINGLE' then 'Solo yo firmo' when 'MULTIPLE' then 'Firmo yo y otros' when 'OTHERS' then 'Firman otros'  else 'DEF' end as tipoFirma, COUNT(*) as totalFirmas,af.fecha_registro as fechaRegistroStr, af.cantidad_consultas as cantidadConsultas "
    		+ "from archivo_firma af left join firma_archivo_usuario fau on af.id_archivo_firma =fau.id_archivo_firma "
    		+ "where  af.estado = 2 "
    		+ "and af.id_usuario = :id or fau.id_usuario = :id "
    		+ "GROUP BY af.id_archivo_firma, af.id_usuario, af.nombre_archivo, af.tipo_firma,af.cantidad_consultas,af.hash_archivo order by af.fecha_registro desc"
    	
            , nativeQuery = true)
    public List<IArchivoFirma> findArchivosFirmados(@Param("id") long id, Pageable paging);
    
    @Query(value = "select af.id_archivo_firma as idArchivo,af.nombre_archivo as nombreArchivo,'' as codigoTransaccion, af.cantidad_firmas as firmasRequeridas, af.cantidad_firmado as totalFirmas, case af.tipo_firma when 'MULTIPLE' then 'Otros y yo' when 'OTHERS' then 'Solo otros' when 'SINGLE' then 'Solo yo' else 'Sin firmar' end as tipoFirma, af.email_firmantes as firmantes, \n"
    		+ " case when af.estado = 1 then 'Pendiente Firma' else 'Firmado' end  as estado ,af.fecha_registro as fechaRegistro ,af.ip as ip , af.hash_archivo as hashArchivo \n"
    		+ "from archivo_firma af inner join firma_archivo_usuario fau on fau.id_archivo_firma = af.id_archivo_firma where fau.id_usuario = :id and af.estado = :estado",
             countQuery = "select count(1) "
            + " from archivo_firma af inner join firma_archivo_usuario fau on fau.id_archivo_firma = af.id_archivo_firma where fau.id_usuario = :id and af.estado = :estado",
             nativeQuery = true)
    public Page<IDocumentosEstado> findByFirmaUsuario(@Param("id") int id, @Param("estado") int estado,Pageable paging);

    // consulta para manejar la busqueda en la tabla firmados
    @Query(value = "SELECT af.id_archivo_firma as idArchivo, af.nombre_archivo as nombreArchivo, '' as codigoTransaccion, " +
            "af.cantidad_firmas as firmasRequeridas, af.cantidad_firmado as totalFirmas, " +
            "CASE af.tipo_firma WHEN 'MULTIPLE' THEN 'Otros y yo' WHEN 'OTHERS' THEN 'Solo otros' WHEN 'SINGLE' THEN 'Solo yo' ELSE 'Sin firmar' END as tipoFirma, " +
            "af.email_firmantes as firmantes, " +
            "CASE WHEN af.estado = 1 THEN 'Pendiente Firma' ELSE 'Firmado' END as estado, " +
            "af.fecha_registro as fechaRegistro, af.ip as ip, af.hash_archivo as hashArchivo " +
            "FROM archivo_firma af " +
            "INNER JOIN firma_archivo_usuario fau ON fau.id_archivo_firma = af.id_archivo_firma " +
            "WHERE fau.id_usuario = :userId " +
            "AND af.estado = :estado " +
            "AND (:nombre IS NULL OR af.nombre_archivo LIKE CONCAT('%', :nombre, '%'))", 
    countQuery = "SELECT COUNT(1) " +
            "FROM archivo_firma af " +
            "INNER JOIN firma_archivo_usuario fau ON fau.id_archivo_firma = af.id_archivo_firma " +
            "WHERE fau.id_usuario = :userId " +
            "AND af.estado = :estado " +
            "AND (:nombre IS NULL OR af.nombre_archivo LIKE CONCAT('%', :nombre, '%'))", 
    nativeQuery = true)
    Page<IDocumentosEstado> findFirmadosConBusqueda(
        @Param("userId") int userId, 
        @Param("estado") int estado,
        @Param("nombre") String nombre, 
        Pageable paging);
    
    // consulta para manejar la busqueda en la tabla de firmados y propietarios 
    @Query(value = "SELECT af.id_archivo_firma as idArchivo, af.nombre_archivo as nombreArchivo, '' as codigoTransaccion, " +
            "af.cantidad_firmas as firmasRequeridas, af.cantidad_firmado as totalFirmas, " +
            "CASE af.tipo_firma WHEN 'MULTIPLE' THEN 'Otros y yo' WHEN 'OTHERS' THEN 'Solo otros' WHEN 'SINGLE' THEN 'Solo yo' ELSE 'Sin firmar' END as tipoFirma, " +
            "af.email_firmantes as firmantes, " +
            "CASE WHEN af.estado = 1 THEN 'Pendiente Firma' ELSE 'Firmado' END as estado, " +
            "af.fecha_registro as fechaRegistro, af.ip as ip, af.hash_archivo as hashArchivo, af.descripcion as descripcion, " +
            "IF(sf.id_archivo_firma IS NOT NULL, 'SI', 'NO') as esSolicitud, " +
            "u.nombre_completo as propietario, " +
            "u.numero_documento as numeroDocumentoPropietario, " +
            "IF(sf.firmado = 1, 'SI', 'NO') as solicitudFirmada, " +
            "sf.fecha_vencimiento as fechaVencimiento " +
            "FROM archivo_firma af " +
            "INNER JOIN usuario u ON af.id_usuario = u.id_usuario " +
            "LEFT JOIN solicitud_firma sf ON af.id_archivo_firma = sf.id_archivo_firma AND sf.firmado = IF(:estado = 1, 0, 1) AND sf.id_usuario != :id " +
            "WHERE (af.id_usuario = :id OR (sf.email_firmante = :email AND sf.id_usuario != :id)) " +
            "AND af.estado = :estado " +
            "AND (sf.fecha_vencimiento > now() OR sf.fecha_vencimiento IS NULL) " +
            "AND (:nombre IS NULL OR af.nombre_archivo LIKE CONCAT('%', :nombre, '%')) " + // filtro de la busqueda
            "ORDER BY af.id_archivo_firma DESC",
    countQuery = "SELECT COUNT(1) " +
            "FROM archivo_firma af " +
            "LEFT JOIN solicitud_firma sf ON af.id_archivo_firma = sf.id_archivo_firma AND sf.firmado = IF(:estado = 1, 0, 1) AND sf.id_usuario != :id " +
            "WHERE (af.id_usuario = :id OR (sf.email_firmante = :email AND sf.id_usuario != :id)) " +
            "AND af.estado = :estado " +
            "AND (sf.fecha_vencimiento > now() OR sf.fecha_vencimiento IS NULL) " +
            "AND (:nombre IS NULL OR af.nombre_archivo LIKE CONCAT('%', :nombre, '%'))", 
    nativeQuery = true)
    public Page<IDocumentosEstado> findDocumentosUnificadosConBusqueda(
        @Param("id") int id,
        @Param("estado") int estado,
        @Param("email") String email,
        @Param("nombre") String nombre, 
        Pageable paging);
    
    /**
     * Busca archivos firmados recientemente por un usuario
     */
    @Query("SELECT af FROM ArchivoFirma af " +
           "JOIN FirmaArchivoUsuario fau ON af.idArchivoFirma = fau.idArchivoFirma " +
           "WHERE fau.idUsuario = :idUsuario " +
           "AND af.estado = 2 " +
           "AND fau.fechaRegistro >= :fechaLimite " +
           "ORDER BY fau.fechaRegistro DESC")
    List<ArchivoFirma> findRecentlySignedByUser(@Param("idUsuario") Long idUsuario, 
                                               @Param("fechaLimite") Date fechaLimite);
}
