package se.firme.ms.service;

import co.venko.ms.models.entity.AdmUsuario;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import se.firme.commons.firmese.dto.ApiResponse;
import se.firme.commons.firmese.dto.ArchivoFirmaResponseDTO;
import se.firme.commons.firmese.dto.SolicitudFirmaDTO;
import se.firme.commons.firmese.service.GeneratePDF;
import se.firme.commons.firmese.util.Parameters;
import se.firme.ms.datos.models.dao.IArchivoFirmaDao;
import se.firme.ms.datos.models.entity.ArchivoFirma;
import se.firme.ms.exception.PreviewException;
import se.firme.ms.preview.web.client.TokenMSClient;
import se.firme.ms.preview.web.client.UsuarioVMSClient;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Logger;

@Service
public class PreviewService {
    private final IArchivoFirmaDao archivoFirmaDao;
    private final Environment env;
    private final UsuarioVMSClient usuarioVMSClient;
    private final TokenMSClient tokenMSClient;

    private static Logger logger = Logger.getLogger(PreviewService.class.getName());

    @Autowired
    public PreviewService(IArchivoFirmaDao archivoFirmaDao, Environment env, UsuarioVMSClient usuarioVMSClient, TokenMSClient tokenMSClient) {
        this.archivoFirmaDao = archivoFirmaDao;
        this.env = env;
        this.usuarioVMSClient = usuarioVMSClient;
        this.tokenMSClient = tokenMSClient;
    }

    private String getRutaArchivos(boolean temporal) {
        return env.getProperty("routes.custom.file") + File.separator + (temporal ? ("tmp" + File.separator) : "");
    }

    public byte[] getPreview(long fileId, int userId, String token) throws PreviewException {
        try {
            logger.info("Obteniendo vista previa del archivo " + fileId + " para el usuario " + userId);

            ArchivoFirma archivoFirma = archivoFirmaDao.findById(fileId).orElseThrow(() -> new PreviewException("Archivo no encontrado", 404));

            if (userId == -1 && token.isEmpty()) {
                throw new PreviewException("No tiene permisos para ver este archivo", 403);
            }

            if (!token.isEmpty()) {
                logger.info("Verificando token: " + token);
                ApiResponse apiResponse = tokenMSClient.verificarTokenFirmante(token);
                ObjectMapper objectMapper = new ObjectMapper();

                SolicitudFirmaDTO solicitudFirmaDTO = objectMapper.convertValue(apiResponse.getData(), SolicitudFirmaDTO.class);
                List<ArchivoFirmaResponseDTO> archivosFirma = solicitudFirmaDTO.getArchivos();
                List<Long> requestFileIds = new ArrayList<>();

                for (ArchivoFirmaResponseDTO archivoFirmaResponseDTO : archivosFirma) {
                    requestFileIds.add(archivoFirmaResponseDTO.getIdArchivo());
                }

                if (requestFileIds.contains(fileId)) {
                    logger.info("El archivo " + fileId + " se encuentra en la solicitud de firma");
                } else {
                    throw new PreviewException("No tiene permisos para ver este archivo", 403);
                }
            } else {
                logger.info("Verificando permisos del usuario " + userId);
                String signersEmailsString = archivoFirma.getEmailFirmantes();

                String[] signerEmails = {};

                if (signersEmailsString != null) {
                    signerEmails = signersEmailsString.split(",");
                }

                List<Integer> signerIds = new ArrayList<>();

                for (String email : signerEmails) {
                    AdmUsuario usuario = usuarioVMSClient.findById(email);

                    if (usuario != null) {
                        signerIds.add(Integer.parseInt(usuario.getIdUsuarioEnCliente()));
                    }
                }
                if (archivoFirma.getIdUsuario() != userId && !signerIds.contains(userId)) {
                    throw new PreviewException("No tiene permisos para ver este archivo", 403);
                }
            }
            boolean isPending = archivoFirma.getEstado() == Parameters.string.ESTADO_ARCHIVO_PENDIENTE_FIRMA;

            String relativePath = isPending ? archivoFirma.getRutaRelativaArchivo() : archivoFirma.getRutaArchivoFirmado();
            String fileName = isPending ? archivoFirma.getNombreArchivo() : "SIGNED_" + archivoFirma.getNombreArchivo();
            String path = getRutaArchivos(isPending) + relativePath + fileName;

            return GeneratePDF.generatePdfPreview(path);
        } catch (Exception e) {
            logger.severe(e.getMessage());

            if (e instanceof PreviewException) {
                throw (PreviewException) e;
            }

            throw new PreviewException("Error al obtener la vista previa", 500);
        }
    }
}
