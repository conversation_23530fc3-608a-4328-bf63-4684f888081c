/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.models.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import se.firme.commons.exception.FirmaException;
import se.firme.commons.firmese.dto.PaqueteServicioDTO;
import se.firme.ms.datos.models.dao.IPaqueteServicioDAO;
import se.firme.ms.datos.models.entity.PaqueteServicio;
import se.firme.ms.models.service.helper.PaqueteServicioHelper;
import se.firme.ms.models.service.interfaz.IPaqueteServicioService;

/**
 * @document PaqueteServicioServiceImpl
 * <AUTHOR>
 * @fecha lunes, enero 18 de 2021, 10:14:44 AM
 */
@Service
public class PaqueteServicioServiceImpl implements IPaqueteServicioService {

    @Autowired
    private IPaqueteServicioDAO iPaqueteServicioDAO;
    
    @Override
    public boolean registrarPaquete(PaqueteServicioDTO paqueteServicioDTO) throws FirmaException {
        try {
            PaqueteServicio paqueteServicio = PaqueteServicioHelper.convert(paqueteServicioDTO);
            iPaqueteServicioDAO.save(paqueteServicio);
            return true;
        } catch (Exception e) {
            throw new FirmaException(e.getMessage());
        }
    }

}
