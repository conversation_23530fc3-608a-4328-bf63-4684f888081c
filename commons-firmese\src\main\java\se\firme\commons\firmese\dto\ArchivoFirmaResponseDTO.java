/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.commons.firmese.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ArchivoFirmaResponseDTO implements Serializable {

    private static final long serialVersionUID = -3696775316625105916L;

    private long idArchivo;
    private String nombreArchivo;
    private String hashArchivo;
    private String ipOrigen;
    private int cantidadFirmas;
    private int cantidadFirmado;
    private Date fechaRegistro;
    private String fechaRegistroStr;
    private int cantidadConsultas;
    private String ip;
    private String observacion;
    private String resultadoFirma;
    private String estado;
    private List<FirmaArchivoUsuarioResponseDTO> firmas;
    private String b64;
    private String emailFirmantes;
    private String tipoFirma;
    private PropietarioDTO propietario;

    private String descripcion;

    public ArchivoFirmaResponseDTO() {
    }

    public String getNombreArchivo() {
        return nombreArchivo;
    }

    public void setNombreArchivo(String nombreArchivo) {
        this.nombreArchivo = nombreArchivo;
    }

    public String getHashArchivo() {
        return hashArchivo;
    }

    public void setHashArchivo(String hashArchivo) {
        this.hashArchivo = hashArchivo;
    }

    public int getCantidadFirmas() {
        return cantidadFirmas;
    }

    public void setCantidadFirmas(int cantidadFirmas) {
        this.cantidadFirmas = cantidadFirmas;
    }

    public int getCantidadFirmado() {
        return cantidadFirmado;
    }

    public void setCantidadFirmado(int cantidadFirmado) {
        this.cantidadFirmado = cantidadFirmado;
    }

    public Date getFechaRegistro() {
        return fechaRegistro;
    }

    public void setFechaRegistro(Date fechaRegistro) {
        this.fechaRegistro = fechaRegistro;
    }

    public int getCantidadConsultas() {
        return cantidadConsultas;
    }

    public void setCantidadConsultas(int cantidadConsultas) {
        this.cantidadConsultas = cantidadConsultas;
    }

    public List<FirmaArchivoUsuarioResponseDTO> getFirmas() {
        return firmas;
    }

    public void setFirmas(List<FirmaArchivoUsuarioResponseDTO> firmas) {
        this.firmas = firmas;
    }

    public String getIpOrigen() {
        return ipOrigen;
    }

    public void setIpOrigen(String ipOrigen) {
        this.ipOrigen = ipOrigen;
    }

    public String getFechaRegistroStr() {
        return fechaRegistroStr;
    }

    public void setFechaRegistroStr(String fechaRegistroStr) {
        this.fechaRegistroStr = fechaRegistroStr;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public long getIdArchivo() {
        return idArchivo;
    }

    public void setIdArchivo(long idArchivo) {
        this.idArchivo = idArchivo;
    }

    public String getObservacion() {
        return observacion;
    }

    public void setObservacion(String observacion) {
        this.observacion = observacion;
    }

    public String getB64() {
        return b64;
    }

    public void setB64(String b64) {
        this.b64 = b64;
    }


    public String getResultadoFirma() {
        return resultadoFirma;
    }

    public void setResultadoFirma(String resultadoFirma) {
        this.resultadoFirma = resultadoFirma;
    }

    public String getEstado() {
        return estado;
    }

    public void setEstado(String estado) {
        this.estado = estado;
    }

    public String getEmailFirmantes() {
        return emailFirmantes;
    }

    public void setEmailFirmantes(String emailFirmantes) {
        this.emailFirmantes = emailFirmantes;
    }

    public String getTipoFirma() {
        return tipoFirma;
    }

    public void setTipoFirma(String tipoFirma) {
        this.tipoFirma = tipoFirma;
    }

    public PropietarioDTO getPropietario() {
        return propietario;
    }

    public void setPropietario(PropietarioDTO propietario) {
        this.propietario = propietario;
    }

    public String getDescripcion() {
        return descripcion;
    }

    public void setDescripcion(String descripcion) {
        this.descripcion = descripcion;
    }
}
