/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.models.service.helper;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import se.firme.commons.firmese.dto.ArchivoFirmaResponseDTO;
import se.firme.commons.firmese.dto.FirmaArchivoUsuarioResponseDTO;
import se.firme.commons.firmese.dto.PropietarioDTO;
import se.firme.commons.firmese.dto.UsuarioResponseDTO;
import se.firme.commons.firmese.util.Utilities;
import se.firme.ms.datos.models.entity.ArchivoFirma;
import se.firme.ms.datos.models.entity.FirmaArchivoUsuario;

/**
 * <AUTHOR>
 */
public class ArchivoFirmaHelper {

    public static ArchivoFirmaResponseDTO getArchivoFirmaHandle(ArchivoFirma archivoFirma) throws ParseException {
        ArchivoFirmaResponseDTO rta = new ArchivoFirmaResponseDTO();
        rta.setCantidadConsultas(archivoFirma.getCantidadConsultas());
        rta.setCantidadFirmado(archivoFirma.getCantidadFirmado());
        rta.setCantidadFirmas(archivoFirma.getCantidadFirmas());
        rta.setFechaRegistro(archivoFirma.getFechaRegistro());
        rta.setHashArchivo(archivoFirma.getHashArchivo());
        rta.setNombreArchivo(archivoFirma.getNombreArchivo());
        rta.setIp(archivoFirma.getIp());
        rta.setFirmas(new ArrayList<>());
        rta.setIdArchivo(archivoFirma.getIdArchivoFirma());

        //rta.setFechaRegistroStr(Utilities.getFechaDateAFechaTexto(archivoFirma.getFechaRegistro(), "EEEE, dd 'de' MMMM 'de' yyyy HH:mm"));
        rta.setFechaRegistroStr(Utilities.getFechaDateAFechaTexto(archivoFirma.getFechaRegistro(), "yyyy-MM-dd HH:mm"));
        rta.setResultadoFirma(archivoFirma.getResultadoFirma());
        rta.setEmailFirmantes(archivoFirma.getEmailFirmantes());
        rta.setTipoFirma(archivoFirma.getTipoFirma());
        switch (archivoFirma.getEstado()) {
            case 1:
                rta.setEstado("Pendiente de firma");
                break;
            case 2:
                rta.setEstado("Firmado");
                break;
            default:
                rta.setEstado("...");
        }

        if (archivoFirma.getDescripcion() != null && !archivoFirma.getDescripcion().trim().isEmpty()) {
            rta.setDescripcion(archivoFirma.getDescripcion());
        }

        for (FirmaArchivoUsuario it : archivoFirma.getFirmaArchivoUsuarioList()) {
            FirmaArchivoUsuarioResponseDTO firma = new FirmaArchivoUsuarioResponseDTO();
            firma.setFechaFirma(it.getFechaRegistro());
            firma.setSubioArchivo(it.getSubio());
            firma.setHashfirma(it.getHashArchivo());
            //firma.setFechaFirmaStr(Utilities.getFechaDateAFechaTexto(it.getFechaRegistro(), "EEEE, dd 'de' MMMM 'de' yyyy HH:mm"));
            firma.setFechaFirmaStr(Utilities.getFechaDateAFechaTexto(it.getFechaRegistro(), "yyyy-MM-dd HH:mm"));
            firma.setIpFirma(it.getIp());
            firma.setAgenteNavegador(it.getAgenteNavegador());
            UsuarioResponseDTO usuario = new UsuarioResponseDTO();
            usuario.setCorreoElectronico(it.getIdUsuario().getCorreoElectronico());
            usuario.setNombreCompleto(it.getIdUsuario().getNombreCompleto());
            usuario.setNombreTipoDocumento(it.getIdUsuario().getIdTipoDocumento().getNombreTipoDocumento());
            usuario.setNumeroDocumento(it.getIdUsuario().getNumeroDocumento());

            firma.setFirma(usuario);
            rta.getFirmas().add(firma);
        }

        return rta;
    }

    public static List<ArchivoFirmaResponseDTO> convertList(List<ArchivoFirma> lista) {
        List<ArchivoFirmaResponseDTO> archivos = new ArrayList<>();
        lista.forEach(archivoFirma -> {
            try {
                archivos.add(getArchivoFirmaHandle(archivoFirma));
            } catch (ParseException ex) {
                Logger.getLogger(ArchivoFirmaHelper.class.getName()).log(Level.SEVERE, null, ex);
            }
        });
        return archivos;
    }
}
