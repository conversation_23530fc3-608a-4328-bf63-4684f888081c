package se.firme.ms.models.service;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import se.firme.commons.exception.FirmaException;
import se.firme.ms.datos.models.dao.UsuarioTemporalDAO;
import se.firme.ms.datos.models.entity.UsuarioTemporal;

@Component
public class UsuarioTemporalService {
	@Autowired
	private UsuarioTemporalDAO dao;

	public void crearRegistro(UsuarioTemporal temporal) throws FirmaException {
		try {
			dao.save(temporal);
		} catch (Exception e) {
			throw new FirmaException("No se pudo crear el registro temporal");
		}

	}

	public UsuarioTemporal consultar(String tipoDocumento, String numeroDocumento) throws FirmaException {
		try {
			Optional<UsuarioTemporal> optional=dao.findByNumeroDocumento(tipoDocumento, numeroDocumento);
			if(optional.isPresent()) {
				return optional.get();
			}
			throw new FirmaException("No se encontro registro temporal de firmante, debes registrarlo previamente en la aplicación móvil como interviniente en la firma del documento");
		} catch (Exception e) {
			throw new FirmaException(e.getMessage());
		}
	}

}
