package se.firme.cli.utils;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@SpringBootApplication
@EntityScan({"se.firme.ms.datos.models.entity"})
@EnableJpaRepositories("se.firme.ms.datos.models.dao")
public class CliUtilsApplication {
    public static void main(String[] args) {
        new SpringApplication(CliUtilsApplication.class).run(args);
    }
}
