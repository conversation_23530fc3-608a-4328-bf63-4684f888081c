/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.models.service.interfaz;

import se.firme.commons.exception.FirmaException;
import se.firme.commons.firmese.dto.PaqueteServicioDTO;

/**
 * @document IPaqueteServicioService
 * <AUTHOR>
 * @fecha lunes, enero 18 de 2021, 10:11:42 AM
 */
public interface IPaqueteServicioService {

    public boolean registrarPaquete(PaqueteServicioDTO paqueteServicioDTO) throws FirmaException;
}
