package se.firme.ms.datos.models.dao;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import se.firme.ms.datos.models.entity.PlantillaDocumento;
import java.util.List;

public interface IPlantillaDocumentoRepository extends JpaRepository<PlantillaDocumento, Long> {
    
    @Query("SELECT p FROM PlantillaDocumento p WHERE p.activa = true ORDER BY p.nombrePlantilla")
    List<PlantillaDocumento> findAllActivasOrderByNombre();
    
    @Query("SELECT p FROM PlantillaDocumento p WHERE p.idPlantilla = :id AND p.activa = true")
    PlantillaDocumento findByIdAndActiva(@Param("id") Long id);
    
    @Query("SELECT p FROM PlantillaDocumento p WHERE p.hashArchivo = :hash")
    PlantillaDocumento findByHashArchivo(@Param("hash") String hash);
    
    /**
     * Busca plantilla por nombre exacto y estado activo
     */
    PlantillaDocumento findByNombrePlantillaAndActiva(String nombrePlantilla, boolean activa);
}
