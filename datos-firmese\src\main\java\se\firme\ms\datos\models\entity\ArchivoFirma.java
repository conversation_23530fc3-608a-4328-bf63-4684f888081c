/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.datos.models.entity;

import com.fasterxml.jackson.annotation.JsonManagedReference;

import java.io.File;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;

import org.hibernate.annotations.CreationTimestamp;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "archivo_firma")
@XmlRootElement
@NamedQueries({
        @NamedQuery(name = "ArchivoFirma.findAll", query = "SELECT a FROM ArchivoFirma a")
        , @NamedQuery(name = "ArchivoFirma.findByIdArchivoFirma", query = "SELECT a FROM ArchivoFirma a WHERE a.idArchivoFirma = :idArchivoFirma")
        , @NamedQuery(name = "ArchivoFirma.findByNombreArchivo", query = "SELECT a FROM ArchivoFirma a WHERE a.nombreArchivo = :nombreArchivo")
        , @NamedQuery(name = "ArchivoFirma.findByHashArchivo", query = "SELECT a FROM ArchivoFirma a WHERE a.hashArchivo = :hashArchivo")
        , @NamedQuery(name = "ArchivoFirma.findByCantidadFirmas", query = "SELECT a FROM ArchivoFirma a WHERE a.cantidadFirmas = :cantidadFirmas")
        , @NamedQuery(name = "ArchivoFirma.findByFechaRegistro", query = "SELECT a FROM ArchivoFirma a WHERE a.fechaRegistro = :fechaRegistro")})
public class ArchivoFirma implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id_archivo_firma")
    private long idArchivoFirma;
    @Basic(optional = false)
    @Column(name = "nombre_archivo")
    private String nombreArchivo;
    @Basic(optional = false)
    @Column(name = "hash_archivo")
    private String hashArchivo;
    @Basic(optional = false)
    @Column(name = "cantidad_firmas")
    private int cantidadFirmas;
    @Basic(optional = false)
    @Column(name = "cantidad_firmado")
    private int cantidadFirmado;
    @Basic(optional = false)
    @Column(name = "fecha_registro")
    @Temporal(TemporalType.TIMESTAMP)
    @CreationTimestamp
    private Date fechaRegistro;
    @Basic(optional = false)
    @Column(name = "cantidad_consultas")
    private int cantidadConsultas;
    @Basic(optional = false)
    @Column(name = "estado")
    private int estado;
    @Basic(optional = false)
    @Column(name = "ip")
    private String ip;
    @Basic(optional = false)
    @Column(name = "id_usuario")
    private long idUsuario;
    @Basic(optional = false)
    @Column(name = "ruta_relativa_archivo")
    private String rutaRelativaArchivo;
    @Transient
    private String resultadoFirma;
    @Column(name = "email_firmantes")
    private String emailFirmantes;
    @Column(name = "tipo_firma")
    private String tipoFirma;

    @Column(name = "descripcion")
    private String descripcion;

    @JsonManagedReference
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "idArchivoFirma", fetch = FetchType.EAGER)
    private List<FirmaArchivoUsuario> firmaArchivoUsuarioList;

    public ArchivoFirma() {
    }

    public ArchivoFirma(Long idArchivoFirma) {
        this.idArchivoFirma = idArchivoFirma;
    }

    public ArchivoFirma(Long idArchivoFirma, String nombreArchivo, String hashArchivo, int cantidadFirmas, Date fechaRegistro) {
        this.idArchivoFirma = idArchivoFirma;
        this.nombreArchivo = nombreArchivo;
        this.hashArchivo = hashArchivo;
        this.cantidadFirmas = cantidadFirmas;
        this.fechaRegistro = fechaRegistro;
    }

    public long getIdArchivoFirma() {
        return idArchivoFirma;
    }

    public void setIdArchivoFirma(long idArchivoFirma) {
        this.idArchivoFirma = idArchivoFirma;
    }

    public String getNombreArchivo() {
        return nombreArchivo;
    }

    public void setNombreArchivo(String nombreArchivo) {
        this.nombreArchivo = nombreArchivo;
    }

    public String getHashArchivo() {
        return hashArchivo;
    }

    public void setHashArchivo(String hashArchivo) {
        this.hashArchivo = hashArchivo;
    }

    public int getCantidadFirmas() {
        return cantidadFirmas;
    }

    public void setCantidadFirmas(int cantidadFirmas) {
        this.cantidadFirmas = cantidadFirmas;
    }

    public Date getFechaRegistro() {
        return fechaRegistro;
    }

    public void setFechaRegistro(Date fechaRegistro) {
        this.fechaRegistro = fechaRegistro;
    }

    @XmlTransient
    public List<FirmaArchivoUsuario> getFirmaArchivoUsuarioList() {
        if (firmaArchivoUsuarioList == null) {
            firmaArchivoUsuarioList = new ArrayList<>();
        }
        return firmaArchivoUsuarioList;
    }

    public void setFirmaArchivoUsuarioList(List<FirmaArchivoUsuario> firmaArchivoUsuarioList) {
        this.firmaArchivoUsuarioList = firmaArchivoUsuarioList;
    }


    @Override
    public String toString() {
        return "ArchivoFirma [idArchivoFirma=" + idArchivoFirma + ", nombreArchivo=" + nombreArchivo + ", hashArchivo="
                + hashArchivo + ", cantidadFirmas=" + cantidadFirmas + ", cantidadFirmado=" + cantidadFirmado
                + ", fechaRegistro=" + fechaRegistro + ", cantidadConsultas=" + cantidadConsultas + ", estado=" + estado
                + ", ip=" + ip + ", idUsuario=" + idUsuario + ", rutaRelativaArchivo=" + rutaRelativaArchivo
                + ", resultadoFirma=" + resultadoFirma + ", emailFirmantes=" + emailFirmantes + ", tipoFirma="
                + tipoFirma + ", firmaArchivoUsuarioList=" + firmaArchivoUsuarioList + "]";
    }

    public int getCantidadFirmado() {
        return cantidadFirmado;
    }

    public void setCantidadFirmado(int cantidadFirmado) {
        this.cantidadFirmado = cantidadFirmado;
    }

    public boolean isComplete() {
        return this.cantidadFirmado >= this.cantidadFirmas;
    }

    public int getCantidadConsultas() {
        return cantidadConsultas;
    }

    public void setCantidadConsultas(int cantidadConsultas) {
        this.cantidadConsultas = cantidadConsultas;
    }

    public int getEstado() {
        return estado;
    }

    public void setEstado(int estado) {
        this.estado = estado;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public long getIdUsuario() {
        return idUsuario;
    }

    public void setIdUsuario(long idUsuario) {
        this.idUsuario = idUsuario;
    }

    public String getRutaRelativaArchivo() {
        return rutaRelativaArchivo;
    }

    public void setRutaRelativaArchivo(String rutaRelativaArchivo) {
        this.rutaRelativaArchivo = rutaRelativaArchivo;
    }

    public String getResultadoFirma() {
        return resultadoFirma;
    }

    public void setResultadoFirma(String resultadoFirma) {
        this.resultadoFirma = resultadoFirma;
    }

    public String getEmailFirmantes() {
        return emailFirmantes;
    }

    public void setEmailFirmantes(String emailFirmantes) {
        if (emailFirmantes != null) {
            emailFirmantes = emailFirmantes.toLowerCase();
        }
        this.emailFirmantes = emailFirmantes;
    }

    public String getPath() {
        return this.rutaRelativaArchivo + File.separator + this.nombreArchivo;
    }

    public String getTipoFirma() {
        return tipoFirma;
    }

    public void setTipoFirma(String tipoFirma) {
        this.tipoFirma = tipoFirma;
    }

    public String getRutaArchivoFirmado() {
        try {
            return this.rutaRelativaArchivo.replaceAll("original[/|\\\\]", "");
        } catch (Exception e) {
            return this.rutaRelativaArchivo;
        }
    }

    public String getDescripcion() {
        return descripcion;
    }

    public void setDescripcion(String descripcion) {
        this.descripcion = descripcion;
    }
}
