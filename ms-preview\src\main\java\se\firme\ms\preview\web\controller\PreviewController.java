package se.firme.ms.preview.web.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import se.firme.ms.exception.PreviewException;
import se.firme.ms.service.PreviewService;

import javax.servlet.http.HttpServletRequest;

@RestController
@RefreshScope
@RequestMapping("/")
public class PreviewController {

    private final PreviewService previewService;

    @Autowired
    public PreviewController(PreviewService previewService) {
        this.previewService = previewService;
    }

    @GetMapping(value = "/{fileId}", produces = MediaType.IMAGE_PNG_VALUE)
    @ResponseBody
    public ResponseEntity<byte[]> getPreview(@PathVariable int fileId, @RequestParam(name = "token", defaultValue = "") String token, HttpServletRequest request) throws PreviewException {
        String xUserId = request.getHeader("X-USER-ID");
        int userId = -1;

        if (xUserId != null) {
            userId = Integer.parseInt(xUserId);
        }

        return ResponseEntity.ok(previewService.getPreview(fileId, userId, token));
    }

    @ExceptionHandler(PreviewException.class)
    public ResponseEntity<String> handlePreviewException(PreviewException ex) {
        return ResponseEntity.status(ex.getCode()).body(ex.getMessage());
    }
}
