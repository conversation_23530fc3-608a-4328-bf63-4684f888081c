package se.firme.ms;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@EnableFeignClients
@EnableEurekaClient
@SpringBootApplication
@EntityScan({"se.firme.ms.datos.models.entity"})
@EnableJpaRepositories("se.firme.ms.datos.models.dao")
public class MainPreview {
    public static void main(String[] args) {
        new SpringApplication(MainPreview.class).run(args);
    }
}