package se.firme.ms.datos.models.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "sku")
public class Sku implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @Basic(optional = false)
    @Column(name = "id_sku")
    private Integer idSku;
    @Basic(optional = false)
    @Column(name = "fecha_registro")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaRegistro;
    
    @Column(name = "descripcion")
    private String descripcion;
    @Column(name = "estado")
    private boolean estado;
    @Column(name = "tipo_servicio")
    private String tipoServicio;
    @Column(name = "tipo_validacion")
    private String tipoValidacion;
    @Column(name = "vigencia_meses")
    private int vigenciaMeses;
    @Column(name = "cantidad_firmas")
    private int cantidadFirmas;
    @Column(name = "cantidad_otros_firmantes")
    private int cantidadOtrosFirmantes;
	public Integer getIdSku() {
		return idSku;
	}
	public void setIdSku(Integer idSku) {
		this.idSku = idSku;
	}
	public Date getFechaRegistro() {
		return fechaRegistro;
	}
	public void setFechaRegistro(Date fechaRegistro) {
		this.fechaRegistro = fechaRegistro;
	}
	public String getDescripcion() {
		return descripcion;
	}
	public void setDescripcion(String descripcion) {
		this.descripcion = descripcion;
	}
	public boolean isEstado() {
		return estado;
	}
	public void setEstado(boolean estado) {
		this.estado = estado;
	}
	public String getTipoServicio() {
		return tipoServicio;
	}
	public void setTipoServicio(String tipoServicio) {
		this.tipoServicio = tipoServicio;
	}
	public String getTipoValidacion() {
		return tipoValidacion;
	}
	public void setTipoValidacion(String tipoValidacion) {
		this.tipoValidacion = tipoValidacion;
	}
	public int getVigenciaMeses() {
		return vigenciaMeses;
	}
	public void setVigenciaMeses(int vigenciaMeses) {
		this.vigenciaMeses = vigenciaMeses;
	}
	public int getCantidadFirmas() {
		return cantidadFirmas;
	}
	public void setCantidadFirmas(int cantidadFirmas) {
		this.cantidadFirmas = cantidadFirmas;
	}
	public int getCantidadOtrosFirmantes() {
		return cantidadOtrosFirmantes;
	}
	public void setCantidadOtrosFirmantes(int cantidadOtrosFirmantes) {
		this.cantidadOtrosFirmantes = cantidadOtrosFirmantes;
	}
	@Override
	public String toString() {
		return "Sku [idSku=" + idSku + ", fechaRegistro=" + fechaRegistro + ", descripcion=" + descripcion + ", estado="
				+ estado + ", tipoServicio=" + tipoServicio + ", tipoValidacion=" + tipoValidacion + ", vigenciaMeses="
				+ vigenciaMeses + ", cantidadFirmas=" + cantidadFirmas + ", cantidadOtrosFirmantes="
				+ cantidadOtrosFirmantes + "]";
	}
    
    

}
