/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.commons.firmese.service;

import java.util.List;
import java.util.Properties;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.activation.DataHandler;
import javax.activation.FileDataSource;
import javax.mail.Address;
import javax.mail.BodyPart;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;

import com.sun.mail.handlers.message_rfc822;

import se.firme.commons.firmese.util.Parameters;

/**
 * @document EmailService
 * <AUTHOR>
 * @fecha viernes, agosto 21 de 2020, 03:50:41 PM
 */
public class EmailService {

    private static Logger logger = Logger.getLogger(EmailService.class.getName());
    private static Properties props;
    private static String remitente;
    private static String claveRemitente;

    static {
        props = new Properties();
        props.put("mail.smtp.port", "587");
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.starttls.enable", "true");
        props.put("mail.smtp.ssl.trust", "smtp.mailgun.org");

        remitente = "<EMAIL>";
        claveRemitente = "alertas_konivin2017$";
    }

    public static boolean send(String email, String subject, String content) throws Exception {
        boolean emailWasSent = true;
        try {
            Session session = Session.getDefaultInstance(props);
            Message message = new MimeMessage(session);
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(email));
            message.setSubject(subject);
            message.setFrom(new InternetAddress("<EMAIL>", "Fírme.se"));
            message.setContent(addBanners(content), "text/html;charset=UTF-8");
            Transport transport = session.getTransport("smtp");
            transport.connect("smtp.mailgun.org", "<EMAIL>", Parameters.string.API_KEY_MAILGUN);
            transport.sendMessage(message, message.getAllRecipients());
            transport.close();
            logger.log(Level.INFO, "Correo enviado a la cuenta {0}", email);
        } catch (MessagingException e) {
        	logger.log(Level.SEVERE, e.getMessage());
            emailWasSent = false;

        }
        return emailWasSent;
    }

    public static void enviarCorreo(String destinatario, String asunto, String mensaje, /*String rutaAdjunto, String nombreAdjunto,*/ List<String[]> adjuntos) throws Exception {

        // Preparamos la sesion
        Session session = Session.getDefaultInstance(props);
        //Recoger los datos
        String destinos[] = destinatario.split(",");
        MimeMessage message = new MimeMessage(session);
       // message.setFrom(new InternetAddress(remitente));
        Address[] receptores = new Address[destinos.length];
        int j = 0;
        while (j < destinos.length) {
        	logger.info("Destinos=" + destinos[j]);
            receptores[j] = new InternetAddress(destinos[j]);
            logger.info("Receptores=" + receptores[j]);
            j++;
        }

        //esta es la nueva parte con adjundo
        //BodyPart texto = new MimeBodyPart();
       // texto.setText(mensaje);

        MimeMultipart multiParte = new MimeMultipart();
        BodyPart htmlBodyPart = new MimeBodyPart();
        htmlBodyPart.setContent(addBanners(mensaje), "text/html");
        multiParte.addBodyPart(htmlBodyPart);

        if (adjuntos != null) {
            for (String[] it : adjuntos) {
            	logger.info("El archivo a enviar es: " + it[0] + it[1]);
                BodyPart adjunto = new MimeBodyPart();
                adjunto.setDataHandler(new DataHandler(new FileDataSource(it[0] + it[1])));
                adjunto.setFileName(it[1]);
                multiParte.addBodyPart(adjunto);
            }
        }
      
        message.setSubject(asunto);
        message.addRecipients(Message.RecipientType.TO, receptores);
        message.setContent(multiParte);
        message.setFrom(new InternetAddress("<EMAIL>", "Fírme.se"));

        Transport t = session.getTransport("smtp");
        t.connect("smtp.mailgun.org", "<EMAIL>", Parameters.string.API_KEY_MAILGUN);
        //t.connect(str_De, str_PwRemitente);
        t.sendMessage(message, message.getRecipients(Message.RecipientType.TO));

        // Cierre de la conexion.
        t.close();
    }

	private static Object addBanners(String mensaje) {
		StringBuilder builder=new StringBuilder();
		builder.append("<div width='width: 80%;margin: auto'>");
		builder.append("<div style='background-image: url('https://www.firme.se/sites/default/files/images/fondofirmese.png')'>");
		builder.append("<img style='width: 100%;max-width: 400px;height: auto;' src='https://www.firme.se/sites/default/files/images/firmesefondooscuro.png'/>");
		builder.append("</div><hr/>");
		builder.append(mensaje);
		
	
		
		builder.append("</div>");
		return builder.toString();
	}

}
