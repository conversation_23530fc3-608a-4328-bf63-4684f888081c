package se.firme.ms.firma.servicio;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import se.firme.commons.firmese.dto.MensajeDTO;
import se.firme.ms.firma.rest.client.MensajeroClient;

@Component
public class MensajeroService {
	@Autowired
	private MensajeroClient mensajeroClient;

	public boolean notificar(String string, String numeroCelular) {
		try {
			MensajeDTO dto = new MensajeDTO();
			dto.setCelular(numeroCelular);
			dto.setMensaje(string);
			dto.setPais("CO");
			dto.setTipoProveedor("smv");
			mensajeroClient.send(dto);
			return true;
		} catch (Exception e) {
			System.err.println(e.getMessage());
			return false;
		}
	}
}
