/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.ms.validacion.negocio;

import co.venko.ms.models.entity.AdmUsuario;
import java.io.File;
import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Service;
import se.firme.commons.exception.FirmaException;
import se.firme.commons.firmese.dto.ApiResponse;
import se.firme.commons.firmese.dto.ArchivoFirmaResponseDTO;
import se.firme.commons.firmese.dto.FirmaRequestDTO;
import se.firme.commons.firmese.dto.PropietarioDTO;
import se.firme.commons.firmese.dto.RegistroDTO;
import se.firme.commons.firmese.service.EmailService;
import se.firme.commons.firmese.service.EmailTemplateService;
import se.firme.commons.firmese.util.Parameters;
import se.firme.commons.firmese.util.Utilities;
import se.firme.ms.datos.models.entity.ArchivoFirma;
import se.firme.ms.datos.models.entity.FirmaArchivoUsuario;
import se.firme.ms.datos.models.entity.ProcesoFirma;
import se.firme.ms.models.service.TokenServiceImpl;
import se.firme.ms.models.service.UsuarioServiceImpl;
import se.firme.ms.datos.models.entity.Token;
import se.firme.ms.datos.models.entity.Usuario;
import se.firme.ms.models.service.ArchivoFirmaServiceImpl;
import se.firme.ms.models.service.FirmaArchivoUsuarioServiceImpl;
import se.firme.ms.models.service.ProcesoFirmaServiceImpl;
import se.firme.ms.models.service.helper.ArchivoFirmaHelper;
import se.firme.ms.validacion.client.UsuarioFeingClient;
import se.firme.ms.validacion.servicio.MensajeroServicio;

/**
 * @document ValidacionNegocio
 * <AUTHOR> Machado Jaimes
 * @fecha miércoles, agosto 19 de 2020, 12:19:06 PM
 */
@Service
public class ValidacionNegocio {

    @Autowired
    private TokenServiceImpl tokenService;
    @Autowired
    private UsuarioServiceImpl usuarioService;
    @Autowired
    private UsuarioFeingClient usuarioClient;
    @Autowired
    private ArchivoFirmaServiceImpl archivoFirmaService;
    @Autowired
    private FirmaArchivoUsuarioServiceImpl firmaArchivoService;
    @Autowired
    private Environment env;
    @Autowired
    private ProcesoFirmaServiceImpl firmaServiceImpl;
    @Autowired
    private MensajeroServicio mensajeroClient;
    private static final Logger logger = LoggerFactory.getLogger(ValidacionNegocio.class);

    public ApiResponse validaCodigoTransaccion(String codigo) throws FirmaException {
        try {
            Token token = tokenService.consultarCodTransaccion(codigo);
            if (token != null) {
                if (token.getTipo() == Parameters.string.TOKEN_TIPO_REGISTRO_USUARIO) {
                    RegistroDTO registroDTO = new RegistroDTO();
                    registroDTO.setNumeroCelular(Utilities.maskMobileNumber(token.getIdUsuario().getNumeroCelular()));
                    return new ApiResponse.ok().data(registroDTO).build();
                }
                throw new FirmaException("Token no es de tipo registro");
            }
            throw new FirmaException("Token no encontrado");
        } catch (FirmaException | NullPointerException e) {
            throw new FirmaException(e.getMessage());
        }
    }

    public ApiResponse validaCodigoTransaccionFirma(String codigo) throws FirmaException {
        try {
            Token token = tokenService.consultarCodTransaccionFirma(codigo);

            if (token != null) {
                RegistroDTO registroDTO = new RegistroDTO();
                registroDTO.setNumeroCelular(Utilities.maskMobileNumber(token.getIdUsuario().getNumeroCelular()));
                return new ApiResponse.ok().data(registroDTO).build();
            }
            throw new FirmaException("No coincide el código de transacción");
        } catch (FirmaException | NullPointerException e) {
            throw new FirmaException(e.getMessage());
        }
    }

    public ApiResponse validaCodigoSMS(String ctoken, String numero) throws FirmaException {
        try {
            Token dto = tokenService.consultarCodTransaccion(ctoken);
            if (numero.equals(dto.getCodigoSms())) {
                // usuarioService.actualizarPreregistro(dto.getIdUsuario().getIdUsuario());

                return new ApiResponse.ok().build();
            }
            throw new FirmaException("No coincide el código sms");
        } catch (FirmaException | NullPointerException e) {
            throw new FirmaException(e.getMessage());
        }
    }

    public ApiResponse validaNumero(String ctoken, String numero) throws FirmaException {
        try {
            Token dto = tokenService.consultarCodTransaccion(ctoken);
            String digitos = Utilities.getDigits(dto.getIdUsuario().getNumeroCelular());
            if (numero.equals(digitos)) {
                String codSms = Utilities.getNumeroAleatorio(6);// Utilities.generarToken(6);
                // codSms = Utilities.replace(codSms);
                tokenService.addCodeSms(codSms, ctoken);
                StringBuilder builder = new StringBuilder();
//                builder.append("<#> ");
                builder.append("Firme.se ");
                builder.append("Tu codigo es: ");
                builder.append(codSms);
                builder.append("\n");
//                builder.append(getSignatureApp());
                // Utils.sendMessage(builder.toString(),
                // dto.getIdUsuario().getNumeroCelular(),Utils.CLIENTE_VONAGE);

                mensajeroClient.notificar(builder.toString(), dto.getIdUsuario().getNumeroCelular(), "smv");
                return new ApiResponse.ok().build();
            }
            throw new FirmaException("No coincide los dígitos asignados del número de celular");
        } catch (FirmaException | NullPointerException | IOException e) {
            throw new FirmaException(e.getMessage());
        }
    }

    public ApiResponse guardarUsuarioAdm(AdmUsuario usuario) throws FirmaException {
        try {
            return new ApiResponse.ok().data(usuarioClient.guardarUsuarioAdm(usuario)).build();
        } catch (Exception e) {
            throw new FirmaException(e.getMessage());
        }

    }

    public ArchivoFirmaResponseDTO verificarFirmaArchivo(FirmaRequestDTO datosFirma, byte[] bytes)
            throws FirmaException {
        try {
            ArchivoFirma archivoFirmado;
            String rutaArchivo = Utilities.procesarArchivo(datosFirma, bytes, getRutaArchivos());

            // obtener el hash
            String hashArchivo = Utilities.generarHash(rutaArchivo);

            List<ArchivoFirma> archivoFirmadoList = archivoFirmaService.getArchivoFirmadoByHash(hashArchivo);
            if (archivoFirmadoList != null && !archivoFirmadoList.isEmpty()) {
                archivoFirmado = archivoFirmadoList.get(0);
                archivoFirmaService.actualizarCantidadConsultas(archivoFirmado.getIdArchivoFirma());
                if (archivoFirmado.getEstado() == 1) {
                    throw new FirmaException(
                            "Documento cargado no tiene una firma válida, Archivo en proceso de firmas");
                } else {
                    ArchivoFirmaResponseDTO obj = ArchivoFirmaHelper.getArchivoFirmaHandle(archivoFirmadoList.get(0));
                    PropietarioDTO dto = new PropietarioDTO();
                    Usuario usuario = usuarioService.findById(archivoFirmado.getIdUsuario());
                    dto.setNombreCompleto(usuario.getNombreCompleto());
                    dto.setNumeroDocumento(usuario.getNumeroDocumento());
                    dto.setTipoDocumento(usuario.getIdTipoDocumento().getNombreTipoDocumento() + "");
                    obj.setPropietario(dto);
                    return obj;
                }
            }
            throw new FirmaException("Documento cargado no tiene una firma válida");
        } catch (Exception e) {
            throw new FirmaException(e.getMessage());
        }
    }

    private String getRutaArchivos() {
        return env.getProperty("routes.custom.file") + File.separator;
    }

    public Resource loadFileAsResource(String fileID) throws FirmaException {
        try {
            List<FirmaArchivoUsuario> firmados = firmaArchivoService.findByIdArchivoFirma(Integer.parseInt(fileID));
            if (firmados != null && !firmados.isEmpty()) {
                FirmaArchivoUsuario archivoUsuario = firmados.get(0);

                Path fileStorageLocation = Paths.get(env.getProperty("routes.custom.file") + File.separator + ""
                        + archivoUsuario.getRutaRelativaArchivo()).toAbsolutePath().normalize();
                Path filePath = fileStorageLocation.resolve(archivoUsuario.getNombreArchivoFirma()).normalize();
                Resource resource = new UrlResource(filePath.toUri());
                if (resource.exists()) {
                    return resource;
                } else {
                    throw new FirmaException("File not found in storage" + fileID);
                }
            }
            throw new FirmaException("File not found in db" + fileID);
        } catch (MalformedURLException ex) {
            throw new FirmaException("File not found " + fileID);
        }
    }

    public String loadFileAsB64(int fileID) throws FirmaException {
        try {
            ArchivoFirma archivoFirma = archivoFirmaService.findById(fileID);
            if (archivoFirma != null) {
                logger.info("estado de documento a descargar {}", archivoFirma.getEstado());
                switch (archivoFirma.getEstado()) {
                    case 1:
                        return Utilities.convertFileToB64(env.getProperty("routes.custom.file") + File.separator + "tmp" + File.separator
                                + archivoFirma.getRutaRelativaArchivo(), archivoFirma.getNombreArchivo());
                    case 2:
                        List<FirmaArchivoUsuario> firmados = firmaArchivoService.findByIdArchivoFirma(fileID);
                        if (firmados == null || firmados.isEmpty()) {
                            throw new FirmaException("Documento no completó proceso de firma correctamente" + fileID);
                        }
                        FirmaArchivoUsuario archivoUsuario = firmados.get(0);
                        return Utilities.convertFileToB64(env.getProperty("routes.custom.file") + File.separator
                                + archivoUsuario.getRutaRelativaArchivo(), archivoUsuario.getNombreArchivoFirma());
                    default:
                        throw new FirmaException("Estado no documentado: " + archivoFirma.getEstado());
                }
            }
            throw new FirmaException("File not found in db " + fileID);
        } catch (IOException ex) {
            throw new FirmaException("File not found " + fileID);
        }
    }

    public List<ArchivoFirmaResponseDTO> firmarDocumentos(String sms, long idUsuario, String token, String ipAddress,
            String userAgent) throws FirmaException {
        try {
            return firmaServiceImpl.firmarDocumentos(sms, idUsuario, token, ipAddress, userAgent);
        } catch (Exception e) {
            throw new FirmaException("" + e.getMessage());
        }
    }

    public ApiResponse enviarOtpEmail(String id, String tipo) {
        try {
            Usuario usuario = usuarioService.findById(Long.parseLong(id));
            if (usuario != null) {
                System.out.println("Correo de usuario: " + usuario.getCorreoElectronico() + "  Celular: " + usuario.getNumeroCelular());
                StringBuilder builder;
                String codigo = null;
                
                // Usar el método de reenvío que fuerza el envío
                ProcesoFirma procesoFirma = firmaServiceImpl.buscarUltimoEnvio(id);
                if (procesoFirma != null) {
                    codigo = procesoFirma.getCodigoSms();
                    logger.info("Reenviando código OTP existente: " + codigo + " para usuario: " + id);
                } else {
                    // Si no encuentra código activo, buscar el último proceso
                    logger.warn("No se encontró proceso activo para usuario: " + id);
                    return new ApiResponse.error().data("No se encontró proceso activo para reenvío").build();
                }
                
                //SIEMPRE ENVIAR CUANDO SE LLAMA ESTE MÉTODO
                switch (tipo) {
                    case "EML":
                        EmailService.send(usuario.getCorreoElectronico(), "Fírmese - Código único de verificación",
                                EmailTemplateService.contenidoEnvioOTP(codigo));
                        break;
                    case "SMS":
                        builder = new StringBuilder();
                        builder.append("Estimado usuario");
                        builder.append("\nA continuación indicamos tu código de verificación para firma de documentos en la plataforma Fírmese");
                        builder.append("\n");
                        builder.append(codigo);
                        builder.append("\n");
                        builder.append("Saludos cordiales.");
                        
                        mensajeroClient.notificar(builder.toString(), usuario.getNumeroCelular(), "smv");
                        break;
                    case "WTS":
                        builder = new StringBuilder();
                        builder.append("*Estimado usuario*");
                        builder.append("\n\nA continuación indicamos tu código de verificación para firma de documentos en la plataforma Fírmese");
                        builder.append("\n\n*");
                        builder.append(codigo);
                        builder.append("*\n\n");
                        builder.append("Saludos cordiales.");
                        
                        mensajeroClient.notificar(builder.toString(), usuario.getNumeroCelular(), "wts");
                        break;
                    default:
                        EmailService.send(usuario.getCorreoElectronico(), "Fírmese - Código único de verificación",
                                EmailTemplateService.contenidoEnvioOTP(codigo));
                        break;
                }
                
                logger.info("Código OTP reenviado exitosamente via " + tipo + " para usuario: " + id);
                return new ApiResponse.ok().data("Notificación enviada correctamente").build();
            }
            return new ApiResponse.ok().data("No se encontró registro").codigo("002").build();
        } catch (Exception e) {
            logger.error("Error al reenviar OTP: " + e.getMessage(), e);
            return new ApiResponse.error().data(e.getMessage()).codigo("003").build();
        }

    }

    public Object confirmarFirmado(Integer idArchivo) throws FirmaException {
     try {
            return firmaServiceImpl.confirmarFirmado(idArchivo);
        } catch (Exception e) {
            throw new FirmaException("" + e.getMessage());
        }    
    }

}
