package se.firme.ms.datos.models.entity;

import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

@Entity
@Table(name = "solicitud_firma")
@XmlRootElement
public class SolicitudFirma implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id_solicitud_firma", nullable = false)
    private Long idSolicitudFirma;

    @Column(name = "email_firmante", nullable = false)
    private String emailFirmante;

    @Column(name = "id_usuario", nullable = false)
    private Long idUsuario;

    @Column(name = "id_archivo_firma", nullable = false)
    private long idArchivoFirma;

    private boolean firmado = false;

    @Column(name = "fecha_registro", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    @CreationTimestamp
    private Date fechaRegistro;

    @Column(name = "fecha_firma")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaFirma;

    @Column(name = "fecha_vencimiento")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaVencimiento;

    @Column(name = "rol_firmante", nullable = false)
    private String rolFirmante = "Firmante"; // VALOR POR DEFECTO

    @Column(name = "orden_firma", nullable = false)
    private int ordenFirma = 1; // VALOR POR DEFECTO

    @Column(name = "tipo_orden_firma", nullable = false)
    private String tipoOrdenFirma = "PARALELO"; // VALOR POR DEFECTO

    public SolicitudFirma() {
    }

    public Long getIdSolicitudFirma() {
        return idSolicitudFirma;
    }

    public void setIdSolicitudFirma(Long idSolicitudFirma) {
        this.idSolicitudFirma = idSolicitudFirma;
    }

    public String getEmailFirmante() {
        return emailFirmante;
    }

    public void setEmailFirmante(String emailFirmante) {
        if (emailFirmante != null) {
            emailFirmante = emailFirmante.toLowerCase().trim().replace(" ", "");
        }

        this.emailFirmante = emailFirmante;
    }

    public Long getIdUsuario() {
        return idUsuario;
    }

    public void setIdUsuario(Long idUsuario) {
        this.idUsuario = idUsuario;
    }

    public long getIdArchivoFirma() {
        return idArchivoFirma;
    }

    public void setIdArchivoFirma(long idArchivoFirma) {
        this.idArchivoFirma = idArchivoFirma;
    }

    public boolean isFirmado() {
        return firmado;
    }

    public void setFirmado(boolean firmado) {
        this.firmado = firmado;
    }

    public Date getFechaRegistro() {
        return fechaRegistro;
    }

    public void setFechaRegistro(Date fechaRegistro) {
        this.fechaRegistro = fechaRegistro;
    }

    public Date getFechaFirma() {
        return fechaFirma;
    }

    public void setFechaFirma(Date fechaFirma) {
        this.fechaFirma = fechaFirma;
    }

    public Date getFechaVencimiento() {
        return fechaVencimiento;
    }

    public void setFechaVencimiento(Date fechaVencimiento) {
        this.fechaVencimiento = fechaVencimiento;
    }

    public String getRolFirmante() {
        return rolFirmante != null ? rolFirmante : "Firmante";
    }

    public void setRolFirmante(String rolFirmante) {
        this.rolFirmante = rolFirmante != null ? rolFirmante : "Firmante";
    }

    public int getOrdenFirma() {
        return ordenFirma > 0 ? ordenFirma : 1;
    }

    public void setOrdenFirma(int ordenFirma) {
        this.ordenFirma = ordenFirma > 0 ? ordenFirma : 1;
    }

    public String getTipoOrdenFirma() {
        return tipoOrdenFirma != null ? tipoOrdenFirma : "PARALELO";
    }

    public void setTipoOrdenFirma(String tipoOrdenFirma) {
        this.tipoOrdenFirma = tipoOrdenFirma != null ? tipoOrdenFirma : "PARALELO";
    }

    @Override
    public String toString() {
        return "SolicitudFirma{" +
                "idSolicitudFirma=" + idSolicitudFirma +
                ", emailFirmante='" + emailFirmante + '\'' +
                ", idUsuario=" + idUsuario +
                ", idArchivoFirma=" + idArchivoFirma +
                ", firmado=" + firmado +
                ", fechaRegistro=" + fechaRegistro +
                ", fechaFirma=" + fechaFirma +
                ", fechaVencimiento=" + fechaVencimiento +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SolicitudFirma that = (SolicitudFirma) o;
        return idArchivoFirma == that.idArchivoFirma && firmado == that.firmado && ordenFirma == that.ordenFirma && Objects.equals(idSolicitudFirma, that.idSolicitudFirma) && Objects.equals(emailFirmante, that.emailFirmante) && Objects.equals(idUsuario, that.idUsuario) && Objects.equals(fechaRegistro, that.fechaRegistro) && Objects.equals(fechaFirma, that.fechaFirma) && Objects.equals(fechaVencimiento, that.fechaVencimiento) && Objects.equals(rolFirmante, that.rolFirmante) && Objects.equals(tipoOrdenFirma, that.tipoOrdenFirma);
    }

    @Override
    public int hashCode() {
        return Objects.hash(idSolicitudFirma, emailFirmante, idUsuario, idArchivoFirma, firmado, fechaRegistro, fechaFirma, fechaVencimiento, rolFirmante, ordenFirma, tipoOrdenFirma);
    }
}