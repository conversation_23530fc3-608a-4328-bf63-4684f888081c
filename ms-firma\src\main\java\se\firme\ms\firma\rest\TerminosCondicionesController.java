package se.firme.ms.firma.rest;

import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import se.firme.commons.firmese.dto.ApiResponse;
import se.firme.ms.datos.models.dao.IUsuarioDao;
import se.firme.ms.datos.models.entity.Usuario;

/**
 * <AUTHOR>
 * @document TerminosCondicionesController
 * @fecha 2025-06-16
 */
@RestController
@RefreshScope
@RequestMapping("/manager")
public class TerminosCondicionesController {

    @Autowired
    private IUsuarioDao usuarioDao;
    
    @Autowired
    private HttpServletRequest request;

    /**
     * Verifica si un usuario ha firmado los términos y condiciones
     */
    @PostMapping(path = "/terminos-condiciones/verificar", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> verificarTyC(@RequestParam("idUsuario") int idUsuario) {
        
        try {
            // Usar ID del header como en FirmaController
            int idUsuarioFromHeader = Integer.parseInt(request.getHeader("X-USER-ID"));
            
            // Buscar usuario directamente en DAO
            Usuario usuario = usuarioDao.findById((long)idUsuarioFromHeader).orElse(null);
            
            if (usuario == null) {
                return new ResponseEntity<>(
                    new ApiResponse.error().mensaje("Usuario no encontrado").build(),
                    HttpStatus.NOT_FOUND
                );
            }
            
            boolean haFirmado = usuario.getFirmadoTyc();
            
            Map<String, Object> response = new HashMap<>();
            response.put("idUsuario", idUsuarioFromHeader);
            response.put("haFirmadoTyC", haFirmado);
            response.put("requiereFirmar", !haFirmado);
            response.put("nombreUsuario", usuario.getNombreCompleto());
            response.put("email", usuario.getCorreoElectronico());
            
            if (haFirmado) {
                response.put("mensaje", "Usuario ha firmado los Términos y Condiciones");
                response.put("estado", "COMPLETO");
            } else {
                response.put("mensaje", "Usuario requiere firmar los Términos y Condiciones");
                response.put("estado", "PENDIENTE");
            }
            
            return new ResponseEntity<>(
                new ApiResponse.ok().data(response).build(),
                HttpStatus.OK
            );
            
        } catch (Exception ex) {
            return new ResponseEntity<>(
                new ApiResponse.error().mensaje(ex.getMessage()).build(),
                HttpStatus.BAD_REQUEST
            );
        }
    }
    
    /**
     * Verifica TyC por email
     */
    @PostMapping(path = "/terminos-condiciones/verificar-email", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> verificarTyCPorEmail(@RequestParam("email") String email) {
        
        try {
            // Buscar usuario por email directamente
            Usuario usuario = usuarioDao.findByEmail(email).orElse(null);
            
            if (usuario == null) {
                Map<String, Object> response = new HashMap<>();
                response.put("email", email);
                response.put("haFirmadoTyC", false);
                response.put("requiereFirmar", true);
                response.put("mensaje", "Usuario no encontrado con email: " + email);
                response.put("estado", "NO_ENCONTRADO");
                
                return new ResponseEntity<>(
                    new ApiResponse.ok().data(response).build(),
                    HttpStatus.OK
                );
            }
            
            boolean haFirmado = usuario.getFirmadoTyc();
            
            Map<String, Object> response = new HashMap<>();
            response.put("email", email);
            response.put("idUsuario", usuario.getIdUsuario());
            response.put("haFirmadoTyC", haFirmado);
            response.put("requiereFirmar", !haFirmado);
            response.put("nombreUsuario", usuario.getNombreCompleto());
            
            if (haFirmado) {
                response.put("mensaje", "Usuario con email " + email + " ha firmado los TyC");
                response.put("estado", "COMPLETO");
            } else {
                response.put("mensaje", "Usuario con email " + email + " requiere firmar los TyC");
                response.put("estado", "PENDIENTE");
            }
            
            return new ResponseEntity<>(
                new ApiResponse.ok().data(response).build(),
                HttpStatus.OK
            );
            
        } catch (Exception ex) {
            return new ResponseEntity<>(
                new ApiResponse.error().mensaje(ex.getMessage()).build(),
                HttpStatus.BAD_REQUEST
            );
        }
    }
    
    /**
     * Verifica TyC del usuario actual (usando header X-USER-ID)
     */
    @PostMapping(path = "/terminos-condiciones/verificar-usuario-actual", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> verificarTyCUsuarioActual() {
        
        try {
            // Obtener ID del usuario desde el header (como en FirmaController)
            int idUsuario = Integer.parseInt(request.getHeader("X-USER-ID"));
            
            Usuario usuario = usuarioDao.findById((long)idUsuario).orElse(null);
            
            if (usuario == null) {
                return new ResponseEntity<>(
                    new ApiResponse.error().mensaje("Usuario no encontrado").build(),
                    HttpStatus.NOT_FOUND
                );
            }
            
            boolean haFirmado = usuario.getFirmadoTyc();
            
            Map<String, Object> response = new HashMap<>();
            response.put("idUsuario", idUsuario);
            response.put("haFirmadoTyC", haFirmado);
            response.put("requiereFirmar", !haFirmado);
            response.put("nombreUsuario", usuario.getNombreCompleto());
            response.put("email", usuario.getCorreoElectronico());
            
            if (haFirmado) {
                response.put("mensaje", "Usuario actual ha firmado los Términos y Condiciones");
                response.put("estado", "COMPLETO");
            } else {
                response.put("mensaje", "Usuario actual requiere firmar los Términos y Condiciones");
                response.put("estado", "PENDIENTE");
            }
            
            return new ResponseEntity<>(
                new ApiResponse.ok().data(response).build(),
                HttpStatus.OK
            );
            
        } catch (Exception ex) {
            return new ResponseEntity<>(
                new ApiResponse.error().mensaje(ex.getMessage()).build(),
                HttpStatus.BAD_REQUEST
            );
        }
    }
    
    /**
     * Endpoint de estado del servicio
     */
    @PostMapping(path = "/terminos-condiciones/estado", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> estadoServicio() {
        
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("servicio", "TerminosCondicionesController");
            response.put("estado", "ACTIVO");
            response.put("version", "1.0.0");
            response.put("modulo", "ms-firma");
            response.put("endpoints", new String[]{
                "POST /terminos-condiciones/verificar",
                "POST /terminos-condiciones/verificar-email",
                "POST /terminos-condiciones/verificar-usuario-actual",
                "POST /terminos-condiciones/estado",
                "POST /terminos-condiciones/marcar-firmado"
            });
            
            return new ResponseEntity<>(
                new ApiResponse.ok().data(response).build(),
                HttpStatus.OK
            );
            
        } catch (Exception ex) {
            return new ResponseEntity<>(
                new ApiResponse.error().mensaje(ex.getMessage()).build(),
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Método auxiliar para marcar TyC como firmados (para testing)
     */
    @PostMapping(path = "/terminos-condiciones/marcar-firmado", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ApiResponse> marcarFirmado(@RequestParam("idUsuario") int idUsuario) {
        
        try {
            Usuario usuario = usuarioDao.findById((long)idUsuario).orElse(null);
            
            if (usuario == null) {
                return new ResponseEntity<>(
                    new ApiResponse.error().mensaje("Usuario no encontrado").build(),
                    HttpStatus.NOT_FOUND
                );
            }
            
            // Marcar como firmado
            usuario.setFirmadoTyc(true);
            usuarioDao.save(usuario);
            
            Map<String, Object> response = new HashMap<>();
            response.put("idUsuario", idUsuario);
            response.put("mensaje", "TyC marcados como firmados exitosamente");
            response.put("nombreUsuario", usuario.getNombreCompleto());
            response.put("email", usuario.getCorreoElectronico());
            response.put("firmadoTyC", true);
            
            return new ResponseEntity<>(
                new ApiResponse.ok().data(response).build(),
                HttpStatus.OK
            );
            
        } catch (Exception ex) {
            return new ResponseEntity<>(
                new ApiResponse.error().mensaje(ex.getMessage()).build(),
                HttpStatus.BAD_REQUEST
            );
        }
    }
}
