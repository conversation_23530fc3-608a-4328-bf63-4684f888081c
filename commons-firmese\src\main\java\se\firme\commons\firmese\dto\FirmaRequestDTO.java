/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package se.firme.commons.firmese.dto;

/**
 * <AUTHOR>
 */
public class FirmaRequestDTO {
    private String nombreArchivo;
    private int cantidadFirmas;
    private int idUsuario;
    private String archivo64;
    private String ip;
    private String estado;
    private long idArchivo;
    private String descripcion;

    public FirmaRequestDTO() {
    }

    public FirmaRequestDTO(String nombreArchivo, String estado, long idArchivo) {
        super();
        this.nombreArchivo = nombreArchivo;
        this.estado = estado;
        this.idArchivo = idArchivo;
    }

    public String getNombreArchivo() {
        return nombreArchivo;
    }

    public void setNombreArchivo(String nombreArchivo) {
        this.nombreArchivo = nombreArchivo;
    }

    public int getCantidadFirmas() {
        return cantidadFirmas;
    }

    public void setCantidadFirmas(int cantidadFirmas) {
        this.cantidadFirmas = cantidadFirmas;
    }

    public int getIdUsuario() {
        return idUsuario;
    }

    public void setIdUsuario(int idUsuario) {
        this.idUsuario = idUsuario;
    }

    public String getArchivo64() {
        return archivo64;
    }

    public void setArchivo64(String archivo64) {
        this.archivo64 = archivo64;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public long getIdArchivo() {
        return idArchivo;
    }

    public void setIdArchivo(long idArchivo) {
        this.idArchivo = idArchivo;
    }

    @Override
    public String toString() {
        return "FirmaRequestDTO{" + "nombreArchivo=" + nombreArchivo + ", cantidadFirmas=" + cantidadFirmas + ", idUsuario=" + idUsuario + ", ip=" + ip + '}';
    }

    public String getEstado() {
        return estado;
    }

    public void setEstado(String estado) {
        this.estado = estado;
    }

    public String getDescripcion() {
        return descripcion;
    }

    public void setDescripcion(String descripcion) {
        this.descripcion = descripcion;
    }
}
