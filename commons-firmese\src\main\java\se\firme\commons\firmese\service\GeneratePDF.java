package se.firme.commons.firmese.service;

import java.io.*;
import java.awt.Color;
import java.awt.image.BufferedImage;

import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;

import java.util.*;

import java.util.logging.Logger;

import javax.imageio.ImageIO;

import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.pdfbox.util.ImageIOUtil;
import se.firme.commons.firmese.dto.ArchivoFirmaResponseDTO;
import se.firme.commons.firmese.dto.FirmaArchivoUsuarioResponseDTO;
import se.firme.commons.firmese.dto.UsuarioResponseDTO;

import java.awt.AlphaComposite;
import java.awt.BasicStroke;
import java.awt.Font;
import java.awt.Graphics2D;
import java.awt.Stroke;
import java.awt.font.TextAttribute;
import java.text.AttributedString;
import java.text.SimpleDateFormat;
import java.util.logging.Level;
import org.apache.pdfbox.pdmodel.PDResources;

import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.apache.pdfbox.pdmodel.graphics.state.PDExtendedGraphicsState;
import org.apache.pdfbox.util.Matrix;

import org.vandeseer.easytable.TableDrawer;
import org.vandeseer.easytable.settings.HorizontalAlignment;
import org.vandeseer.easytable.structure.Row;
import org.vandeseer.easytable.structure.Table;
import org.vandeseer.easytable.structure.cell.TextCell;

public class GeneratePDF {

    private final float MARGIN = 25f;
    private float ancho;

    private static Logger logger = Logger.getLogger(GeneratePDF.class.getName());

    public String agregarMarcaVerificable(ArchivoFirmaResponseDTO archivoFirmaResponseDTO, String pathFronted,
            int tipoDocumento, boolean isConFondo, boolean isVerificable, int px, int py, int an, int al)
            throws IOException, WriterException {
        try {
            String filePath = "/opt/firmese/tmp" + File.separator + archivoFirmaResponseDTO.getIdArchivo() + "_"
                    + System.currentTimeMillis() + ".pdf";

            String data = pathFronted + "" + archivoFirmaResponseDTO.getHashArchivo();
            String nombreArchivoQR = "/opt/firmese/tmp/qr" + File.separator + archivoFirmaResponseDTO.getIdArchivo()
                    + "_" + System.currentTimeMillis() + ".png";
            generateQRCode(nombreArchivoQR, data, an, al);

            createFilePDF(archivoFirmaResponseDTO, filePath);

            try (PDDocument tempDoc = PDDocument.load(new File(filePath))) {
                int paginas = tempDoc.getNumberOfPages();
                //Creating PDImageXObject object
                PDImageXObject pdImageTemplate = PDImageXObject.createFromFile("/opt/firmese/template/FIRMESE_1.png", tempDoc);

                for (int i = 0; i < paginas; i++) {
                    PDPage pageTemplate = tempDoc.getPage(i);
                    //Drawing the image in the PDF document
                    try ( //creating the PDPageContentStream object
                            PDPageContentStream contentStreamT = new PDPageContentStream(tempDoc, pageTemplate, PDPageContentStream.AppendMode.APPEND, true, true)) {
                        //Drawing the image in the PDF document
                        contentStreamT.drawImage(pdImageTemplate, 0, 0, pageTemplate.getMediaBox().getWidth(), pageTemplate.getMediaBox().getHeight());
                        /**/
                        contentStreamT.beginText();
                        contentStreamT.setNonStrokingColor(205, 205, 205);
                        contentStreamT.setFont(PDType1Font.TIMES_BOLD, 5);
                        contentStreamT.newLineAtOffset(500, 25);
                        contentStreamT.showText("Este documento está firmado con firme.se");
                        contentStreamT.endText();
                        /**/
                        PDImageXObject pdImage = PDImageXObject.createFromFile(nombreArchivoQR, tempDoc);
                        contentStreamT.drawImage(pdImage, pageTemplate.getMediaBox().getWidth() - 84, pageTemplate.getMediaBox().getHeight() - 76, 64, 64);
                    }
                }

                tempDoc.save(filePath);
            }

            logger.info("Cerrando PDPageContentStream");

            return filePath;
        } catch (IOException ex) {
            System.out.println("ERROR EN SERVICIO EDITOR PDF - AGREGAR MARCA VERIICABLE: " + ex.getMessage());
            throw new IOException("(Se produjo un error al generar un documento): " + ex.getMessage());
        }
    }

    //createFilePDF --> crea la tabla con multipágina
    private void createFilePDF(ArchivoFirmaResponseDTO archivoFirmaResponseDTO, String filePath) throws IOException {
        try {
            PDDocument document = new PDDocument();
            PDPage page = new PDPage();
            document.addPage(page);

            PDPageContentStream contentStream = new PDPageContentStream(document, page, PDPageContentStream.AppendMode.APPEND, true, true);
            ancho = (page.getMediaBox().getWidth() - (2 * MARGIN)) / 4;

            contentStream.beginText();
            contentStream.setFont(PDType1Font.HELVETICA, 14);
            contentStream.newLineAtOffset(MARGIN, page.getMediaBox().getHeight() - 120);
            contentStream.showText("Tabla de firmantes");
            contentStream.endText();

            Table myTableH = Table.builder()
                    .addColumnsOfWidth(ancho + 30, ancho + 30, ancho, ancho - 60)
                    .padding(5)
                    .addRow(Row.builder()
                            .fontSize(10)
                            .textColor(Color.BLACK)
                            .add(TextCell.builder().text("Nombre de firmante").horizontalAlignment(HorizontalAlignment.LEFT).borderWidthTop(0.75f).borderWidthBottom(0.75f).backgroundColor(Color.WHITE).build())
                            .add(TextCell.builder().text("Correo electrónico").horizontalAlignment(HorizontalAlignment.LEFT).borderWidthTop(0.75f).borderWidthBottom(0.75f).backgroundColor(Color.WHITE).build())
                            .add(TextCell.builder().text("Fecha de firma").horizontalAlignment(HorizontalAlignment.LEFT).borderWidthTop(0.75f).borderWidthBottom(0.75f).backgroundColor(Color.WHITE).build())
                            .add(TextCell.builder().text("IP de firma").horizontalAlignment(HorizontalAlignment.LEFT).borderWidthTop(0.75f).borderWidthBottom(0.75f).backgroundColor(Color.WHITE).build())
                            .build())
                    .build();

            // Set up the drawer
            TableDrawer tableDrawer = TableDrawer.builder()
                    .contentStream(contentStream)
                    .startX(MARGIN)
                    .startY(page.getMediaBox().getHeight() - 140)
                    .table(myTableH)
                    .build();

            // And go for it!
            tableDrawer.draw();

            // Build the table
            final Table.TableBuilder myTableBuilder = Table.builder()
                    .addColumnsOfWidth(ancho + 30, ancho + 30, ancho, ancho - 60);

            if (archivoFirmaResponseDTO.getFirmas() != null && !archivoFirmaResponseDTO.getFirmas().isEmpty()) {
                for (FirmaArchivoUsuarioResponseDTO firma : archivoFirmaResponseDTO.getFirmas()) {
                    String nombreFirmante = firma.getFirma().getNombreCompleto();
                    String correoElectronico = firma.getFirma().getCorreoElectronico();
                    String fechaFirma = firma.getFechaFirmaStr();
                    String ips = firma.getIpFirma();

                    myTableBuilder
                            .addRow(Row.builder()
                                    .padding(3)
                                    .fontSize(7)
                                    .add(TextCell.builder().text(nombreFirmante).borderWidthTop(0).borderWidthBottom(0.5f).borderColor(Color.lightGray).backgroundColor(Color.WHITE).build())
                                    .add(TextCell.builder().text(correoElectronico).borderWidthTop(0).borderWidthBottom(0.5f).borderColor(Color.lightGray).backgroundColor(Color.WHITE).build())
                                    .add(TextCell.builder().text(fechaFirma).horizontalAlignment(HorizontalAlignment.LEFT).borderWidthTop(0).borderWidthBottom(0.5f).borderColor(Color.lightGray).backgroundColor(Color.WHITE).build())
                                    .add(TextCell.builder().text(ips).horizontalAlignment(HorizontalAlignment.LEFT).borderWidthTop(0).borderWidthBottom(0.5f).borderColor(Color.lightGray).backgroundColor(Color.WHITE).build())
                                    .build())
                            .build();
                }
                // Set up the drawer
                TableDrawer.builder()
                        .table(myTableBuilder.build())
                        .startX(MARGIN)
                        .startY(page.getMediaBox().getHeight() - 160) //valor a tomar en cuenta en la primera pagina
                        .endY(60) // <-- If the table is bigger, a new page is started
                        .build()
                        .draw(() -> document, () -> new PDPage(), 105); //valor a variar altura inicial desde la siguiente pagina
            }
            contentStream.close();
            document.save(filePath);
            document.close();

        } catch (IOException e) {
            throw new IOException(e.getMessage());
        }
    }

    public String createPDF(ArchivoFirmaResponseDTO archivoFirmaResponseDTO, String pathFronted) throws IOException, WriterException {
        return agregarMarcaVerificable(archivoFirmaResponseDTO, pathFronted, 0, true, true, 500, 670, 400, 400);
    }

    public static void mergePDF(List<InputStream> inputs, OutputStream outputStream) throws IOException {
        try {
            PDFMergerUtility mergePdf = new PDFMergerUtility();

            for (InputStream pdf : inputs) {
                mergePdf.addSource(pdf);
            }
            mergePdf.setDestinationStream(outputStream);
            mergePdf.mergeDocuments(null);
            outputStream.flush();
            outputStream.close();

        } catch (Exception e) {
            logger.log(Level.SEVERE, "ER: {0}", e.getMessage());
            throw new IOException(e.getMessage());
        } finally {
            try {
                if (outputStream != null) {
                    outputStream.close();
                }
            } catch (Exception ioe) {
                logger.log(Level.SEVERE, "ER: {0}", ioe.getMessage());
            }
        }
    }

    private void generateQRCode(String imgPath, String data, int width, int height) throws IOException, WriterException {
        try {
            // Encode URL in QR format
            BitMatrix matrix;
            MultiFormatWriter writer = new MultiFormatWriter();
            Map<EncodeHintType, Object> hintMap = new HashMap<EncodeHintType, Object>();
            hintMap.put(EncodeHintType.MARGIN, 0);
            hintMap.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);

            try {

                matrix = writer.encode(data, BarcodeFormat.QR_CODE, width, height, hintMap);

            } catch (WriterException e) {
                throw new IOException(e.getMessage());
            }

            // Create buffered image to draw to
            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);

            // Iterate through the matrix and draw the pixels to the image
            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    int grayValue = (matrix.get(x, y) ? 0 : 1) & 0xff;
                    image.setRGB(x, y, (grayValue == 0 ? 0 : 0xFFFFFF));
                }
            }

            try ( // Write the image to a file
                    FileOutputStream qrCode = new FileOutputStream(imgPath)) {
                ImageIO.write(image, "png", qrCode);

                File file = new File("/opt/firmese/template/logoRB100.png");
                BufferedImage logoImage = ImageIO.read(file);
                BufferedImage combined = new BufferedImage(400, 400, BufferedImage.TYPE_INT_ARGB);
                Graphics2D g = (Graphics2D) combined.getGraphics();
                g.drawImage(image, 0, 0, null);
                g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 1f));
                g.drawImage(logoImage, 150, 150, null);
                ImageIO.write(combined, "png", new File(imgPath));
                System.out.println("done");

            }
        } catch (IOException e) {
            throw new IOException(e.getMessage());
        }
    }

    public String addVerticalStamp(String nombreFirmante, String hash, String path) {
        File inputFile = new File(path);
        Random random = new Random(100);
        File outputFile = new File("/opt/firmese/tmp/output-" + System.currentTimeMillis() + "-" + random.nextInt() + ".pdf");
        // Texto que se va a escribir, dividido en líneas
        String[] lines = {
            "Código hash: "+hash,
            "Firmado por: " + nombreFirmante + ", el día " + new SimpleDateFormat("EEEE, dd 'de' MMMM, yyyy HH:mm",Locale.forLanguageTag("ES")).format(new Date())
        };

        try (PDDocument document = PDDocument.load(inputFile)) {
            PDImageXObject icon = PDImageXObject.createFromFile("/opt/firmese/template/logoRB100_.png", document);
            float iconHeight = 12; // Altura del ícono en puntos
            float iconWidth = iconHeight * icon.getWidth() / icon.getHeight(); // Mantener la relación de aspecto
            PDExtendedGraphicsState graphicsState = new PDExtendedGraphicsState();
            graphicsState.setNonStrokingAlphaConstant(1.1f);
            for (PDPage page : document.getPages()) {
                PDPageContentStream contentStream = new PDPageContentStream(document, page, PDPageContentStream.AppendMode.APPEND, true, true);
                PDResources resources = page.getResources();
                PDResources modifiedResources = resources == null ? new PDResources() : resources;

                // Configurar la fuente y tamaño
                contentStream.setFont(PDType1Font.HELVETICA_BOLD, 5);
                contentStream.setGraphicsStateParameters(graphicsState);
                // Obtener las dimensiones de la página
                float pageWidth = page.getMediaBox().getWidth();
                float pageHeight = page.getMediaBox().getHeight();

                float x = pageWidth - 2; // Margen derecho

                // Escribir cada línea de texto
                float iconX = x - iconWidth - 5; 
               // contentStream.drawImage(icon, iconX, 200 - (iconHeight / 2), iconWidth, iconHeight);
                contentStream.beginText();
                for (int i = 0; i < lines.length; i++) {
                    float y = pageHeight / 4;
                    x = x - 5;
                    contentStream.setTextMatrix(Matrix.getRotateInstance(Math.toRadians(90), x, y));
                    contentStream.showText(lines[i]);
                }

                contentStream.endText();
                contentStream.close();
            }
            document.save(outputFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return outputFile.getAbsolutePath();
    }

    public String addStamp(String nombreFirmante, String identificacion, String path) throws IOException {

        File inputFile = new File(path);
        Random random = new Random(100);
        File outputFile = new File("/opt/firmese/tmp/output-" + System.currentTimeMillis() + "-" + random.nextInt() + ".pdf");

        File outputImage = new File(createStamp(nombreFirmante, identificacion));

        try (PDDocument document = PDDocument.load(inputFile)) {
            PDImageXObject pdImage = PDImageXObject.createFromFileByExtension(outputImage, document);
            PDExtendedGraphicsState graphicsState = new PDExtendedGraphicsState();
            graphicsState.setNonStrokingAlphaConstant(1.1f); // Configura la opacidad (0.0f es totalmente transparente, 1.0f es opaco)

            for (PDPage page : document.getPages()) {
                PDPageContentStream contentStream = new PDPageContentStream(document, page, PDPageContentStream.AppendMode.APPEND, true, true);
                PDResources resources = page.getResources();
                PDResources modifiedResources = resources == null ? new PDResources() : resources;

                contentStream.saveGraphicsState();
                contentStream.setGraphicsStateParameters(graphicsState);

                // Obtén las dimensiones de la página
                float pageWidth = page.getMediaBox().getWidth();
                float pageHeight = page.getMediaBox().getHeight();

                // Establece las dimensiones y posición de la marca de agua
                float imageWidth = pdImage.getWidth() / 2; // Escalar la imagen si es necesario
                float imageHeight = pdImage.getHeight() / 2;
                float x = (pageWidth - imageWidth);
                float y = 1;

                contentStream.drawImage(pdImage, x, y, imageWidth, imageHeight);
                contentStream.restoreGraphicsState();
                contentStream.close();
            }
            document.save(outputFile);

        } catch (IOException e) {

            throw e;
        }
        return outputFile.getAbsolutePath();
    }

    private String createStamp(String nombreFirmante, String identificacion) {

        int ancho = 400;
        int alto = 100;

        Random random = new Random(100);
        File outputFile = new File("/opt/firmese/tmp/stamp-" + System.currentTimeMillis() + "-" + random.nextInt() + ".png");
        String fecha = new SimpleDateFormat("EEEE dd MMM 'de' yyyy HH:mm").format(new Date());
        // Crear una imagen vacía
        BufferedImage imagen = new BufferedImage(ancho, alto, BufferedImage.TYPE_INT_RGB);

        // Obtener el contexto gráfico de la imagen
        Graphics2D g2d = imagen.createGraphics();

        double thickness = 2;
        Stroke oldStroke = g2d.getStroke();

        // Establecer el color de fondo
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, ancho, alto);

        // Establecer el color del texto
        g2d.setColor(Color.GRAY);

        // Establecer la fuente del texto
        g2d.setFont(new Font("Arial", Font.BOLD, 16));

        // Calcular la posición del texto
        System.out.println("metrics: " + g2d.getFontMetrics().stringWidth(nombreFirmante));
//        int x = (ancho - g2d.getFontMetrics().stringWidth(nombreFirmante)) / 2;
        int x = 20;
        int y = (alto - g2d.getFontMetrics().getHeight()) / 2 + g2d.getFontMetrics().getAscent();

        g2d.setStroke(new BasicStroke((float) thickness));
        g2d.drawRect(0, 0, ancho, alto);
        g2d.setStroke(oldStroke);
        // Dibujar el texto en la imagen
        AttributedString attNombre = new AttributedString(nombreFirmante);
        attNombre.addAttribute(TextAttribute.FONT, new Font("Arial", Font.BOLD, 18), 0, nombreFirmante.length());
        attNombre.addAttribute(TextAttribute.FOREGROUND, Color.DARK_GRAY, 0, nombreFirmante.length());

        g2d.drawString(attNombre.getIterator(), x, y);
        AttributedString attDocumento = new AttributedString(identificacion);
        attDocumento.addAttribute(TextAttribute.FONT, new Font("Arial", Font.BOLD & Font.ITALIC, 16), 0, identificacion.length());
        attDocumento.addAttribute(TextAttribute.FOREGROUND, Color.DARK_GRAY, 0, identificacion.length());
        g2d.drawString(attDocumento.getIterator(), x, y + 14);
        g2d.drawLine(x, y + 15, 380, y + 15);

        g2d.drawString("Firmante de documento", x, y + 30);
        AttributedString attFecha = new AttributedString(fecha);
        attFecha.addAttribute(TextAttribute.FONT, new Font("Arial", Font.BOLD, 12), 0, fecha.length());
        attFecha.addAttribute(TextAttribute.FOREGROUND, Color.GRAY, 0, fecha.length());
        g2d.drawString(attFecha.getIterator(), x, y + 40);

        // Liberar recursos
        g2d.dispose();

        // Guardar la imagen en un archivo
        try {
            ImageIO.write(imagen, "png", outputFile);
            System.out.println("Imagen generada con éxito: " + outputFile.getAbsolutePath());
        } catch (IOException e) {
            System.out.println("Error al generar la imagen: " + e.getMessage());
        }
        return outputFile.getAbsolutePath();
    }

    public String addWatermarkToFile(String path) throws IOException {

        File inputFile = new File(path);
        Random random = new Random(100);
        File outputFile = new File("/opt/firmese/tmp/output-" + System.currentTimeMillis() + "-" + random.nextInt() + ".pdf");

        File outputImage = new File("/opt/firmese/template/watermark.png");

        try (PDDocument document = PDDocument.load(inputFile)) {
            PDImageXObject pdImage = PDImageXObject.createFromFileByExtension(outputImage, document);
            PDExtendedGraphicsState graphicsState = new PDExtendedGraphicsState();
            graphicsState.setNonStrokingAlphaConstant(0.1f); // Configura la opacidad (0.0f es totalmente transparente, 1.0f es opaco)

            for (PDPage page : document.getPages()) {
                PDPageContentStream contentStream = new PDPageContentStream(document, page, PDPageContentStream.AppendMode.APPEND, true, true);
                PDResources resources = page.getResources();
                PDResources modifiedResources = resources == null ? new PDResources() : resources;

                contentStream.saveGraphicsState();
                contentStream.setGraphicsStateParameters(graphicsState);

                // Obtén las dimensiones de la página
                float pageWidth = page.getMediaBox().getWidth();
                float pageHeight = page.getMediaBox().getHeight();

                // Establece las dimensiones y posición de la marca de agua
                float imageWidth = pdImage.getWidth() / 2; // Escalar la imagen si es necesario
                float imageHeight = pdImage.getHeight() / 2;
                float x = (pageWidth - imageWidth) / 2;
                float y = (pageHeight - imageHeight) / 2;

                contentStream.drawImage(pdImage, x, y, imageWidth, imageHeight);
                contentStream.restoreGraphicsState();
                contentStream.close();
            }
            document.save(outputFile);

        } catch (IOException e) {

            throw e;
        }
        return outputFile.getAbsolutePath();
    }

    public static void main(String args[]) throws WriterException {
        try {

            for (int l = 0; l < 1; l++) {

                ArchivoFirmaResponseDTO archivo = new ArchivoFirmaResponseDTO();
                archivo.setIdArchivo(1L);
                archivo.setHashArchivo("2e4c21833e9a01e9e090794a2811413a09077fee");
                archivo.setNombreArchivo("salida.pdf");
                List<FirmaArchivoUsuarioResponseDTO> lista = new ArrayList<>();
                for (int i = 1; i <= 100; i++) {
                    FirmaArchivoUsuarioResponseDTO f = new FirmaArchivoUsuarioResponseDTO();
                    f.setFirma(new UsuarioResponseDTO("JHOSEP SAMUEL MACHADO CARVAJAL " + i, "<EMAIL>"));
                    f.setFechaFirmaStr("2021-07-22 17:07");
                    f.setIpFirma("************");
                    lista.add(f);
                }
                archivo.setFirmas(lista);
                GeneratePDF gp = new GeneratePDF();

                List<InputStream> streams = new ArrayList<>();
                String rutaArchivo = gp.createPDF(archivo, "2e4c21833e9a01e9e090794a2811413a09077fee#50d45d10c680fa00a1f7a669b07966030a4da537_1472_1473");
                String tmp = "/opt/venko/documento6.pdf";
                tmp = gp.addWatermarkToFile(tmp);
                tmp = gp.addStamp("DYLAN SANTIAGO MACHADO CARVAJAL", "CC 1142720000", tmp);
                tmp = gp.addVerticalStamp("DYLAN SANTIAGO MACHADO CARVAJAL", "8f79fdc46e56de01621ffeb388757f9446a9eded94e5336027526d4c5f0fd5e04442fad91062c22a241a4759cbb1cc1aa7d9ae8d5af3b7e8bfe0deeaa75ba693", tmp);
                streams.add(new FileInputStream(tmp));
                streams.add(new FileInputStream(rutaArchivo));
                OutputStream outputStream = new FileOutputStream("/opt/venko/salida-doc" + l + ".pdf");
                mergePDF(streams, outputStream);
                System.out.println("rutaArchivo: " + rutaArchivo);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception ex) {
            Logger.getLogger(GeneratePDF.class.getName()).log(Level.SEVERE, null, ex);
        }

    }

    public static byte[] generatePdfPreview(String path) throws IOException {
        try (PDDocument pd = PDDocument.load(new File(path))) {
            PDFRenderer pr = new PDFRenderer(pd);
            BufferedImage bi = pr.renderImage(0, 0.5f, ImageType.RGB);

            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIOUtil.writeImage(bi, "png", baos);
            baos.flush();
            byte[] imageInByte = baos.toByteArray();
            baos.close();

            return imageInByte;
        }
    }

    public static String generatePdfPreviewB64(String path) throws IOException {
        byte[] imageInByte = GeneratePDF.generatePdfPreview(path);

        return Base64.getEncoder().encodeToString(imageInByte);
    }
}
