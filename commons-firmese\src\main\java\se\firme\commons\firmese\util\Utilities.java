/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package se.firme.commons.firmese.util;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.lowagie.text.Document;
import com.lowagie.text.Rectangle;
import com.lowagie.text.pdf.PdfContentByte;
import com.lowagie.text.pdf.PdfImportedPage;
import com.lowagie.text.pdf.PdfReader;
import com.lowagie.text.pdf.PdfWriter;
import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.CopyOption;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Random;
import java.util.logging.Level;
import java.util.logging.Logger;

import se.firme.commons.firmese.dto.FirmaRequestDTO;
import se.firme.commons.firmese.enumerados.EAlgoritmoHash;
import se.firme.commons.firmese.exceptions.ValidationsException;
//import sun.misc.BASE64Encoder;

/**
 * @document Utilities
 * <AUTHOR> Machado Jaimes
 * @fecha martes, agosto 18 de 2020, 03:34:54 PM
 */
public class Utilities {

	private static Logger logger = Logger.getLogger(Utilities.class.getName());

	public static String maskMobileNumber(String mobile) {
		final String mask = "*******";
		mobile = mobile == null ? mask : mobile;
		final int lengthOfMobileNumber = mobile.length();
		if (lengthOfMobileNumber > 2) {
			final int maskLen = Math.min(Math.max(lengthOfMobileNumber / 1, 1), 7);
			final int start = (lengthOfMobileNumber - maskLen) / 2;
			return mobile.substring(0, start) + mask.substring(0, maskLen) + mobile.substring(start + maskLen);
		}
		return mobile;
	}

	public static String generarToken(int longitud) {
		String cadenaAleatoria = "";
		long milis = new java.util.GregorianCalendar().getTimeInMillis();
		Random r = new Random(milis);
		int i = 0;
		while (i < longitud) {
			char c = (char) r.nextInt(255);
			if ((c >= '0' && c <= '9') || (c >= 'A' && c <= 'Z')) {
				cadenaAleatoria += c;
				i++;
			}
		}
		return cadenaAleatoria;
	}

	public static String getNumeroAleatorio(int digitos) {
		String rta = "";

		long milis = new java.util.GregorianCalendar().getTimeInMillis();
		Random r = new Random(milis);
		int i = 0;
		while (i < digitos) {

			char c = (char) r.nextInt(255);

			if ((c >= '1' && c <= '9')) {
				rta += c;
				i++;
			}
		}

		return rta;
	}

	public static Date getSumarMinutosHorasDiasAFecha(Date fecha, int minutos, int horas, int dias) {
		Date result = null;

		Calendar cal = Calendar.getInstance();
		cal.setTime(fecha);
		fecha = cal.getTime();

		cal.set(Calendar.MINUTE, cal.get(Calendar.MINUTE) + minutos);
		cal.set(Calendar.HOUR, cal.get(Calendar.HOUR) + horas);
		cal.set(Calendar.DAY_OF_WEEK, cal.get(Calendar.DAY_OF_WEEK) + dias);

		fecha = cal.getTime();

		return result = fecha;
	}

	public static boolean isVacio(String cadena) {
		if (cadena == null || cadena.trim().equals("")) {
			return true;
		} else {
			return false;
		}
	}

	public static String getDigits(String numeroCelular) throws IOException {
		if (numeroCelular != null && !"".equals(numeroCelular)) {
			int l = numeroCelular.length();
			return numeroCelular.substring(l - 4, l - 2);
		}
		throw new IOException("No hay contenido en la variable asignada");
	}

	public static String getAtributoDeJson(String json, String atributo) {
		JsonParser parser = new JsonParser();
		JsonObject obj = parser.parse(json).getAsJsonObject();

		try {
			return obj.get(atributo).getAsString();
		} catch (Exception e) {
			return null;
		}
	}

	public static JsonObject getJsonObject(JsonObject json, String miembro) {
		try {
			return json.getAsJsonObject(miembro);
		} catch (Exception e) {
			return null;
		}
	}

	public static JsonObject getJsonObject(String json) {
		JsonParser parser = new JsonParser();
		return parser.parse(json).getAsJsonObject();
	}

	public static JsonObject getJsonObject(String json, String miembro) {
		return getJsonObject(json).getAsJsonObject(miembro);
	}

	public static String getJsonString(JsonObject json, String atributo) {
		try {
			return json.get(atributo).getAsString();
		} catch (Exception e) {
			return null;
		}
	}

	public static String getFechaDateAFechaTexto(Date fecha, String patron) throws ParseException {
		if (patron != null) {
			return new SimpleDateFormat(patron, new Locale("es", "ES")).format(fecha);
		}
		return new SimpleDateFormat("EE, MMM dd 'de' yyyy HH:mm", new Locale("es", "ES")).format(fecha);
	}

	public static Date getFechaTextoADate(String fecha) throws Exception {
		if (fecha != null && !"".equals(fecha)) {
			try {
				return getFechaTextoADate(fecha, "yyyy-MM-dd");
			} catch (Exception e) {
				throw new Exception("Formato de fecha no permitido, asegurate que asignar el patrón indicado yyyy-MM-dd");
			}
		}
		return null;
	}

	public static Date getFechaTextoADate(String fecha, String patron) throws ParseException {
		return new SimpleDateFormat(patron).parse(fecha);
	}

	public static void escribirArchivoBase64EnDisco(String archivoBase64, String ruraArchivo)
			throws FileNotFoundException, IOException {
//        byte[] bytes = new sun.misc.BASE64Decoder().decodeBuffer(archivoBase64);
		byte[] bytes = Base64.getDecoder().decode(archivoBase64);
		File outputFile = new File(ruraArchivo);
		try (FileOutputStream outputStream = new FileOutputStream(outputFile)) {
			outputStream.write(bytes);
		} catch (Exception e) {
			System.out.println("The file can't be saved : " + ruraArchivo + " : " + e.getMessage());
		}
	}

	public static void crearDirectorio(String directorio, boolean todaLaRuta) throws IOException {
		File folder = new File(directorio);
		if (todaLaRuta) {
			folder.mkdirs();
		} else {
			folder.mkdir();
		}
	}

	public static String getHashing(String tipoDigest, String rutaArchivo)
			throws NoSuchAlgorithmException, IOException {
		// https://howtodoinjava.com/java/io/sha-md5-file-checksum-hash/
		MessageDigest digest = MessageDigest.getInstance(tipoDigest);
		File file = new File(rutaArchivo);
		return getFileHash(digest, file);
	}

	private static String getFileHash(MessageDigest digest, File file) throws IOException {
		FileInputStream fis = new FileInputStream(file);
		byte[] byteArray = new byte[1024];
		int bytesCount = 0;
		while ((bytesCount = fis.read(byteArray)) != -1) {
			digest.update(byteArray, 0, bytesCount);
		}
		;
		fis.close();
		byte[] bytes = digest.digest();
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < bytes.length; i++) {
			sb.append(Integer.toString((bytes[i] & 0xff) + 0x100, 16).substring(1));
		}

		return sb.toString();
	}

	public static String replace(String codSms) {
		try {
			codSms = codSms.replaceAll("0", "H");
			codSms = codSms.replaceAll("O", "X");
			codSms = codSms.replaceAll("I", "N");
			codSms = codSms.replaceAll("1", "L");
			return codSms;
		} catch (Exception e) {
			return codSms;
		}
	}

	public static String converB64ToString(String document) throws IOException {
		try {
			byte[] array = Base64.getDecoder().decode(document);
			return new String(array, "UTF-8");
		} catch (UnsupportedEncodingException e) {
			throw new IOException("No se pudo convertir la cadena " + e.getMessage());
		}
	}

	public static boolean isEncryptedPDF(String rutaArchivo) throws IOException {
		try {
			PdfReader reader = new PdfReader(rutaArchivo);
			return reader.isEncrypted();
		} catch (Exception e) {
			throw new IOException("No se pudo leer el archivo " + e.getMessage());
		}
	}

	public static String procesarArchivo(FirmaRequestDTO datosFirma, byte[] bytes, String rutaArchivos)
			throws IOException, Exception {
		String rutaArchivo = "";
		if (bytes == null) {
			rutaArchivo = descargarArchivoBase64(datosFirma, rutaArchivos);
		} else {
			rutaArchivo = descargarArhivoBinario(bytes, datosFirma, rutaArchivos);
		}
		// TODO: Si se define que los archivos fisicos se deben guardar, se debe crear
		// la funcionalidad de primero descargar en una tmp para calcular el hash y
		// alfinal moverlo al destino final

		if (isVacio(rutaArchivo)) {
			throw new Exception("Se presentaron inconvenientes con la firma del archivo");
		}

		return rutaArchivo;
	}

	public static String descargarArchivoBase64(FirmaRequestDTO datosFirma, String rutaArchivos)
			throws IOException, ParseException {
		String rutaPrincipal = getRutaPrincipalDescarga(datosFirma, rutaArchivos);
		String rutaArchivo = rutaPrincipal + datosFirma.getNombreArchivo();

		escribirArchivoBase64EnDisco(datosFirma.getArchivo64(), rutaArchivo);

		return rutaArchivo;
	}

	public static String descargarArhivoBinario(byte[] bytes, FirmaRequestDTO datosFirma, String rutaArchivos)
			throws IOException, ParseException {
		String rutaPrincipal = getRutaPrincipalDescarga(datosFirma, rutaArchivos);
		String rutaArchivo = rutaPrincipal + datosFirma.getNombreArchivo();

		Path pathFile = Paths.get(rutaArchivo);
		Files.write(pathFile, bytes);

		return rutaArchivo;
	}

	public static String getPathArchivo(long idUsuario) throws ParseException {
		String fecha = getFechaDateAFechaTexto(new Date(), "yyyyMMdd");
		return String.valueOf(idUsuario) + File.separator + fecha + File.separator + "original" + File.separator;
	}

	public static String getRutaPrincipalDescarga(FirmaRequestDTO datosFirma, String rutaArchivos)
			throws IOException, ParseException {
		String path = getPathArchivo(datosFirma.getIdUsuario());
		String rutaPrincipal = rutaArchivos + path;

		crearDirectorio(rutaPrincipal, true);

		return rutaPrincipal;
	}

	public static String generarHash(String rutaArchivo) throws IOException {
		try {
			String tipoDigest = Parameters.getParameter(Parameters.HASH_ALGORITMO);

			if (!EAlgoritmoHash.isHashValido(tipoDigest)) {
				throw new IOException("El hash configurado no es válido");
			}

			return Utilities.getHashing(tipoDigest, rutaArchivo);
		} catch (NoSuchAlgorithmException | NullPointerException ex) {
			throw new IOException("ER: " + ex.getMessage());
		}
	}

	public static void copiarArchivos(String origen, String destino) throws IOException {
		Path FROM = Paths.get(origen);
		Path TO = Paths.get(destino);
		CopyOption[] options = new CopyOption[] { StandardCopyOption.REPLACE_EXISTING,
				StandardCopyOption.COPY_ATTRIBUTES };
		Files.copy(FROM, TO, options);
	}

	public static boolean isBorrarArchivo(String archivo) throws IOException {
		try {
			File arc = new File(archivo);
			return arc.delete();
		} catch (Exception e) {
			throw new IOException("No se pudo mover el archivo al directorio de firmas");
		}
	}



	public static Connection getConexionJDBC(String urlConexion) throws SQLException {
		return DriverManager.getConnection(urlConexion);
	}

	public static void concatenarPDFs(List<InputStream> streamOfPDFFiles, OutputStream outputStream, boolean paginate) {

		Document document = new Document();
		try {
			List<InputStream> pdfs = streamOfPDFFiles;
			List<PdfReader> readers = new ArrayList<PdfReader>();
			int totalPages = 0;
			Iterator<InputStream> iteratorPDFs = pdfs.iterator();

			while (iteratorPDFs.hasNext()) {
				InputStream pdf = iteratorPDFs.next();
				PdfReader pdfReader = new PdfReader(pdf);
				readers.add(pdfReader);
				totalPages += pdfReader.getNumberOfPages();
			}

			PdfWriter writer = PdfWriter.getInstance(document, outputStream);

			document.open();
			PdfContentByte cb = writer.getDirectContent();

			PdfImportedPage page;
			int currentPageNumber = 0;
			int pageOfCurrentReaderPDF = 0;
			Iterator<PdfReader> iteratorPDFReader = readers.iterator();

			while (iteratorPDFReader.hasNext()) {
				PdfReader pdfReader = iteratorPDFReader.next();

				while (pageOfCurrentReaderPDF < pdfReader.getNumberOfPages()) {

					Rectangle rectangle = pdfReader.getPageSizeWithRotation(1);
					document.setPageSize(rectangle);
					document.newPage();

					pageOfCurrentReaderPDF++;
					currentPageNumber++;
					page = writer.getImportedPage(pdfReader, pageOfCurrentReaderPDF);
					switch (rectangle.getRotation()) {
					case 0:
						cb.addTemplate(page, 1f, 0, 0, 1f, 0, 0);
						break;
					case 90:
						cb.addTemplate(page, 0, -1f, 1f, 0, 0, pdfReader.getPageSizeWithRotation(1).getHeight());
						break;
					case 180:
						cb.addTemplate(page, -1f, 0, 0, -1f, 0, 0);
						break;
					case 270:
						cb.addTemplate(page, 0, 1.0F, -1.0F, 0, pdfReader.getPageSizeWithRotation(1).getWidth(), 0);
						break;
					default:
						break;
					}
					if (paginate) {
						cb.beginText();
						cb.getPdfDocument().getPageSize();
						cb.endText();
					}
				}
				pageOfCurrentReaderPDF = 0;
			}
			outputStream.flush();
			document.close();
			outputStream.close();
		} catch (Exception e) {
			logger.log(Level.SEVERE, "ER: {0}", e.getMessage());
		} finally {
			if (document.isOpen()) {
				document.close();
			}
			try {
				if (outputStream != null) {
					outputStream.close();
				}
			} catch (IOException ioe) {
				logger.log(Level.SEVERE, "ER: {0}", ioe.getMessage());
			}
		}
	}

	public static String convertirRutaArchivoStringAABse64(String rutaArchivo) throws Exception {
		// BASE64Encoder encoder = new BASE64Encoder();
		try {
			byte[] base64EncodedImage = convertirArchivoAByte(rutaArchivo);
			if (base64EncodedImage != null) {
				// String imageBase64 = encoder.encodeBuffer(base64EncodedImage);
				String imageBase64 = Base64.getEncoder().encodeToString(base64EncodedImage);
				if (imageBase64 != null && !imageBase64.trim().equals("")) {
					return imageBase64;
				} else {
					return null;
				}
			} else {
				return null;
			}
		} catch (Exception e) {
			return null;
		}
	}

	public static byte[] convertirArchivoAByte(String rutaArchivo) throws Exception {
		File file = new File(rutaArchivo.toString());
		if (file.exists()) {
			BufferedInputStream reader = new BufferedInputStream(new FileInputStream(file));
			try {
				int lenght = (int) file.length();
				byte[] bytes = new byte[lenght];
				reader.read(bytes, 0, lenght);
				reader.close();
				return bytes;
			} catch (Exception e) {
				return null;
			} finally {
				reader.close();
			}
		} else {
			return null;
		}
	}

	public static boolean notificarEndpoint(boolean isEnable, String endpointCBack, List<FirmaRequestDTO> firmas) {
		if (!isEnable || endpointCBack == null || "".equals(endpointCBack)) {
			return false;
		}
		HttpURLConnection connection = null;
		String urlParameters = new Gson().toJson(firmas);
		try {
			// Create connection
			URL url = new URL(endpointCBack);
			connection = (HttpURLConnection) url.openConnection();
			connection.setRequestMethod("POST");
			connection.setRequestProperty("Content-Type", "application/json");

			connection.setRequestProperty("Content-Length", Integer.toString(urlParameters.getBytes().length));
			// connection.setRequestProperty("Content-Language", "en-US");

			connection.setUseCaches(false);
			connection.setDoOutput(true);

			// Send request
			DataOutputStream wr = new DataOutputStream(connection.getOutputStream());
			wr.writeBytes(urlParameters);
			wr.close();

			// Get Response
			InputStream is = connection.getInputStream();
			BufferedReader rd = new BufferedReader(new InputStreamReader(is));
			StringBuilder response = new StringBuilder(); // or StringBuffer if Java version 5+
			String line;
			while ((line = rd.readLine()) != null) {
				response.append(line);
				response.append('\r');
			}
			rd.close();
			logger.info(response.toString());
			// return response.toString();
			return true;
		} catch (Exception e) {
			logger.severe("" + e.getMessage());
			return false;
		} finally {
			if (connection != null) {
				connection.disconnect();
			}
		}
	}

	public static void main(String[] args) throws ParseException {
		List<FirmaRequestDTO> firmas = new ArrayList<>();
		firmas.add(new FirmaRequestDTO("acta.pdf", "firmado", 1));
		notificarEndpoint(true, "http://192.168.1.30:1024/user", firmas);
	}

	public static boolean validarMaximoFirmantes(int totalFirmantes, String maxFirmantes) throws ValidationsException {
		try {
			logger.info("Total firmantes en el documento " + totalFirmantes + ", máximo permitido = " + maxFirmantes);
			if (maxFirmantes == null || "".equals(maxFirmantes)) {
				return true;
			}
			int maximo = Integer.parseInt(maxFirmantes);
			if (totalFirmantes <= maximo) {
				return true;
			}
			throw new ValidationsException("Es permitido como máximo " + maximo + " firmantes en el documento");
		} catch (Exception e) {
			throw new ValidationsException(e.getMessage());
		}

	}

	public static String convertFileToB64(String pathFile, String fileName) throws IOException {
		try {
			logger.info("Convirtiendo archivo " + fileName + " ruta: " + pathFile);
			Path fileStorageLocation = Paths.get(pathFile).toAbsolutePath().normalize();
			Path filePath = fileStorageLocation.resolve(fileName).normalize();
			File file = filePath.toFile();

			if (file.exists()) {
				byte[] fileContent = Files.readAllBytes(filePath);
				return Base64.getEncoder().encodeToString(fileContent);
			} else {
				throw new IOException("File not found in storage: " + pathFile + File.separator + fileName);
			}
		} catch (Exception e) {
            logger.severe("Error while converting file to base64: " + e.getMessage());
			throw new IOException("ERROR: " + e.getMessage());
		}
	}

}
