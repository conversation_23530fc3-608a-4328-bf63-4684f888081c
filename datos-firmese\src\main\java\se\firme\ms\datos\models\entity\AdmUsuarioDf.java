package se.firme.ms.datos.models.entity;

import javax.persistence.*;

@Entity
@Table(name = "adm_usuario", schema = "gestion_adm")
public class AdmUsuarioDf {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id_usuario")
    private Long idUsuario;

    @Column(name = "nombre_usuario")
    private String nombreUsuario;

    @Column(name = "telefono_usuario")
    private String telefonoUsuario;

    @Column(name = "direccion_usuario")
    private String direccionUsuario;

    @Column(name = "clave_usuario")
    private String claveUsuario;

    @Column(name = "activo_usuario")
    private Integer activoUsuario;

    @Column(name = "e_mail_usuario")
    private String eMailUsuario;

    @Column(name = "client_id")
    private String client_id;

    @Column(name = "id_usuario_en_cliente")
    private Long idUsuarioEnCliente;

    // Getters y setters
    public Long getIdUsuario() { return idUsuario; }
    public void setIdUsuario(Long idUsuario) { this.idUsuario = idUsuario; }

    public String getNombreUsuario() { return nombreUsuario; }
    public void setNombreUsuario(String nombreUsuario) { this.nombreUsuario = nombreUsuario; }

    public String getTelefonoUsuario() { return telefonoUsuario; }
    public void setTelefonoUsuario(String telefonoUsuario) { this.telefonoUsuario = telefonoUsuario; }

    public String getDireccionUsuario() { return direccionUsuario; }
    public void setDireccionUsuario(String direccionUsuario) { this.direccionUsuario = direccionUsuario; }

    public String getClaveUsuario() { return claveUsuario; }
    public void setClaveUsuario(String claveUsuario) { this.claveUsuario = claveUsuario; }

    public Integer getActivoUsuario() { return activoUsuario; }
    public void setActivoUsuario(Integer activoUsuario) { this.activoUsuario = activoUsuario; }

    public String geteMailUsuario() { return eMailUsuario; }
    public void seteMailUsuario(String eMailUsuario) { this.eMailUsuario = eMailUsuario; }

    public String getClient_id() { return client_id; }
    public void setClient_id(String client_id) { this.client_id = client_id; }

    public Long getIdUsuarioEnCliente() { return idUsuarioEnCliente; }
    public void setIdUsuarioEnCliente(Long idUsuarioEnCliente) { this.idUsuarioEnCliente = idUsuarioEnCliente; }
}