/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package se.firme.ms.datos.models.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;

/**
 * <AUTHOR>
 * @document Servicio
 * @fecha mié<PERSON>ole<PERSON>, enero 13 de 2021, 03:27:27 PM
 */
@Entity
@Table(name = "servicio")
@XmlRootElement
@NamedQueries({
        @NamedQuery(name = "Servicio.findAll", query = "SELECT s FROM Servicio s"),
        @NamedQuery(name = "Servicio.findByIdServicio", query = "SELECT s FROM Servicio s WHERE s.idServicio = :idServicio"),
        @NamedQuery(name = "Servicio.findByCantidadFirmas", query = "SELECT s FROM Servicio s WHERE s.cantidadFirmas = :cantidadFirmas"),
        @NamedQuery(name = "Servicio.findByFechaVencimiento", query = "SELECT s FROM Servicio s WHERE s.fechaVencimiento = :fechaVencimiento"),
        @NamedQuery(name = "Servicio.findByFechaActualizacion", query = "SELECT s FROM Servicio s WHERE s.fechaActualizacion = :fechaActualizacion"),
        @NamedQuery(name = "Servicio.findByTipoServicio", query = "SELECT s FROM Servicio s WHERE s.tipoServicio = :tipoServicio")})
public class Servicio implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "id_servicio")
    private Long idServicio;
    @Basic(optional = false)
    @NotNull
    @Column(name = "cantidad_firmas")
    private int cantidadFirmas;
    @Basic(optional = false)
    @NotNull
    @Column(name = "fecha_vencimiento")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaVencimiento;
    @Basic(optional = false)
    @NotNull
    @Column(name = "fecha_actualizacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaActualizacion;
    @Basic(optional = false)
    @NotNull
    @Size(min = 1, max = 3)
    @Column(name = "tipo_servicio")
    private String tipoServicio;
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "idServicio")
    private List<PaqueteServicio> paqueteServicioList;
    @JoinColumn(name = "id_servicio", referencedColumnName = "id_usuario", insertable = false, updatable = false)
    @OneToOne(optional = false)
    private Usuario usuario;
    @Column(name = "endpoint_cback")
    private String endpointCBack;

    @Column(name = "endpoint_cback_habilitado")
    private boolean endpointCBackHabilitado = false;

    @Column(name = "tipo_validacion")
    private String tipoValidacion;
    @Column(name = "cant_otros")
    private int cantOtros;

    @Column(name = "notificar_firma")
    private boolean notificarFirma = false;
    @Column(name = "oris_habilitado")
    private boolean orisHabilitado = false;

    public Servicio() {
    }

    public Servicio(Long idServicio) {
        this.idServicio = idServicio;
    }

    public Servicio(Long idServicio, int cantidadFirmas, Date fechaVencimiento, Date fechaActualizacion, String tipoServicio) {
        this.idServicio = idServicio;
        this.cantidadFirmas = cantidadFirmas;
        this.fechaVencimiento = fechaVencimiento;
        this.fechaActualizacion = fechaActualizacion;
        this.tipoServicio = tipoServicio;
    }

    public Long getIdServicio() {
        return idServicio;
    }

    public void setIdServicio(Long idServicio) {
        this.idServicio = idServicio;
    }

    public int getCantidadFirmas() {
        return cantidadFirmas;
    }

    public void setCantidadFirmas(int cantidadFirmas) {
        this.cantidadFirmas = cantidadFirmas;
    }

    public Date getFechaVencimiento() {
        return fechaVencimiento;
    }

    public void setFechaVencimiento(Date fechaVencimiento) {
        this.fechaVencimiento = fechaVencimiento;
    }

    public Date getFechaActualizacion() {
        return fechaActualizacion;
    }

    public void setFechaActualizacion(Date fechaActualizacion) {
        this.fechaActualizacion = fechaActualizacion;
    }

    public String getTipoServicio() {
        return tipoServicio;
    }

    public void setTipoServicio(String tipoServicio) {
        this.tipoServicio = tipoServicio;
    }

    @XmlTransient
    public List<PaqueteServicio> getPaqueteServicioList() {
        return paqueteServicioList;
    }

    public void setPaqueteServicioList(List<PaqueteServicio> paqueteServicioList) {
        this.paqueteServicioList = paqueteServicioList;
    }

    public Usuario getUsuario() {
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (idServicio != null ? idServicio.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Servicio)) {
            return false;
        }
        Servicio other = (Servicio) object;
        if ((this.idServicio == null && other.idServicio != null) || (this.idServicio != null && !this.idServicio.equals(other.idServicio))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "se.firme.ms.datos.models.entity.Servicio[ idServicio=" + idServicio + " ]";
    }

    public String getEndpointCBack() {
        return endpointCBack;
    }

    public void setEndpointCBack(String endpointCBack) {
        this.endpointCBack = endpointCBack;
    }

    public String getTipoValidacion() {
        return tipoValidacion;
    }

    public void setTipoValidacion(String tipoValidacion) {
        this.tipoValidacion = tipoValidacion;
    }

    public int getCantOtros() {
        return cantOtros;
    }

    public void setCantOtros(int cantOtros) {
        this.cantOtros = cantOtros;
    }

    public boolean isNotificarFirma() {
        return notificarFirma;
    }

    public void setNotificarFirma(boolean notificarFirma) {
        this.notificarFirma = notificarFirma;
    }

    public boolean isEndpointCBackHabilitado() {
        return endpointCBackHabilitado;
    }

    public void setEndpointCBackHabilitado(boolean endpointCBackHabilitado) {
        this.endpointCBackHabilitado = endpointCBackHabilitado;
    }

    public boolean isOrisHabilitado() {
        return orisHabilitado;
    }

    public void setOrisHabilitado(boolean orisHabilitado) {
        this.orisHabilitado = orisHabilitado;
    }
}
