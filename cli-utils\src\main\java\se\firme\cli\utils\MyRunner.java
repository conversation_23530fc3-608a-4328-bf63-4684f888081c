package se.firme.cli.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import se.firme.cli.utils.commands.MigrateSignRequestsCommand;
import se.firme.ms.datos.models.dao.IArchivoFirmaDao;
import se.firme.ms.datos.models.dao.ISolicitudFirmaDao;
import se.firme.ms.datos.models.dao.ITokenDao;

import java.util.Arrays;
import java.util.logging.Logger;

@Component
public class MyRunner implements CommandLineRunner {

    private static Logger LOG = Logger.getLogger(CliUtilsApplication.class.getName());
    private final ITokenDao tokenDao;

    private final ISolicitudFirmaDao solicitudFirmaDao;

    private final IArchivoFirmaDao archivoFirmaDao;

    @Autowired
    public MyRunner(ITokenDao tokenDao, ISolicitudFirmaDao solicitudFirmaDao, IArchivoFirmaDao archivoFirmaDao) {
        this.tokenDao = tokenDao;
        this.solicitudFirmaDao = solicitudFirmaDao;
        this.archivoFirmaDao = archivoFirmaDao;
    }

    private void help() {
        LOG.info("Available commands:");
        LOG.info("  help");
        LOG.info("  migrate-sign-requests");
    }

    @Override
    public void run(String... args) throws Exception {

        args = Arrays.stream(args).filter(arg -> !arg.contains("--spring")).toArray(String[]::new);

        if (args.length == 0) {
            LOG.info("No command specified");
            help();
            return;
        }

        String commandName = args[0];

        if (commandName.equals("help")) {
            help();
            return;
        }

        if (commandName.equals("migrate-sign-requests")) {
            MigrateSignRequestsCommand command = new MigrateSignRequestsCommand(tokenDao, solicitudFirmaDao, archivoFirmaDao);
            command.run();
            return;
        }


        LOG.info(commandName);
    }
}
