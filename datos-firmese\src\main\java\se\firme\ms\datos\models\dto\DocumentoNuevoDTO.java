package se.firme.ms.datos.models.dto;

public class DocumentoNuevoDTO {
    
    private String nombreArchivo;
    private String archivo64; // Base64 del PDF
    private String descripcion;
    private Integer cantidadFirmas;
    
    // Constructores
    public DocumentoNuevoDTO() {}
    
    public DocumentoNuevoDTO(String nombreArchivo, String archivo64, String descripcion) {
        this.nombreArchivo = nombreArchivo;
        this.archivo64 = archivo64;
        this.descripcion = descripcion;
    }
    
    // Getters y Setters
    public String getNombreArchivo() { return nombreArchivo; }
    public void setNombreArchivo(String nombreArchivo) { this.nombreArchivo = nombreArchivo; }
    
    public String getArchivo64() { return archivo64; }
    public void setArchivo64(String archivo64) { this.archivo64 = archivo64; }
    
    public String getDescripcion() { return descripcion; }
    public void setDescripcion(String descripcion) { this.descripcion = descripcion; }
    
    public Integer getCantidadFirmas() { return cantidadFirmas; }
    public void setCantidadFirmas(Integer cantidadFirmas) { this.cantidadFirmas = cantidadFirmas; }
    
    @Override
    public String toString() {
        return "DocumentoNuevoDTO{" +
                "nombreArchivo='" + nombreArchivo + '\'' +
                ", descripcion='" + descripcion + '\'' +
                ", cantidadFirmas=" + cantidadFirmas +
                '}';
    }
}