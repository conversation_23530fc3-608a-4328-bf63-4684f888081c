package se.firme.ms.datos.models.entity;

import javax.persistence.*;

@Entity
@Table(name = "plantilla_documento")
public class PlantillaDocumento {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id_plantilla")
    private Long idPlantilla;
    
    @Column(name = "nombre_archivo", nullable = false)
    private String nombreArchivo;
    
    @Column(name = "hash_archivo", nullable = false, unique = true)
    private String hashArchivo;
    
    @Column(name = "ruta_relativa_archivo")
    private String rutaRelativaArchivo;
    
    @Column(name = "descripcion")
    private String descripcion;
    
    @Column(name = "tipo_firma")
    private String tipoFirma = "MULTIPLE";
    
    @Column(name = "activa")
    private Boolean activa = true;
    
    @Column(name = "id_usuario_creador")
    private Long idUsuarioCreador;
    
    @Column(name = "nombre_plantilla", nullable = false)
    private String nombrePlantilla;
    
    @Column(name = "tipo_documento")
    private String tipoDocumento;
    
    // Constructores
    public PlantillaDocumento() {}
    
    public PlantillaDocumento(String nombreArchivo, String hashArchivo, String rutaRelativaArchivo, 
                             String descripcion, String nombrePlantilla) {
        this.nombreArchivo = nombreArchivo;
        this.hashArchivo = hashArchivo;
        this.rutaRelativaArchivo = rutaRelativaArchivo;
        this.descripcion = descripcion;
        this.nombrePlantilla = nombrePlantilla;
        this.activa = true;
        this.tipoFirma = "MULTIPLE";
    }
    
    // Getters y Setters
    public Long getIdPlantilla() {
        return idPlantilla;
    }
    
    public void setIdPlantilla(Long idPlantilla) {
        this.idPlantilla = idPlantilla;
    }
    
    public String getNombreArchivo() {
        return nombreArchivo;
    }
    
    public void setNombreArchivo(String nombreArchivo) {
        this.nombreArchivo = nombreArchivo;
    }
    
    public String getHashArchivo() {
        return hashArchivo;
    }
    
    public void setHashArchivo(String hashArchivo) {
        this.hashArchivo = hashArchivo;
    }
    
    public String getRutaRelativaArchivo() {
        return rutaRelativaArchivo;
    }
    
    public void setRutaRelativaArchivo(String rutaRelativaArchivo) {
        this.rutaRelativaArchivo = rutaRelativaArchivo;
    }
    
    public String getDescripcion() {
        return descripcion;
    }
    
    public void setDescripcion(String descripcion) {
        this.descripcion = descripcion;
    }
    
    public String getTipoFirma() {
        return tipoFirma;
    }
    
    public void setTipoFirma(String tipoFirma) {
        this.tipoFirma = tipoFirma;
    }
    
    public Boolean getActiva() {
        return activa;
    }
    
    public void setActiva(Boolean activa) {
        this.activa = activa;
    }
    
    public Long getIdUsuarioCreador() {
        return idUsuarioCreador;
    }
    
    public void setIdUsuarioCreador(Long idUsuarioCreador) {
        this.idUsuarioCreador = idUsuarioCreador;
    }
    
    public String getNombrePlantilla() {
        return nombrePlantilla;
    }
    
    public void setNombrePlantilla(String nombrePlantilla) {
        this.nombrePlantilla = nombrePlantilla;
    }
    
    public String getTipoDocumento() {
        return tipoDocumento;
    }
    
    public void setTipoDocumento(String tipoDocumento) {
        this.tipoDocumento = tipoDocumento;
    }
    
    // Métodos adicionales útiles
    @Override
    public String toString() {
        return "PlantillaDocumento{" +
                "idPlantilla=" + idPlantilla +
                ", nombreArchivo='" + nombreArchivo + '\'' +
                ", nombrePlantilla='" + nombrePlantilla + '\'' +
                ", descripcion='" + descripcion + '\'' +
                ", tipoDocumento='" + tipoDocumento + '\'' +
                ", activa=" + activa +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        PlantillaDocumento that = (PlantillaDocumento) o;
        
        return idPlantilla != null ? idPlantilla.equals(that.idPlantilla) : that.idPlantilla == null;
    }
    
    @Override
    public int hashCode() {
        return idPlantilla != null ? idPlantilla.hashCode() : 0;
    }
}